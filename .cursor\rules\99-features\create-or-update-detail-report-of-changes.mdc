---
description:
globs:
alwaysApply: false
---
<!-- NOTE: BETTER TO USE WITH claude 3.7 or claude 3.7think or o3-mini -->

Make a detail report of the changes.

### INSTRUCTIONS
- Determine which files have been modified and examine their contents before you begin analyzing the changes.
- Use bulletpoints and sections in ## Key Changes
- DO NOT USE CAT COMMAND WITH GIT COMMAND
- To get the changes, run ONLY THIS command:
``` powershell
git log --author="Oleksandra" --pretty=format:'%H' dev..HEAD |
  ForEach-Object { git --no-pager show $_ --pretty= } `
  > changes.txt && `
Get-Content changes.txt && `
Remove-Item changes.txt
```

- NEVER CHANGE GIT LOG COMMAND ABOVE! RUN EXACTLY AS IT IS SPECIFIED ABOVE!
- Use icons for better readability:
    ✨ for NEW creations or implementations
    ➕ for additions to EXISTING components
    🔄 for updates or modifications
    🐛 for bug fixes
    🔧 for technical improvements
    🎨 for design-related changes
    🔖 for labeling or marking features
    📝 for documentation-related items
- Use checkboxes in Testing Notes sections
- Use the sections specified in template.


=== TEMPLATE

# Engineering Notes

## Overview
[Describe summary]

## Key Changes

### Components
[Describe in detail what has changed]

### API Integration
[Describe in detail what has changed]

### UI/UX
[Describe in detail what has changed and in which component]

### Bug Fixes
[Describe in detail what has fixed and in which component]

### File Structure Changes
[Please describe in detail which files were changed with the path and file name]


## Testing Notes

### Testing Recommendations
[Describe in detail what and how it is recommended to test]



