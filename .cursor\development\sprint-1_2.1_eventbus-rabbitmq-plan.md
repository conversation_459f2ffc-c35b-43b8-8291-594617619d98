# Sprint 1: EventBus Abstraction & RabbitMQ Implementation Plan

**Objective:** Deliver the EventBus abstraction (`@pulsepanel/events`) with a RabbitMQ adapter, a `noop` adapter for testing, and a local RabbitMQ development stack, fulfilling the requirements for Days 1-3 of Sprint 1.

---

## 1. Package Setup: `@pulsepanel/events`

- **Action:** Create a new package.
  - Directory: `packages/events`
- **Action:** Initialize `package.json`.
  - `name`: `@pulsepanel/events`
  - `private`: `true`
  - `main`: `src/index.ts` (or `dist/index.js` after build)
  - `types`: `dist/index.d.ts`
- **Action:** Add dependencies.
  - `amqplib`: For `rabbitBus` implementation.
  - `ulid`: For generating `EventEnvelope.id`.
- **Action:** Add dev dependencies.
  - `typescript`
  - `@types/node`
  - `@types/amqplib`
  - `@pulsepanel/eslint-config-custom` (and ensure root `eslint.config.js` or similar is set up)
  - `@pulsepanel/tsconfig-custom`
  - Testing framework (e.g., `jest`, `@types/jest`, `ts-jest`)
- **Action:** Configure `tsconfig.json`.
  - Extend `@pulsepanel/tsconfig-custom/node.json`.
  - Set `outDir` to `dist`, `rootDir` to `src`.
- **Action:** Setup ESLint configuration for the package.
- **Action:** Add scripts to `packages/events/package.json`:
  - `"build": "tsc -p tsconfig.json"`
  - `"lint": "eslint src --ext .ts"`
  - `"test": "jest"` (or other test command)
  - `"clean": "rm -rf dist"`
- **Action:** Update root `turbo.json` to include `@pulsepanel/events` in the build, lint, and test pipelines.

## 2. Interface Design (`packages/events/src/index.ts`)

- **Action:** Define and export core interfaces and types.

  - `EventEnvelope<T>`:

        ```typescript
        export interface EventEnvelope<T = unknown> {
          id: string;          // ULID, e.g., generated by ulid()
          type: string;        // Event type, e.g., "shop.order.created.v1"
          timestamp: string;   // ISO-8601 string
          source?: string;      // Optional: service that originated the event, e.g., "shopify-collector"
          payload: T;
        }
        ```

  - `Unsubscribe`:

        ```typescript
        export type Unsubscribe = () => Promise<void>;
        ```

  - `EventBus`:

        ```typescript
        export interface EventBus {
          publish<E>(topic: string, message: EventEnvelope<E>): Promise<void>;
          subscribe<E>(
            topic: string,
            handler: (message: EventEnvelope<E>) => Promise<void>,
            options?: { consumerTag?: string; concurrency?: number }
          ): Promise<Unsubscribe>;
          disconnect(): Promise<void>; // For graceful shutdown
        }
        ```

- **Acceptance Criteria:** `packages/events/src/index.ts` is created, fully typed, and exports these core elements.

## 3. `noopBus` Implementation (`packages/events/src/noopBus.ts`)

- **Objective:** Create a no-operation `EventBus` for local unit tests and environments where a message broker is not needed.
- **Action:** Implement the `EventBus` interface.
  - `publish`: Logs to console (optional) and returns `Promise.resolve()`.
  - `subscribe`: Logs to console (optional) and returns `Promise.resolve(() => Promise.resolve())`.
  - `disconnect`: Returns `Promise.resolve()`.
- **Action:** Create a factory function.

  ```typescript
  // packages/events/src/noopBus.ts
  import { EventBus, EventEnvelope, Unsubscribe } from "./index";

  export function createNoopBus(): EventBus {
    console.log("[NoopEventBus] Initialized.");
    return {
      publish: async <E>(
        topic: string,
        message: EventEnvelope<E>,
      ): Promise<void> => {
        console.log(`[NoopEventBus] PUBLISH to ${topic}:`, message);
      },
      subscribe: async <E>(
        topic: string,
        handler: (message: EventEnvelope<E>) => Promise<void>,
        options?: { consumerTag?: string; concurrency?: number },
      ): Promise<Unsubscribe> => {
        console.log(
          `[NoopEventBus] SUBSCRIBED to ${topic} with options:`,
          options,
        );
        return async () => {
          console.log(`[NoopEventBus] UNSUBSCRIBED from ${topic}`);
        };
      },
      disconnect: async (): Promise<void> => {
        console.log("[NoopEventBus] Disconnected.");
      },
    };
  }
  ```

- **Acceptance Criteria:** No runtime dependency on RabbitMQ or `amqplib`.

## 4. `rabbitBus` Implementation (`packages/events/src/rabbitBus.ts`)

- **Objective:** Implement the `EventBus` interface using `amqplib` to connect to RabbitMQ.
- **Action:** Implement the `EventBus` interface.
  - Factory function: `createRabbitBus(connectionUrl: string, exchangeName: string = 'pulse.topic'): EventBus`.
  - Internal `connect()` method to establish/reuse connection and channel.
  - `publish`:
    - Assert topic exchange (`exchangeName`, type `topic`).
    - Publish message with `routingKey = topic`.
    - Buffer messages if not connected, or handle connection errors.
  - `subscribe`:
    - Assert topic exchange.
    - Declare a queue (exclusive for simple cases, or named if `consumerTag` is used for potentially shared/durable queues).
    - Bind queue to the exchange with `routingKey = topic`.
    - Use `channel.consume()` to receive messages.
    - Call the handler. Acknowledge message (`channel.ack(msg)`) upon successful handling. Handle errors (`channel.nack(msg)`).
    - The returned `Unsubscribe` function should use `channel.cancel(consumerTagFromConsume)` and potentially clean up (e.g., unbind, delete queue if exclusive and temporary).
  - `disconnect`: Close channel and connection gracefully.
- **Action:** Export `createRabbitBus` factory.
- **Acceptance Criteria:** Unit test successfully publishes a message and a subscriber consumes it locally via RabbitMQ. The exchange used is `pulse.topic`.

## 5. Unified EventBus Factory (`packages/events/src/index.ts`)

- **Objective:** Provide a single factory function to create an `EventBus` instance based on configuration.
- **Action:** Define configuration types and the factory.

  ```typescript
  // packages/events/src/index.ts (add to existing exports)
  // ... other imports and interface definitions ...
  import { createNoopBus } from "./noopBus";
  import { createRabbitBus } from "./rabbitBus"; // Ensure this is created

  export type EventBusConfig =
    | { type: "noop" }
    | { type: "rabbitmq"; rabbitUrl: string; defaultExchange?: string };

  export function createEventBus(config: EventBusConfig): EventBus {
    if (config.type === "rabbitmq" && config.rabbitUrl) {
      return createRabbitBus(
        config.rabbitUrl,
        config.defaultExchange || "pulse.topic",
      );
    }
    return createNoopBus();
  }
  ```

## 6. Dependency Injection Strategy

- **Objective:** Define how `EventBus` will be provided to services.
- **NestJS Provider (for microservices):**

  - **Action:** Define a standard provider pattern.

        ```typescript
        // Example for a NestJS module
        // import { Provider } from '@nestjs/common';
        // import { ConfigService } from '@nestjs/config'; // Optional: if using NestJS ConfigModule
        // import { EventBus, createEventBus, EventBusConfig } from '@pulsepanel/events';

        // export const EVENT_BUS_PROVIDER_TOKEN = 'EVENT_BUS';

        // export const eventBusProvider: Provider = {
        //   provide: EVENT_BUS_PROVIDER_TOKEN,
        //   useFactory: (configService?: ConfigService) => { // configService is optional
        //     const rabbitUrl = process.env.RABBITMQ_URL || configService?.get<string>('RABBITMQ_URL');
        //     const nodeEnv = process.env.NODE_ENV || configService?.get<string>('NODE_ENV');

        //     const busConfig: EventBusConfig =
        //       (nodeEnv === 'test' || !rabbitUrl)
        //       ? { type: 'noop' }
        //       : { type: 'rabbitmq', rabbitUrl: rabbitUrl, defaultExchange: 'pulse.topic' };

        //     const bus = createEventBus(busConfig);
        //     // Consider adding bus.connect() here if your rabbitBus needs explicit connection
        //     // and handle graceful shutdown via NestJS lifecycle hooks (OnModuleDestroy)
        //     // bus.disconnect()
        //     return bus;
        //   },
        //   inject: [ConfigService], // Remove if ConfigService is not used
        // };
        ```

- **Next.js Singleton (for BFF or other Node.js apps):**

  - **Action:** Implement a singleton accessor.

        ```typescript
        // Example: apps/bff/src/lib/eventBusInstance.ts
        // import { EventBus, createEventBus, EventBusConfig } from '@pulsepanel/events';

        // let eventBus: EventBus;

        // export function getEventBusInstance(): EventBus {
        //   if (!eventBus) {
        //     const rabbitUrl = process.env.RABBITMQ_URL;
        //     const nodeEnv = process.env.NODE_ENV;
        //     const busConfig: EventBusConfig =
        //       (nodeEnv === 'test' || !rabbitUrl)
        //       ? { type: 'noop' }
        //       : { type: 'rabbitmq', rabbitUrl: rabbitUrl, defaultExchange: 'pulse.topic' };
        //     eventBus = createEventBus(busConfig);
        //     // Add connect/disconnect logic if needed, e.g., on app startup/shutdown
        //   }
        //   return eventBus;
        // }
        ```

- **Acceptance Criteria:** All application imports for EventBus compile. Configuration allows switching between `noopBus` and `rabbitBus`.

## 7. Dev Tooling: Local RabbitMQ Stack

- **Objective:** Enable local RabbitMQ development via Docker Compose.
- **Action:** Update `docker-compose.dev.yml` in the project root.

  ```yaml
  # docker-compose.dev.yml
  services:
    # ... other services (postgres, redis etc.)

    rabbitmq:
      image: rabbitmq:3.13-management-alpine # Use a recent version
      container_name: pulsepanel_rabbitmq_dev
      hostname: rabbitmq # Allows connection using amqp://user:pass@rabbitmq:5672 from other containers
      ports:
        - "5672:5672" # AMQP port for application connection
        - "15672:15672" # Management UI (guest/guest by default if not overridden)
      volumes:
        - rabbitmq_dev_data:/var/lib/rabbitmq/
      environment:
        # For local dev, default guest/guest is often fine, or set explicit user/pass:
        # RABBITMQ_DEFAULT_USER: your_user
        # RABBITMQ_DEFAULT_PASS: your_password
        # These would then be used in RABBITMQ_URL
      networks:
        - pulsepanel_network # Assuming a common network 'pulsepanel_network'

  volumes:
    # ... other volumes
    rabbitmq_dev_data:
  # networks:
  #   pulsepanel_network:
  #     driver: bridge
  ```

- **Action:** Add `RABBITMQ_URL="amqp://guest:guest@localhost:5672"` (or with custom user/pass if set) to relevant local `.env` files (e.g., for BFF, `metrics-etl`).
- **Action:** Add script to root `package.json` as specified in Sprint 1.
  - `"events:up": "docker compose -f docker-compose.dev.yml up -d rabbitmq"`
  - (Alternatively, confirm that the existing `services:up` script from Sprint 0 correctly starts RabbitMQ if it's included in the default `docker compose up` services).
- **Acceptance Criteria:** Documentation for spinning up RabbitMQ is present in `packages/events/README.md`.

## 8. Testing

- **`@pulsepanel/events` package:**
  - **Action:** Write unit tests for `noopBus`.
    - Verify `publish` and `subscribe` calls are logged or handled as expected without errors.
  - **Action:** Write unit/integration tests for `rabbitBus`.
    - These tests will require a running RabbitMQ instance.
    - Test: successful `publish` and `subscribe` flow for a message.
    - Test: `unsubscribe` functionality.
    - Test: connection and disconnection.
- **Application Level:**
  - Ensure services using `EventBus` can be tested with `noopBus` to isolate logic from broker interaction.

## 9. Documentation

- **Action:** Create `packages/events/README.md`.
- **Contents:**
  - Purpose of the `@pulsepanel/events` package.
  - How to use `EventBus` (example of `publish` and `subscribe`).
  - Configuration:
    - Environment variable for `RABBITMQ_URL`.
    - How to choose between `noop` and `rabbitmq` (via `createEventBus` config).
  - How to run RabbitMQ locally (e.g., `pnpm services:up` or `pnpm events:up`).
  - Link to RabbitMQ management UI (`http://localhost:15672`).
- **Acceptance Criteria:** README is created with essential information for developers.

---

This plan outlines the necessary steps to implement the EventBus abstraction and integrate RabbitMQ for local development as per Sprint 1.
