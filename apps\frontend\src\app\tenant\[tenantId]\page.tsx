"use client";

import { useParams } from "next/navigation";

export default function TenantDashboard() {
  const params = useParams();
  const tenantId = params.tenantId as string;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-blue-100 flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <h1 className="text-3xl font-bold text-gray-900">
                Tenant Dashboard
              </h1>
              <p className="text-gray-600">
                Tenant:{" "}
                <span className="font-mono bg-gray-200 px-2 py-1 rounded">
                  {tenantId}
                </span>
              </p>
            </div>
          </div>
        </div>

        {/* Tenant Isolation Success Alert */}
        <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-green-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-green-800">
                🏢 Tenant Isolation Working!
              </h3>
              <p className="text-green-700 mt-1">
                You have successfully accessed tenant:{" "}
                <strong>{tenantId}</strong>. Dynamic route protection with
                tenant isolation is functioning correctly.
              </p>
            </div>
          </div>
        </div>

        {/* Tenant Info Card */}
        <div className="bg-white rounded-lg shadow border border-gray-200 p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            🔍 Tenant Access Analysis
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Tenant Details */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Tenant Information
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded">
                  <span className="font-medium">Tenant ID</span>
                  <span className="font-mono text-blue-600">{tenantId}</span>
                </div>
                <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded">
                  <span className="font-medium">Route Pattern</span>
                  <span className="font-mono text-blue-600">
                    /tenant/[tenantId]
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                  <span className="font-medium">Access Status</span>
                  <span className="text-green-600 font-semibold">
                    ✓ Authorized
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                  <span className="font-medium">Tenant Isolation</span>
                  <span className="text-green-600 font-semibold">✓ Active</span>
                </div>
              </div>
            </div>

            {/* Access Logic */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Access Control Logic
              </h3>
              <div className="space-y-3 text-sm">
                <div className="p-3 bg-gray-50 border rounded">
                  <strong className="text-gray-900">
                    1. Authentication Check
                  </strong>
                  <p className="text-gray-600 mt-1">
                    User must be authenticated via SuperTokens
                  </p>
                </div>
                <div className="p-3 bg-gray-50 border rounded">
                  <strong className="text-gray-900">
                    2. Tenant Validation
                  </strong>
                  <p className="text-gray-600 mt-1">
                    Middleware extracts tenant ID from URL path
                  </p>
                </div>
                <div className="p-3 bg-gray-50 border rounded">
                  <strong className="text-gray-900">
                    3. Access Authorization
                  </strong>
                  <p className="text-gray-600 mt-1">
                    User tenant matches route tenant OR user has admin role
                  </p>
                </div>
                <div className="p-3 bg-green-50 border border-green-200 rounded">
                  <strong className="text-green-800">✓ Access Granted</strong>
                  <p className="text-green-700 mt-1">
                    All checks passed successfully
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Test Navigation */}
        <div className="bg-white rounded-lg shadow border border-gray-200 p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            🧪 Test Tenant Navigation
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Current Tenant Routes */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Current Tenant Routes
              </h3>
              <div className="space-y-3">
                <button
                  onClick={() =>
                    window.open(`/tenant/${tenantId}/settings`, "_blank")
                  }
                  className="w-full text-left p-3 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Tenant Settings</span>
                    <span className="text-blue-600">→ Test Access</span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">
                    Manager+ access required
                  </p>
                </button>

                <button
                  onClick={() =>
                    window.open(`/tenant/${tenantId}/users`, "_blank")
                  }
                  className="w-full text-left p-3 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Tenant Users</span>
                    <span className="text-blue-600">→ Test Access</span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">
                    Manager+ with read:users permission
                  </p>
                </button>
              </div>
            </div>

            {/* Cross-Tenant Test */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Cross-Tenant Access Test
              </h3>
              <div className="space-y-3">
                <button
                  onClick={() => window.open("/tenant/other-tenant", "_blank")}
                  className="w-full text-left p-3 bg-yellow-50 border border-yellow-200 rounded hover:bg-yellow-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Different Tenant</span>
                    <span className="text-yellow-600">⚠️ Test Isolation</span>
                  </div>
                  <p className="text-sm text-yellow-700 mt-1">
                    Should be blocked unless you're admin
                  </p>
                </button>

                <button
                  onClick={() =>
                    window.open("/tenant/blocked-tenant", "_blank")
                  }
                  className="w-full text-left p-3 bg-red-50 border border-red-200 rounded hover:bg-red-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Unauthorized Tenant</span>
                    <span className="text-red-600">🚫 Should Block</span>
                  </div>
                  <p className="text-sm text-red-700 mt-1">
                    Should redirect to unauthorized page
                  </p>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Security Features */}
        <div className="bg-white rounded-lg shadow border border-gray-200 p-6 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            🛡️ Security Features Active
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded">
              <svg
                className="h-6 w-6 text-green-600 mr-3"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <div>
                <div className="font-medium text-green-800">
                  Tenant Isolation
                </div>
                <div className="text-sm text-green-700">
                  Cross-tenant protection
                </div>
              </div>
            </div>

            <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded">
              <svg
                className="h-6 w-6 text-green-600 mr-3"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <div>
                <div className="font-medium text-green-800">Dynamic Routes</div>
                <div className="text-sm text-green-700">
                  URL parameter validation
                </div>
              </div>
            </div>

            <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded">
              <svg
                className="h-6 w-6 text-green-600 mr-3"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
              <div>
                <div className="font-medium text-green-800">Role Hierarchy</div>
                <div className="text-sm text-green-700">
                  Admin override access
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Dashboard */}
        <div className="text-center">
          <button
            onClick={() => (window.location.href = "/dashboard")}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            ← Back to Main Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}
