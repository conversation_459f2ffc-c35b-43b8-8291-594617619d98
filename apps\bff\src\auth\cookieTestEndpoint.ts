/**
 * <PERSON>ie Configuration Test Endpoint
 *
 * Development-only endpoint for testing and debugging cookie configuration
 */

import type { Request, Response } from "express";
import {
  getCookieDebugInfo,
  validateCookieConfig,
  getSessionDuration,
  buildLogoutCookieHeaders,
} from "./cookieConfig.js";

/**
 * Cookie configuration debug endpoint
 * GET /auth/cookie-debug
 */
export async function cookieDebugHandler(req: Request, res: Response) {
  if (process.env.NODE_ENV === "production") {
    return res.status(404).json({ error: "Not found" });
  }

  try {
    const rememberMe = req.query.rememberMe === "true";

    const debugInfo = {
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV,
      cookieConfig: getCookieDebugInfo(rememberMe),
      validation: validateCookieConfig(),
      sessionDurations: {
        normal: getSessionDuration(false),
        rememberMe: getSessionDuration(true),
      },
      testHeaders: {
        logout: buildLogoutCookieHeaders(),
      },
      environmentVariables: {
        COOKIE_DOMAIN: process.env.COOKIE_DOMAIN || "not set",
        FRONTEND_URL: process.env.FRONTEND_URL || "not set",
        BFF_URL: process.env.BFF_URL || "not set",
        NODE_ENV: process.env.NODE_ENV,
      },
    };

    res.json(debugInfo);
  } catch (error) {
    res.status(500).json({
      error: "Debug endpoint failed",
      message: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Cookie configuration validation endpoint
 * GET /auth/cookie-validate
 */
export async function cookieValidateHandler(req: Request, res: Response) {
  try {
    const validation = validateCookieConfig();

    if (validation.valid) {
      res.json({
        status: "valid",
        message: "Cookie configuration is valid",
        environment: process.env.NODE_ENV,
      });
    } else {
      res.status(400).json({
        status: "invalid",
        errors: validation.errors,
        environment: process.env.NODE_ENV,
      });
    }
  } catch (error) {
    res.status(500).json({
      status: "error",
      message: error instanceof Error ? error.message : String(error),
    });
  }
}

/**
 * Register cookie test endpoints
 */
export function registerCookieTestEndpoints(app: any) {
  if (process.env.NODE_ENV !== "production") {
    app.get("/auth/cookie-debug", cookieDebugHandler);
    app.get("/auth/cookie-validate", cookieValidateHandler);

    console.log("🍪 Cookie debug endpoints registered:");
    console.log("  - GET /auth/cookie-debug");
    console.log("  - GET /auth/cookie-validate");
  }
}
