# Health Checks Best Practices

## Overview

All services in PulsePanel have comprehensive health checks configured for reliable orchestration and monitoring.

## Current Health Checks

### ✅ PostgreSQL

```yaml
healthcheck:
  test: ["CMD-SHELL", "pg_isready -U pulsepanel_user -d pulsepanel_dev_db"]
  interval: 10s
  timeout: 5s
  retries: 5
  start_period: 10s
```

### ✅ Redis

```yaml
healthcheck:
  test: ["CMD", "redis-cli", "ping"]
  interval: 10s
  timeout: 5s
  retries: 5
  start_period: 5s
```

### ✅ RabbitMQ

```yaml
healthcheck:
  test: ["CMD", "rabbitmq-diagnostics", "check_port_connectivity"]
  interval: 10s
  timeout: 5s
  retries: 5
  start_period: 15s
```

### ✅ SuperTokens Core

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:3567/hello"]
  interval: 10s
  timeout: 5s
  retries: 5
  start_period: 30s
```

### ✅ Auth Service

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
  interval: 10s
  timeout: 5s
  retries: 5
  start_period: 10s
```

## Health Check Templates

### For Node.js/Express Services

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:PORT/health"]
  interval: 10s
  timeout: 5s
  retries: 5
  start_period: 10s
```

**Requirements:**

- Install curl in Dockerfile: `RUN apk add --no-cache curl` (Alpine) or `RUN apt-get install -y curl` (Debian)
- Implement `/health` endpoint returning JSON

### For Next.js Frontend

```yaml
healthcheck:
  test: ["CMD", "curl", "-f", "http://localhost:3000/_next/static/health"]
  interval: 15s
  timeout: 5s
  retries: 3
  start_period: 20s
```

### For GraphQL APIs

```yaml
healthcheck:
  test:
    [
      "CMD",
      "curl",
      "-f",
      "-H",
      "Content-Type: application/json",
      "-d",
      '{"query":"{ __typename }"}',
      "http://localhost:4000/graphql",
    ]
  interval: 10s
  timeout: 5s
  retries: 5
  start_period: 15s
```

## Parameters Explained

- **`test`**: Command to run for health check
- **`interval`**: How often to run the check
- **`timeout`**: Maximum time for single check
- **`retries`**: Number of consecutive failures before marking unhealthy
- **`start_period`**: Grace period during container startup

## Best Practices

### 1. Start Period Guidelines

- **Database services**: 10-15s
- **Message queues**: 15-20s
- **Application services**: 10-15s
- **Heavy services (SuperTokens)**: 30s+

### 2. Health Check Endpoints

Always implement dedicated health endpoints:

```typescript
// Express example
app.get("/health", (req, res) => {
  res.json({
    status: "ok",
    service: "service-name",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
  });
});
```

### 3. Dependencies Chain

```yaml
depends_on:
  database:
    condition: service_healthy
  cache:
    condition: service_healthy
```

### 4. Monitoring Integration

Health checks integrate with:

- Docker Compose orchestration
- Docker Swarm
- Kubernetes readiness/liveness probes
- External monitoring (Prometheus, etc.)

## Troubleshooting

### Common Issues

1. **Missing utilities in container**

   ```dockerfile
   RUN apk add --no-cache curl  # Alpine
   RUN apt-get install -y curl # Debian
   ```

2. **Wrong port or path**

   - Verify service actually listens on expected port
   - Check internal container networking

3. **Too aggressive timeouts**
   - Increase `start_period` for slow-starting services
   - Adjust `retries` for flaky services

### Debugging Health Checks

```bash
# Check health status
docker-compose ps

# Run health check manually
docker exec container-name curl -f http://localhost:PORT/health

# View health check logs
docker inspect container-name | grep Health -A 20
```

## Future Enhancements

### Planned Health Checks

- **BFF Service**: GraphQL health endpoint
- **Frontend**: Static asset health check
- **Monitoring**: Prometheus metrics endpoint
- **Database migrations**: Schema validation check
