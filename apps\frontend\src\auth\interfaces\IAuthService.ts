/**
 * Transport-agnostic auth service interface
 *
 * This interface abstracts all auth operations from the transport layer.
 * UI components will only interact with this interface, making it easy
 * to switch between different auth providers (SuperTokens → KeyCloak → Auth0)
 * without changing any UI code.
 */

export interface DeviceInfo {
  deviceType?: string;
  userAgent?: string;
  location?: string;
  ipAddress?: string;
}

export interface SecurityMetadata {
  lastLoginAt?: string;
  loginAttempts: number;
  riskScore: number;
  suspiciousActivity: boolean;
}

export interface SessionInfo {
  userId: string;
  sessionHandle: string;
  tenantId?: string;
  roles: string[];
  issuedAt: string;
  expiresAt: string;
  deviceInfo?: DeviceInfo;
  securityMetadata?: SecurityMetadata;
}

export interface User {
  id: string;
  name?: string;
  tenantId: string;
  createdAt: string;
  updatedAt: string;
}

export interface AuthError {
  code: string;
  message: string;
  field?: string;
}

export interface AuthResult {
  success: boolean;
  user?: User;
  sessionInfo?: SessionInfo;
  error?: AuthError;
  sessionDuration?: number; // TTL in seconds
}

export interface SignInInput {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface SignUpInput {
  email: string;
  password: string;
  name?: string;
  tenantId: string;
}

/**
 * Core Auth Service Interface
 *
 * Implementations can use any transport layer:
 * - GraphQLAuthService (Apollo Client)
 * - RestAuthService (fetch API)
 * - KeycloakAuthService (Keycloak SDK)
 */
export interface IAuthService {
  signIn(input: SignInInput): Promise<AuthResult>;

  signUp(input: SignUpInput): Promise<AuthResult>;

  signOut(): Promise<boolean>;

  refreshToken(): Promise<AuthResult>;

  getCurrentUser(): Promise<User | null>;

  getSessionInfo(): Promise<SessionInfo | null>;

  isAuthenticated(): Promise<boolean>;

  hasRole(role: string): Promise<boolean>;

  getUserRoles(): Promise<string[]>;
}

/**
 * Auth Service Factory Interface
 * Allows dependency injection and easy switching between implementations
 */
export interface IAuthServiceFactory {
  createAuthService(): IAuthService;
}

/**
 * Auth Configuration Interface
 * Environment-specific configuration for auth service
 */
export interface AuthConfig {
  apiUrl: string;
  graphqlEndpoint?: string;
  restEndpoint?: string;
  enableCaching?: boolean;
  sessionRefreshInterval?: number;
  rememberMeDefault?: boolean;
  debugMode?: boolean;
}
