import UserRoles from "supertokens-node/recipe/userroles/index.js";

/**
 * Role and Permission System for PulsePanel
 *
 * Roles:
 * - admin: Full system access, can manage users, roles, and all tenant data
 * - manager: Can view and manage users in their tenant, limited admin access
 * - user: Standard user access, can only access their own data
 */

export const ROLES = {
  ADMIN: "admin",
  MANAGER: "manager",
  USER: "user",
} as const;

export const PERMISSIONS = {
  // User management
  READ_USERS: "read:users",
  WRITE_USERS: "write:users",
  DELETE_USERS: "delete:users",

  // Role management
  MANAGE_ROLES: "manage:roles",

  // Data access
  READ_ALL_DATA: "read:all-data",
  WRITE_ALL_DATA: "write:all-data",
  DELETE_ALL_DATA: "delete:all-data",

  // Tenant management
  MANAGE_TENANTS: "manage:tenants",

  // System administration
  SYSTEM_ADMIN: "system:admin",
} as const;

/**
 * Role-Permission Mapping
 */
const ROLE_PERMISSIONS: Record<string, string[]> = {
  [ROLES.ADMIN]: [
    PERMISSIONS.READ_USERS,
    PERMISSIONS.WRITE_USERS,
    PERMISSIONS.DELETE_USERS,
    PERMISSIONS.MANAGE_ROLES,
    PERMISSIONS.READ_ALL_DATA,
    PERMISSIONS.WRITE_ALL_DATA,
    PERMISSIONS.DELETE_ALL_DATA,
    PERMISSIONS.MANAGE_TENANTS,
    PERMISSIONS.SYSTEM_ADMIN,
  ],
  [ROLES.MANAGER]: [
    PERMISSIONS.READ_USERS,
    PERMISSIONS.WRITE_USERS,
    PERMISSIONS.READ_ALL_DATA,
    PERMISSIONS.WRITE_ALL_DATA,
  ],
  [ROLES.USER]: [
    // Users only get implicit permissions for their own data
  ],
};

/**
 * Initialize default roles and permissions in SuperTokens
 */
export async function initializeRoles(): Promise<void> {
  try {
    console.log("🚀 Initializing SuperTokens roles and permissions...");

    // Create roles with their permissions
    for (const [roleName, permissions] of Object.entries(ROLE_PERMISSIONS)) {
      try {
        const response = await UserRoles.createNewRoleOrAddPermissions(
          roleName,
          permissions,
        );

        if (response.createdNewRole) {
          console.log(
            `✅ Created new role: ${roleName} with ${permissions.length} permissions`,
          );
        } else {
          console.log(
            `🔄 Updated existing role: ${roleName} with ${permissions.length} permissions`,
          );
        }
      } catch (error) {
        console.error(`❌ Failed to create/update role ${roleName}:`, error);
      }
    }

    // Verify roles were created
    const allRoles = await UserRoles.getAllRoles();
    console.log("📋 Available roles:", allRoles.roles);

    console.log("✅ SuperTokens roles initialization completed!");
  } catch (error) {
    console.error("❌ Failed to initialize SuperTokens roles:", error);
    throw error;
  }
}

/**
 * Check if a user has specific permission
 * @param userRoles - User's current roles
 * @param permission - Permission to check
 * @returns boolean
 */
export function hasPermission(
  userRoles: string[],
  permission: string,
): boolean {
  for (const role of userRoles) {
    const rolePermissions = ROLE_PERMISSIONS[role];
    if (rolePermissions && rolePermissions.includes(permission)) {
      return true;
    }
  }
  return false;
}

/**
 * Check if a user has any of the specified roles
 * @param userRoles - User's current roles
 * @param requiredRoles - Roles to check against
 * @returns boolean
 */
export function hasRole(userRoles: string[], requiredRoles: string[]): boolean {
  return userRoles.some((role) => requiredRoles.includes(role));
}

/**
 * Get all permissions for given roles
 * @param roles - User roles
 * @returns Array of permissions
 */
export function getPermissionsForRoles(roles: string[]): string[] {
  const permissions = new Set<string>();

  for (const role of roles) {
    const rolePermissions = ROLE_PERMISSIONS[role];
    if (rolePermissions) {
      rolePermissions.forEach((permission) => permissions.add(permission));
    }
  }

  return Array.from(permissions);
}
