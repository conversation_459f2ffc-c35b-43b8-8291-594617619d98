# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

**Essential Commands:**

```bash
pnpm dev              # Start all apps in development mode
pnpm build            # Build all packages with dependency resolution
pnpm test             # Run all tests across the monorepo
pnpm test:watch       # Run tests in watch mode
pnpm lint             # Lint entire codebase
pnpm codegen          # Generate GraphQL types and Prisma client
```

**Service Management:**

```bash
pnpm services:up      # Start PostgreSQL, Redis, RabbitMQ, SuperTokens Core
pnpm services:down    # Stop all services
pnpm auth:setup       # Initialize auth database schema
```

**Individual Package Commands:**

- Frontend: `pnpm --filter @pulsepanel/frontend dev` (Next.js with Turbopack)
- BFF: `pnpm --filter @pulsepanel/bff dev` (Express + Apollo Server)
- Auth API: `pnpm --filter @pulsepanel/auth-api dev` (SuperTokens integration)

## Architecture Overview

**Monorepo Structure:**

- **pnpm workspaces** with **Turbo** for task orchestration
- **Backend for Frontend (BFF)** pattern with clear separation of concerns
- TypeScript 5.4.5 unified across all packages

**Core Applications:**

- `apps/frontend`: Next.js 15 + React 19 + Apollo Client
- `apps/bff`: Express + Apollo Server + TypeGraphQL + Prisma integration
- `apps/auth-api`: SuperTokens authentication service
- `apps/supertokens-core`: Dockerized SuperTokens Core

**Shared Packages:**

- `packages/db`: Prisma ORM with typegraphql-prisma integration
- `packages/events`: RabbitMQ event system with amqplib
- `packages/core-sdk`: Shared types and utilities

## Authentication & Security Architecture

**SuperTokens Integration:**

- Auth API runs on port 3001, SuperTokens Core on port 3567
- Redis-backed session management with caching
- Built-in multi-tenancy support

**RBAC System:**

- GraphQL directives: `@authenticated`, `@hasRole`, `@belongsToTenant`, `@rateLimit`
- Default "user" role with automatic assignment
- Admin-only role management operations
- Rate limiting and audit logging built-in

## GraphQL & Type Generation

**Code Generation Flow:**

1. `pnpm codegen` generates types from GraphQL schemas
2. Prisma generates database client types
3. typegraphql-prisma bridges database and GraphQL types

**Key GraphQL Operations:**

- Authentication: signUp, signIn, signOut, password management
- RBAC: role assignment/removal, user role queries
- All mutations protected by security directives

## Testing & Code Quality

**Test Commands:**

```bash
pnpm test                    # All tests
pnpm --filter bff test       # BFF tests only
pnpm --filter events test    # Events package tests
```

**Code Quality:**

- ESLint 9.0 with unified TypeScript configuration
- Prettier 3.5 for formatting
- Husky + lint-staged for pre-commit hooks
- Vitest for testing with coverage support

## Development Services

**Required Services (Docker Compose):**

- PostgreSQL 15 (port 5432) - Primary database
- Redis 7 (port 6379) - Session cache
- RabbitMQ 3.13 (ports 5672, 15672) - Message broker
- SuperTokens Core (port 3567) - Authentication service

All services include health checks for reliable development environment.

## Code Patterns & Standards

**Validation Over Modification:**

- Understand codebase thoroughly before making changes
- Only modify code directly related to assigned tasks
- Prioritize security: input validation, secure coding patterns
- Implement robust error handling for external systems

**Type Safety:**

- End-to-end TypeScript with generated types
- Shared type definitions in core-sdk package
- Database types flow through Prisma to GraphQL

**Event-Driven Architecture:**

- RabbitMQ integration for async processing
- Event package provides consistent messaging patterns
