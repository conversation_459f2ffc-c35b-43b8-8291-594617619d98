export interface SessionData {
  userId: string;
  tenantId: string;
  userDataInJWT: any;
  sessionHandle: string;
  roles?: string[];
  permissionsLastUpdated?: number;
}

export interface AuthContext {
  isAuthenticated: boolean;
  session?: SessionData | null;
  userId?: string;
  tenantId?: string;
  error?: any;
  fromCache?: boolean;
}

export interface CacheStats {
  totalSessions: number;
  hitRate: number;
  avgLatency: number;
}

// Extend Express Request to include auth context
declare global {
  namespace Express {
    interface Request {
      authContext: AuthContext;
    }
  }
}

// Advanced session invalidation types
export interface SessionMetadata {
  loginTime: number;
  lastActivity: number;
  ipAddress: string;
  userAgent: string;
  deviceType: "web" | "mobile" | "unknown";
  geoLocation?: {
    country: string;
    city?: string;
    coordinates?: [number, number];
  };
  loginAttempts: number;
  securityFlags: SecurityFlag[];
}

export interface SecurityFlag {
  type:
    | "ip_change"
    | "geo_change"
    | "device_change"
    | "concurrent_login"
    | "suspicious_activity";
  timestamp: number;
  severity: "low" | "medium" | "high" | "critical";
  details: Record<string, any>;
}

export interface InvalidationEvent {
  sessionId: string;
  userId: string;
  tenantId?: string;
  reason: InvalidationReason;
  timestamp: number;
  triggeredBy: "system" | "user" | "admin" | "security";
  details: Record<string, any>;
  ipAddress?: string;
}

export type InvalidationReason =
  | "logout"
  | "role_change"
  | "tenant_switch"
  | "security_violation"
  | "expired"
  | "concurrent_limit"
  | "device_change"
  | "admin_action"
  | "system_cleanup";

export interface SessionHealthStats {
  totalActiveSessions: number;
  expiredSessions: number;
  orphanedSessions: number;
  memoryUsageMB: number;
  cleanupEventsLast24h: number;
  securityEventsLast24h: number;
  averageSessionLifetime: number;
}

export interface SecurityDetectionResult {
  isViolation: boolean;
  riskLevel: "low" | "medium" | "high" | "critical";
  flags: SecurityFlag[];
  recommendedAction: "none" | "warn" | "logout" | "block";
}
