schema: './src/schema/schema.graphql'
generates:
  ./src/generated/graphql-types.ts:
    plugins:
      - typescript
      - typescript-resolvers
    config:
      contextType: '../types/context#Context'
      mappers:
        User: '@pulsepanel/db/generated/client#User as PrismaUser'
      scalars:
        ID: string
        String: string
        Boolean: boolean
        Int: number
        Float: number 