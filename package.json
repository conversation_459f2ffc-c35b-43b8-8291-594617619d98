{"type": "module", "devDependencies": {"@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "eslint": "^9.27.0", "eslint-config-prettier": "^10.1.5", "husky": "^9.1.7", "lint-staged": "^16.1.0", "prettier": "^3.5.3", "turbo": "^2.5.3"}, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "test": "turbo run test", "test:watch": "turbo run test --watch", "clean": "turbo run clean", "format": "prettier --write \"**/*.{ts,tsx,md,json}\"", "prepare": "husky", "services:up": "docker-compose -f docker-compose.dev.yml up -d", "services:down": "docker-compose -f docker-compose.dev.yml down", "services:logs": "docker-compose -f docker-compose.dev.yml logs -f", "events:up": "docker-compose -f docker-compose.dev.yml up -d rabbitmq", "codegen": "turbo run codegen", "auth:setup": "docker exec -i pulsepanel-postgres psql -U pulsepanel_user -d pulsepanel_dev_db < packages/db/scripts/create-auth-schema.sql"}, "lint-staged": {"*.{js,jsx,ts,tsx}": ["turbo lint -- --fix", "prettier --write", "pnpm --filter {.} test -- --bail --findRelatedTests"], "*.{json,md}": ["prettier --write"]}, "packageManager": "pnpm@10.10.0+sha512.d615db246fe70f25dcfea6d8d73dee782ce23e2245e3c4f6f888249fb568149318637dca73c2c5c8ef2a4ca0d5657fb9567188bfab47f566d1ee6ce987815c39"}