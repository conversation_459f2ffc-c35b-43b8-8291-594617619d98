import http from "http";

const BFF_URL = "http://localhost:4000/graphql";

// Helper to make GraphQL requests
async function makeGraphQLRequest(query, variables = {}, sessionCookies = "") {
  return new Promise((resolve, reject) => {
    const data = JSON.stringify({
      query,
      variables,
    });

    const options = {
      hostname: "localhost",
      port: 4000,
      path: "/graphql",
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        "Content-Length": Buffer.byteLength(data),
        Cookie: sessionCookies,
      },
    };

    const req = http.request(options, (res) => {
      let body = "";
      res.on("data", (chunk) => {
        body += chunk;
      });
      res.on("end", () => {
        try {
          const response = JSON.parse(body);
          resolve({
            status: res.statusCode,
            data: response,
            headers: res.headers,
          });
        } catch (error) {
          reject(error);
        }
      });
    });

    req.on("error", reject);
    req.write(data);
    req.end();
  });
}

// Test configuration
const tests = [
  {
    name: "Forgot Password - Valid Email",
    query: `
      mutation ForgotPassword($email: String!) {
        forgotPassword(email: $email)
      }
    `,
    variables: { email: "<EMAIL>" },
    check: (response) => {
      return response.data?.data?.forgotPassword === true;
    },
  },

  {
    name: "Forgot Password - Invalid Email",
    query: `
      mutation ForgotPassword($email: String!) {
        forgotPassword(email: $email)
      }
    `,
    variables: { email: "invalid-email" },
    check: (response) => {
      // Security-first: should return true even for invalid emails (don't reveal validation)
      return response.data?.data?.forgotPassword === true;
    },
  },

  {
    name: "Reset Password - Missing Token",
    query: `
      mutation ResetPassword($token: String!, $newPassword: String!) {
        resetPassword(token: $token, newPassword: $newPassword) {
          success
          error {
            code
            message
          }
        }
      }
    `,
    variables: { token: "", newPassword: "newpassword123" },
    check: (response) => {
      const result = response.data?.data?.resetPassword;
      return (
        result?.success === false && result?.error?.code === "INVALID_INPUT"
      );
    },
  },

  {
    name: "Reset Password - Weak Password",
    query: `
      mutation ResetPassword($token: String!, $newPassword: String!) {
        resetPassword(token: $token, newPassword: $newPassword) {
          success
          error {
            code
            message
          }
        }
      }
    `,
    variables: { token: "valid-token", newPassword: "123" },
    check: (response) => {
      const result = response.data?.data?.resetPassword;
      return (
        result?.success === false && result?.error?.code === "WEAK_PASSWORD"
      );
    },
  },

  {
    name: "Reset Password - Invalid Token",
    query: `
      mutation ResetPassword($token: String!, $newPassword: String!) {
        resetPassword(token: $token, newPassword: $newPassword) {
          success
          error {
            code
            message
          }
        }
      }
    `,
    variables: { token: "invalid-token-123", newPassword: "strongpassword123" },
    check: (response) => {
      const result = response.data?.data?.resetPassword;
      return (
        result?.success === false && result?.error?.code === "INVALID_TOKEN"
      );
    },
  },

  {
    name: "Change Password - Unauthenticated",
    query: `
      mutation ChangePassword($oldPassword: String!, $newPassword: String!) {
        changePassword(oldPassword: $oldPassword, newPassword: $newPassword)
      }
    `,
    variables: { oldPassword: "oldpassword123", newPassword: "newpassword123" },
    check: (response) => {
      return response.data?.errors?.[0]?.extensions?.code === "UNAUTHENTICATED";
    },
  },
];

// Run tests
async function runTests() {
  console.log("🚀 Starting Password Management API Tests...\n");

  let passed = 0;
  let failed = 0;
  const startTime = Date.now();

  for (const test of tests) {
    try {
      console.log(`Testing: ${test.name}`);

      const response = await makeGraphQLRequest(
        test.query,
        test.variables,
        test.sessionCookies || "",
      );

      const success = test.check(response);

      if (success) {
        console.log(`✅ PASSED: ${test.name}`);
        passed++;
      } else {
        console.log(`❌ FAILED: ${test.name}`);
        console.log(`   Response:`, JSON.stringify(response.data, null, 2));
        failed++;
      }
    } catch (error) {
      console.log(`❌ ERROR: ${test.name}`);
      console.log(`   Error:`, error.message);
      failed++;
    }

    console.log(""); // Empty line for readability
  }

  const totalTime = Date.now() - startTime;
  const successRate = ((passed / (passed + failed)) * 100).toFixed(1);

  console.log("📊 TEST SUMMARY");
  console.log("=".repeat(50));
  console.log(`✅ Passed: ${passed}`);
  console.log(`❌ Failed: ${failed}`);
  console.log(`📈 Success Rate: ${successRate}%`);
  console.log(`⏱️ Total Time: ${totalTime}ms`);
  console.log("=".repeat(50));

  if (failed === 0) {
    console.log(
      "🎉 ALL TESTS PASSED! Password management API is working correctly.",
    );
  } else {
    console.log("⚠️ Some tests failed. Check the output above for details.");
  }
}

// Execute tests
runTests().catch(console.error);
