## <PERSON><PERSON><PERSON> vs <PERSON><PERSON><PERSON> для PulsePanel

| Критерий                          | **Apache Kafka**                                                                                 | **RabbitMQ**                                                                                   | Что важно PulsePanel                                                                                           |
| --------------------------------- | ------------------------------------------------------------------------------------------------ | ---------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------- |
| **Модель**                        | Лог-журнал (append-only), потребители хранят смещение.                                           | Очередь сообщений с ack/nack, каждая копия хранится в брокере.                                 | Kafka подходит, если нужно реплеить историю и строить стрим-агрегации; Rabbit проще для «доставили — удалили». |
| **Скорость / скейл**              | 100 k+ msg/s на кластере; горизонтальный шард через partition.                                   | 20–30 k msg/s на ноду; масштаб через кластеры/шардинг, но сложнее.                             | При 10 k магазинов суммарный поток ≈ 2–5 k msg/s → обе системы справятся; Kafka оставляет запас.               |
| **Семантика доставки**            | «At-least once» по умолчанию; «exactly once» через idempotent producer + transactions (сложнее). | «At-least» или «at-most» в зависимости от ack; «exactly-once» не гарантируется (нужна de-dup). | Для KPI агрегатов потеря/дубликат пары событий некритична; обе подходят.                                       |
| **Отложенный ребилд KPI**         | Исторический лог хранится неделями → можно переиграть ETL с нуля.                                | Сообщения удаляются после ack или TTL; для ребилда нужен отдельный warehouse.                  | Kafka даёт бесплатный «replay» при миграции схемы.                                                             |
| **Многократные консьюмер-группы** | Лог → любой number групп, каждый читает независимо.                                              | Fan-out через exchange → дублирование сообщений.                                               | Если появится второе приложение (например alert-engine), Kafka экономнее.                                      |
| **Компактность JS-клиента**       | `kafkajs` ~1 MB, требует TCP-зондов.                                                             | `amqplib` ~200 kB, лёгкая.                                                                     | В collectors размер образа важен; RabbitMQ клиент легче.                                                       |
| **Edge-friendly**                 | Требует постоянного TCP; не подходит для ограниченных edge-функций.                              | Можно общаться AMQP 1.0/WebSocket через Shovel/Plugin (но не edge-native).                     | Мы не планируем brokers на edge — не критично.                                                                 |
| **Установка / Dev-опыт**          | Требует 3 ноки ZooKeeper-free (KRaft) или Redpanda / TinyKafka.                                  | Один контейнер, 5 мин в docker-compose.                                                        | RabbitMQ выигрывает в Sprint 0.                                                                                |
| **Managed в DigitalOcean**        | Нет 1st-party; Confluent Cloud (от $0.11/GB †).                                                  | DO Managed RabbitMQ (β) $15/мес (1 GB).                                                        | Для early prod на DO easier — RabbitMQ.                                                                        |
| **Managed в AWS**                 | **MSK Serverless** (автоскейл, $0.11/GB IO) или MSK Standard.                                    | Amazon MQ (RabbitMQ) — $0.08/час + storage.                                                    | При переходе на AWS обе опции доступны.                                                                        |
| **Накладные расходы DevOps**      | Трёхнодовый кластер, мониторинг (JMX, Prometheus).                                               | Один instance + HA-policy.                                                                     | У Kafka выше начальная сложность.                                                                              |
| **Экосистема стриминга**          | Kafka Streams, ksqlDB, Flink → real-time модели.                                                 | Поддержка стрим-lib меньше, нужно доп. инструмент (Flink / Stream-P).                          | Если планируем real-time alert pipeline — Kafka готов.                                                         |

† — Confluent Cloud EU (май 2025), ingress + egress IO, 2 GB storage включено.

---

### Рекомендация по этапам

| Фаза                                      | Брокер                                             | Аргументы                                                                                                                        |
| ----------------------------------------- | -------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------- |
| **Sprint 0–1 (Dev + Early Prod / DO)**    | **RabbitMQ** контейнер → затем DO Managed RabbitMQ | • 1 команда, 1 консюмер.• Быстрый docker-compose.• Стоимость $15/мес, меньше DevOps.                                             |
| **Scale-up (> 5 k msg/s, новые сервисы)** | **Kafka** (MSK Serverless или Confluent Cloud)     | • Исторический log нужен для пересчёта KPI.• Несколько консьюмер-групп (alert-engine, ML-фичи).• Horizontal partition для пиков. |

---

### Как не потерпеть при миграции

1. **Слой-абстракция**: `@pulsepanel/events`

   ```
   ts
   CopyEdit
   export interface EventPublisher { publish(topic: string, data: Buffer): Promise<void> }
   export interface EventSubscriber { subscribe(topic: string, onMsg: (m) => void): void }

   ```

   Реализации: `rabbitAdapter.ts`, `kafkaAdapter.ts`.

2. **Соглашение “topic + schema = <namespace>.<version>”** — одинаково в обоих брокерах.
3. Коллекторы / ETL получают брокер DSN через переменную `MESSAGE_BROKER_URL`.
4. Пишем idempotent consumer: `event_id` в payload → позволяет переехать и редоставить сообщения.
5. Держим TTL семь дней в RabbitMQ; перед миграцией — дублируем поток в Kafka (dual-write) и сравниваем офсеты.

---

### Bottom line

- **RabbitMQ** — лучший старт: прост, дешёв, «в одном контейнере», покрывает 100 % MVP потребностей.
- **Kafka** — стратегический апгрейд: скейл, реплей истории, полное fan-out, богатая стрим-экосистема.
- Абстрагируем продюсер/консюмер интерфейс, чтобы миграция отняла не месяц, а пару дней конфигурации.

## Требование абстракции «Event Bus» и переход c RabbitMQ → AWS SQS

---

### 1 Общая идея

Все сервисы PulsePanel должны говорить не с конкретным брокером, а с **тонким слоем `@pulsepanel/events`**:

```
ts
CopyEdit
// packages/events/src/index.ts
export interface EventEnvelope<T = unknown> {
  id: string;              // ULID
  type: string;            // "shop.order.created.v1"
  timestamp: string;       // ISO-8601
  payload: T;
}

export interface EventBus {
  publish<E>(topic: string, message: EventEnvelope<E>): Promise<void>;
  subscribe<E>(
      topic: string,
      handler: (msg: EventEnvelope<E>) => Promise<void>,
      opts?: { concurrency?: number },
  ): Promise<Unsubscribe>;
}

```

- **Код сервисов** знает только `EventBus`.
- **Адаптеры**:
  - `rabbitBus.ts` — AMQP 0-9-1 (RabbitMQ topic exchange).
  - `sqsBus.ts` — AWS SQS (+ SNS fan-out, см. ниже).
  - Env-флаг `MESSAGE_BACKEND=rabbit|sqs` + DI в NestJS/Node.

---

### 2 Устройство адаптеров

| Фича                | RabbitMQ (сейчас)                                     | AWS SQS (будущее)                              |
| ------------------- | ----------------------------------------------------- | ---------------------------------------------- |
| **Fan-out**         | topic-exchange `raw_events` → queue на каждую группу. | SNS topic ➜ SQS queues (1 per consumer-group). |
| **Ack / Retry**     | Manual ack; nack→dead-letter queue.                   | Delete-Msg on success; redrive policy (DLQ).   |
| **Ordering**        | Routing key `storeId` (hash sharding).                | SQS FIFO + groupId=`storeId` (если нужно).     |
| **TTL / Retention** | Queue TTL 7 days.                                     | SQS retention 7 days (max 14).                 |
| **Exactly-once**    | Idempotency via Redis set `<id>` TTL=24 h.            | То же — consumer-side de-dup (SQS Standard).   |

---

### 3 Конвенции событий (независимы от брокера)

- **Topic-name** = `<domain>.<event>.v<version>` например `shop.order.created.v1`.
- **Envelope.id** — ULID (монотонный → легко сортировать).
- **Payload** — JSON, schema в репо (`/schema/event/*.json`).
- **Producer Outbox** — таблица `outbox_events` в Postgres (`status = PENDING|SENT`) чтобы гарантировать “commit + publish” (Transactional Outbox).

---

### 4 Переход RabbitMQ → SQS — план

| Этап | Действия                                                                                                                  | Downtime |
| ---- | ------------------------------------------------------------------------------------------------------------------------- | -------- |
| 1    | Добавить `sqsBus` адаптер + Terraform SNS/SQS шаблон.                                                                     | 0        |
| 2    | **Dual-write**: `publish()` отправляет в Rabbit _и_ SNS.                                                                  | Нет      |
| 3    | Консьюмеры `metrics-etl`, `product-linker` запускаются со второй инстанцией, читающей SQS-очередь (параметр `GROUP=sqs`). | Нет      |
| 4    | Сравнить метрики lag / dup-rate (Redis de-dup) 48-ч.                                                                      | —        |
| 5    | Переключить переменную `MESSAGE_BACKEND=sqs`, выключить Rabbit-адаптер.                                                   | < 1 мин  |
| 6    | Через 7 дней удалить Rabbit контейнер и exchange.                                                                         | —        |

---

### 5 Плюсы/минусы SQS против RabbitMQ (именно для PulsePanel)

| Плюс SQS                             | Минус SQS                                                               |
| ------------------------------------ | ----------------------------------------------------------------------- |
| _Managed_: zero-ops, HA              | Цена выше при больших объёмах (ingress + egress).                       |
| Retention 14 days — можно «replay».  | Нет глубокой метрики в CloudWatch — придётся строить custom dashboards. |
| Упрощённый IAM вместо user/pass.     | 256 kB лимит msg (большие payload → S3 pre-signed).                     |
| Scale «до нуля», нет забот о дисках. | Fan-out через SNS — лишний ресурс.                                      |

---

### 6 Что учесть в коде, чтобы миграция была painless

1. **Всегда публиковать Envelope с `id`** — для idempotency на SQS.
2. **Не использовать AMQP-специфику** (prefetch, headers-exchange) в бизнес-коде.
3. **Не закладываться на «delete on ack»** — в обоих адаптерах абстракция `ack()` / internal delete.
4. **Back-pressure** — producer читает ENV `MAX_INFLIGHT`. В SQS лимиты выше, но оставляем общий контракт.
5. **Общие dev-скрипты**

   ```bash
   bash
   CopyEdit
   pnpm events:up:rabbit   # docker-compose rabbit
   pnpm events:up:local-sqs # uses localstack

   ```
