import { getDirective, MapperKind, mapSchema } from "@graphql-tools/utils";
import {
  GraphQLSchema,
  defaultFieldResolver,
  GraphQLFieldConfig,
} from "graphql";
import type { Context } from "../types/context.js";
import {
  requireAuth,
  requireRole,
  requireTenantAccess,
  checkRateLimit,
  AuthenticationError,
  AuthorizationError,
  RateLimitError,
} from "./guards.js";

// =============================================================================
// @authenticated DIRECTIVE
// =============================================================================

/**
 * Applies authentication requirement to GraphQL fields
 * Usage: @authenticated
 */
export function authenticatedDirectiveTransformer(schema: GraphQLSchema) {
  return mapSchema(schema, {
    [MapperKind.OBJECT_FIELD]: (fieldConfig: GraphQLFieldConfig<any, any>) => {
      const authDirective = getDirective(
        schema,
        fieldConfig,
        "authenticated",
      )?.[0];

      if (authDirective) {
        const { resolve = defaultFieldResolver } = fieldConfig;

        fieldConfig.resolve = async function (
          source,
          args,
          context: Context,
          info,
        ) {
          const startTime = Date.now();

          try {
            // Require authentication
            requireAuth(context);

            // Optional rate limiting for authenticated operations
            await checkRateLimit(context, info.fieldName, 100, 60000);

            // Execute original resolver
            const result = await resolve(source, args, context, info);

            // Audit successful operation
            context.logSecurityEvent("authenticated_operation_success", {
              field: info.fieldName,
              userId: context.user.id,
              latency: Date.now() - startTime,
            });

            return result;
          } catch (error) {
            // Audit failed operation
            context.logSecurityEvent("authenticated_operation_failed", {
              field: info.fieldName,
              error: error instanceof Error ? error.message : String(error),
              latency: Date.now() - startTime,
            });

            throw error;
          }
        };
      }

      return fieldConfig;
    },
  });
}

// =============================================================================
// @hasRole DIRECTIVE
// =============================================================================

/**
 * Applies role-based access control to GraphQL fields
 * Usage: @hasRole(role: "admin")
 */
export function hasRoleDirectiveTransformer(schema: GraphQLSchema) {
  return mapSchema(schema, {
    [MapperKind.OBJECT_FIELD]: (fieldConfig: GraphQLFieldConfig<any, any>) => {
      const roleDirective = getDirective(schema, fieldConfig, "hasRole")?.[0];

      if (roleDirective) {
        const { resolve = defaultFieldResolver } = fieldConfig;
        const requiredRole = roleDirective.role;

        if (!requiredRole) {
          throw new Error("@hasRole directive requires a 'role' argument");
        }

        fieldConfig.resolve = async function (
          source,
          args,
          context: Context,
          info,
        ) {
          const startTime = Date.now();

          try {
            // Require authentication first
            requireAuth(context);

            // Check role requirement
            requireRole(context, requiredRole);

            // Rate limiting for role-protected operations
            await checkRateLimit(
              context,
              `${info.fieldName}:${requiredRole}`,
              50,
              60000,
            );

            // Execute original resolver
            const result = await resolve(source, args, context, info);

            // Audit successful role-based operation
            context.logSecurityEvent("role_based_operation_success", {
              field: info.fieldName,
              requiredRole,
              userId: context.user.id,
              userRoles: context.user.roles,
              latency: Date.now() - startTime,
            });

            return result;
          } catch (error) {
            // Audit failed role-based operation
            context.logSecurityEvent("role_based_operation_failed", {
              field: info.fieldName,
              requiredRole,
              userId: context.user?.id,
              userRoles: context.user?.roles,
              error: error instanceof Error ? error.message : String(error),
              latency: Date.now() - startTime,
            });

            throw error;
          }
        };
      }

      return fieldConfig;
    },
  });
}

// =============================================================================
// @belongsToTenant DIRECTIVE
// =============================================================================

/**
 * Enforces tenant isolation for GraphQL fields
 * Usage: @belongsToTenant
 */
export function belongsToTenantDirectiveTransformer(schema: GraphQLSchema) {
  return mapSchema(schema, {
    [MapperKind.OBJECT_FIELD]: (fieldConfig: GraphQLFieldConfig<any, any>) => {
      const tenantDirective = getDirective(
        schema,
        fieldConfig,
        "belongsToTenant",
      )?.[0];

      if (tenantDirective) {
        const { resolve = defaultFieldResolver } = fieldConfig;

        fieldConfig.resolve = async function (
          source,
          args,
          context: Context,
          info,
        ) {
          const startTime = Date.now();

          try {
            // Require authentication first
            requireAuth(context);

            // Extract tenant ID from args or source
            const tenantId = args.tenantId || source?.tenantId || args.id;

            if (tenantId) {
              // Enforce tenant access
              requireTenantAccess(context, tenantId);
            }

            // Rate limiting for tenant operations
            await checkRateLimit(
              context,
              `${info.fieldName}:tenant`,
              200,
              60000,
            );

            // Execute original resolver with tenant context
            const result = await resolve(source, args, context, info);

            // Audit successful tenant operation
            context.logSecurityEvent("tenant_operation_success", {
              field: info.fieldName,
              tenantId: context.user.tenantId,
              requestedTenantId: tenantId,
              userId: context.user.id,
              latency: Date.now() - startTime,
            });

            return result;
          } catch (error) {
            // Audit failed tenant operation
            context.logSecurityEvent("tenant_operation_failed", {
              field: info.fieldName,
              tenantId: context.user?.tenantId,
              userId: context.user?.id,
              error: error instanceof Error ? error.message : String(error),
              latency: Date.now() - startTime,
              severity: "HIGH", // Tenant violations are high severity
            });

            throw error;
          }
        };
      }

      return fieldConfig;
    },
  });
}

// =============================================================================
// @rateLimit DIRECTIVE
// =============================================================================

/**
 * Applies custom rate limiting to GraphQL fields
 * Usage: @rateLimit(max: 10, window: 60)
 */
export function rateLimitDirectiveTransformer(schema: GraphQLSchema) {
  return mapSchema(schema, {
    [MapperKind.OBJECT_FIELD]: (fieldConfig: GraphQLFieldConfig<any, any>) => {
      const rateLimitDirective = getDirective(
        schema,
        fieldConfig,
        "rateLimit",
      )?.[0];

      if (rateLimitDirective) {
        const { resolve = defaultFieldResolver } = fieldConfig;
        const maxRequests = rateLimitDirective.max || 100;
        const windowMs = (rateLimitDirective.window || 60) * 1000;

        fieldConfig.resolve = async function (
          source,
          args,
          context: Context,
          info,
        ) {
          const startTime = Date.now();

          try {
            // Apply custom rate limiting
            await checkRateLimit(
              context,
              info.fieldName,
              maxRequests,
              windowMs,
            );

            // Execute original resolver
            const result = await resolve(source, args, context, info);

            // Audit rate limited operation
            context.logSecurityEvent("rate_limited_operation_success", {
              field: info.fieldName,
              maxRequests,
              windowMs,
              remaining: context.security.rateLimit?.remaining,
              userId: context.user?.id,
              latency: Date.now() - startTime,
            });

            return result;
          } catch (error) {
            if (error instanceof RateLimitError) {
              // Audit rate limit violation
              context.logSecurityEvent("rate_limit_violation", {
                field: info.fieldName,
                maxRequests,
                windowMs,
                userId: context.user?.id,
                ip: context.security.ipAddress,
                latency: Date.now() - startTime,
                severity: "MEDIUM",
              });
            }

            throw error;
          }
        };
      }

      return fieldConfig;
    },
  });
}

// =============================================================================
// DIRECTIVE APPLIER
// =============================================================================

/**
 * Applies all security directives to the GraphQL schema
 */
export function applySecurityDirectives(schema: GraphQLSchema): GraphQLSchema {
  let transformedSchema = schema;

  // Apply directives in order
  transformedSchema = authenticatedDirectiveTransformer(transformedSchema);
  transformedSchema = hasRoleDirectiveTransformer(transformedSchema);
  transformedSchema = belongsToTenantDirectiveTransformer(transformedSchema);
  transformedSchema = rateLimitDirectiveTransformer(transformedSchema);

  return transformedSchema;
}
