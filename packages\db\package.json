{"name": "@pulsepanel/db", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "prisma:generate": "prisma generate", "prisma:migrate:dev": "prisma migrate dev", "prisma:migrate:deploy": "prisma migrate deploy", "prisma:studio": "prisma studio", "build": "pnpm clean && pnpm prisma:generate && tsc -p .", "lint": "eslint ."}, "devDependencies": {"@pulsepanel/eslint-config-custom": "workspace:*", "@pulsepanel/tsconfig-custom": "workspace:*", "@types/node": "^20.11.20", "prisma": "^5.22.0", "rimraf": "^6.0.0", "typegraphql-prisma": "^0.28.0", "typescript": "^5.4.5"}, "dependencies": {"@prisma/client": "^5.22.0", "graphql": "^16.11.0", "tslib": "^2.8.1", "type-graphql": "2.0.0-rc.2"}}