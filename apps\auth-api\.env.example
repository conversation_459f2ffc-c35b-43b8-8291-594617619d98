# Authentication API Environment Configuration

# Server Configuration
NODE_ENV=development
PORT=3001

# CORS Configuration
FRONTEND_URL=http://localhost:3000
BFF_URL=http://localhost:4000
WEBSITE_DOMAIN=http://localhost:3000

# SuperTokens Configuration
SUPERTOKENS_CORE_URL=http://localhost:3567
SUPERTOKENS_API_KEY=dev-api-key-secure-2024
API_DOMAIN=http://localhost:3001
API_BASE_PATH=/auth
WEBSITE_BASE_PATH=/auth

# Cookie Configuration (production only)
COOKIE_DOMAIN=

# API Gateway Configuration (optional)
API_GATEWAY_PATH=

# PostgreSQL Database Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=pulsepanel_user
POSTGRES_PASSWORD=pulsepanel_password
POSTGRES_DB=pulsepanel_dev_db