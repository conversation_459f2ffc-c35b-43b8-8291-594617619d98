---
description: 
globs: 
alwaysApply: false
---
---
description:
globs:
alwaysApply: true
---

# State Applied Rules

## Critical Rules

- The very first line of any response MUST state which rules are being applied
- Format the statement as: "⚠️Applied rules: [Validation Over Modification, Task Focus, ...]"
- If no rules are being applied, state: "No rules applied."
- Use only the section headers of the rule as the identifier
- Multiple rules should be separated by commas and a space
- This statement must appear before any other content, including greetings or explanations

## Examples

<example>
⚠️Applied rules: <PERSON><PERSON><PERSON>, Comments

Here is my response to your query about implementing the new feature...
</example>

<example>
No rules applied.

Let me help you with your question about file organization...
</example>

<example type="invalid">
Here is my implementation following our Python standards...

⚠️Applied rules: python-style-auto.mdc

Or:

I'm using the following rules: .cursor/rules/python/python-style-auto.mdc

Or:

[Without any statement about applied rules]
</example>
