import { EventBus, EventEnvelope, Unsubscribe } from "./index";

export function createNoopBus(): EventBus {
  console.log("[NoopEventBus] Initialized.");
  return {
    publish: async <E>(
      topic: string,
      message: EventEnvelope<E>,
    ): Promise<void> => {
      console.log(`[NoopEventBus] PUBLISH to ${topic}:`, message);
    },
    subscribe: async <E>(
      topic: string,
      handler: (message: EventEnvelope<E>) => Promise<void>,
      options?: { consumerTag?: string; concurrency?: number },
    ): Promise<Unsubscribe> => {
      console.log(
        `[NoopEventBus] SUBSCRIBED to ${topic} with options:`,
        options,
      );
      return async () => {
        console.log(`[NoopEventBus] UNSUBSCRIBED from ${topic}`);
      };
    },
    disconnect: async (): Promise<void> => {
      console.log("[NoopEventBus] Disconnected.");
    },
  };
}
