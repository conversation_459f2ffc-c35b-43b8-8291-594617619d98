import type { Redis } from "ioredis";
import type { InvalidationEvent, InvalidationReason } from "./types.js";

export class AuditService {
  constructor(private redis: Redis) {}

  async logInvalidationEvent(
    sessionId: string,
    userId: string,
    tenantId: string | undefined,
    reason: InvalidationReason,
    triggeredBy: "system" | "user" | "admin" | "security",
    details: Record<string, any> = {},
    ipAddress?: string,
  ): Promise<void> {
    const event: InvalidationEvent = {
      sessionId,
      userId,
      tenantId,
      reason,
      timestamp: Date.now(),
      triggeredBy,
      details,
      ipAddress,
    };

    try {
      const eventKey = `audit:invalidation:${Date.now()}:${sessionId}`;
      await this.redis.setex(eventKey, 604800, JSON.stringify(event)); // 7 days retention

      // Daily aggregation for analytics
      const dateKey = this.getDailyKey();
      await this.redis.hincrby(dateKey, `invalidation:${reason}`, 1);
      await this.redis.expire(dateKey, 2592000); // 30 days retention

      // Security events get special treatment
      if (triggeredBy === "security") {
        await this.logSecurityEvent(event);
      }

      console.log(
        `🔒 Audit: Session invalidated - ${reason} for user ${userId} by ${triggeredBy}`,
      );
    } catch (error) {
      console.error("Failed to log invalidation event:", error);
    }
  }

  async logSecurityEvent(event: InvalidationEvent): Promise<void> {
    try {
      const securityKey = `security:events:${Date.now()}`;
      await this.redis.setex(securityKey, 2592000, JSON.stringify(event)); // 30 days retention

      // Security alert counter
      const alertKey = `security:alerts:${this.getDailyKey()}`;
      const alertCount = await this.redis.incr(alertKey);
      await this.redis.expire(alertKey, 86400); // 24 hours

      // Critical security events
      if (event.reason === "security_violation" && alertCount > 10) {
        console.warn(
          `🚨 SECURITY ALERT: ${alertCount} security events today - possible attack`,
        );
      }
    } catch (error) {
      console.error("Failed to log security event:", error);
    }
  }

  async getInvalidationStats(days = 7): Promise<Record<string, any>> {
    try {
      const stats: Record<string, any> = {
        totalEvents: 0,
        byReason: {},
        byTrigger: {},
        securityEvents: 0,
      };

      for (let i = 0; i < days; i++) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        const dayKey = `audit:daily:${date.toISOString().split("T")[0]}`;

        const dayStats = await this.redis.hgetall(dayKey);

        for (const [key, value] of Object.entries(dayStats)) {
          if (key.startsWith("invalidation:")) {
            const reason = key.replace("invalidation:", "");
            stats.byReason[reason] =
              (stats.byReason[reason] || 0) + parseInt(value);
            stats.totalEvents += parseInt(value);
          }
        }
      }

      // Get security events count
      const securityKeys = await this.redis.keys("security:events:*");
      stats.securityEvents = securityKeys.length;

      return stats;
    } catch (error) {
      console.error("Failed to get invalidation stats:", error);
      return { totalEvents: 0, byReason: {}, byTrigger: {}, securityEvents: 0 };
    }
  }

  async getRecentEvents(limit = 50): Promise<InvalidationEvent[]> {
    try {
      const keys = await this.redis.keys("audit:invalidation:*");

      // Sort by timestamp (newest first)
      keys.sort((a, b) => {
        const timestampA = parseInt(a.split(":")[2]);
        const timestampB = parseInt(b.split(":")[2]);
        return timestampB - timestampA;
      });

      const recentKeys = keys.slice(0, limit);
      const events: InvalidationEvent[] = [];

      for (const key of recentKeys) {
        const eventData = await this.redis.get(key);
        if (eventData) {
          events.push(JSON.parse(eventData));
        }
      }

      return events;
    } catch (error) {
      console.error("Failed to get recent events:", error);
      return [];
    }
  }

  async cleanupOldEvents(): Promise<{ deleted: number; errors: number }> {
    let deleted = 0;
    let errors = 0;

    try {
      // Delete audit events older than 7 days
      const cutoffTime = Date.now() - 7 * 24 * 60 * 60 * 1000;
      const keys = await this.redis.keys("audit:invalidation:*");

      for (const key of keys) {
        try {
          const timestamp = parseInt(key.split(":")[2]);
          if (timestamp < cutoffTime) {
            await this.redis.del(key);
            deleted++;
          }
        } catch {
          errors++;
        }
      }

      if (deleted > 0) {
        console.log(
          `🧹 Audit cleanup: Deleted ${deleted} old events, ${errors} errors`,
        );
      }
    } catch (error) {
      console.error("Failed to cleanup old audit events:", error);
      errors++;
    }

    return { deleted, errors };
  }

  private getDailyKey(): string {
    return new Date().toISOString().split("T")[0];
  }
}
