# No-Lock Strategies

## Стратегия «UI/Refine без lock‑in»

| Слой              | Приём                                                                       | Почему помогает миграции                                        |
| ----------------- | --------------------------------------------------------------------------- | --------------------------------------------------------------- |
| **Данные**        | GraphQL SDK (`graphql-request`) вне Refine.                                 | Смена UI‑фреймворка не трогает network слой.                    |
| **Hooks wrapper** | Создаём `useProducts()` → внутри зовём `useList({ resource: "products" })`. | Меняем API один раз внутри обёртки, остальной код стабилен.     |
| **UI компоненты** | `<ProductTable />` использует shadcn Table, не Refine `<List>` напрямую.   | На React‑Admin или TanStack меняем только имплементации таблиц. |
| **Формы**         | `react-hook-form` + Zod вне Refine action‑helper.                           | Универсальный DX, легко портируется.                            |
| **Routing**       | Доверяем Next Router (или Remix) — Refine router adapter тонкий.            | При миграции остаётся родной файловый роутинг.                  |
| **RBAC / Auth**   | Собственный `useAuth()` (cookie JWT).                                       | Избавляет от Refine `authProvider` специфики.                   |
| **Theme**         | Использовать Mantine tokens через `ThemeProvider` а не Refine preset.       | Любой новый фреймворк можно «надеть» тот же Mantine theme.      |

## Стратегия "Remix/NextJS без lock-in"

- Изолировать серверные функции в /server или /utils/server‑only.ts — если когда‑либо перейдёте на Remix или tRPC, придётся трогать только одну папку.
- GraphQL fetcher — делайте универсальный graphql‑request‑hook, а не Next‑specific fetch, чтобы он жил и в Remix.
- Формы — не завязывайтесь на useFormStatus глубоко; оставьте fallback `<form action="/api/…">` + progressive enhancement.
- Env‑границы — храните process.env._доступные клиенту через NEXT*PUBLIC* — в Remix это превратится в import.meta.env.PUBLIC\__.
