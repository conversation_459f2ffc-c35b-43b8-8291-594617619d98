import type { GraphQLSchema, ValidationRule } from "graphql";
import { GraphQLError } from "graphql";
import depthLimit from "graphql-depth-limit";
import type { Context } from "../types/context.js";

// =============================================================================
// QUERY COMPLEXITY ANALYSIS
// =============================================================================

/**
 * GraphQL query depth limiting (max 10 levels)
 */
export function createDepthLimitRule(maxDepth = 10): ValidationRule {
  return depthLimit(maxDepth);
}

/**
 * Query cost analysis для expensive operations
 */
export function analyzeQueryCost(
  query: string,
  variables?: Record<string, any>,
): number {
  let cost = 1; // Base cost

  // Basic cost calculation (можно расширить)
  const lines = query.split("\n").length;
  const complexity = (query.match(/{/g) || []).length; // Nested objects
  const arrayFields = (query.match(/\[\w+!\]!/g) || []).length; // Array fields

  cost += lines * 0.1;
  cost += complexity * 0.5;
  cost += arrayFields * 2; // Arrays are expensive

  return Math.ceil(cost);
}

/**
 * Query timeout enforcement (30s max)
 */
export function createTimeoutWrapper(timeoutMs = 30000) {
  return function timeoutMiddleware(resolve: any) {
    return async function (
      source: any,
      args: any,
      context: Context,
      info: any,
    ) {
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => {
          context.logSecurityEvent("query_timeout", {
            field: info.fieldName,
            timeout: timeoutMs,
            operation: context.audit.operationName,
          });
          reject(
            new GraphQLError("Query timeout exceeded", {
              extensions: { code: "QUERY_TIMEOUT" },
            }),
          );
        }, timeoutMs),
      );

      const resultPromise = resolve(source, args, context, info);

      return Promise.race([resultPromise, timeoutPromise]);
    };
  };
}

// =============================================================================
// FIELD-LEVEL SECURITY
// =============================================================================

interface FieldSecurityRule {
  field: string;
  requiredRole?: string;
  redact?: boolean;
  gdprSensitive?: boolean;
}

/**
 * Sensitive field redaction (PII protection)
 */
export function redactSensitiveFields(data: any, context: Context): any {
  if (!data) return data;

  const sensitiveFields = ["email", "phone", "ssn", "personalId"];
  const userRoles = context.user?.roles || [];

  // Only admins can see PII
  const canSeePII = userRoles.includes("admin");

  if (Array.isArray(data)) {
    return data.map((item) => redactSensitiveFields(item, context));
  }

  if (typeof data === "object") {
    const redacted = { ...data };

    for (const field of sensitiveFields) {
      if (field in redacted && !canSeePII) {
        redacted[field] = "***REDACTED***";

        // Log PII access attempt
        context.logSecurityEvent("pii_access_denied", {
          field,
          userId: context.user?.id,
          requiredRole: "admin",
          userRoles,
        });
      }
    }

    // Recursively redact nested objects
    for (const key in redacted) {
      if (typeof redacted[key] === "object") {
        redacted[key] = redactSensitiveFields(redacted[key], context);
      }
    }

    return redacted;
  }

  return data;
}

/**
 * GDPR compliance helpers для EU users
 */
export function applyGDPRCompliance(data: any, context: Context): any {
  // Check if user is in EU (basic implementation)
  const isEUUser = context.security.ipAddress.startsWith("EU") || false;

  if (!isEUUser) return data;

  // Apply GDPR data minimization
  const gdprSafeData = redactSensitiveFields(data, context);

  // Log GDPR data access
  context.logSecurityEvent("gdpr_data_access", {
    userId: context.user?.id,
    dataFields: Object.keys(data || {}),
    isEUUser,
  });

  return gdprSafeData;
}

// =============================================================================
// ADVANCED AUDIT & MONITORING
// =============================================================================

interface SecurityAlert {
  severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
  type: string;
  message: string;
  userId?: string;
  ip?: string;
  timestamp: string;
  metadata?: Record<string, any>;
}

/**
 * Real-time security violation alerts
 */
export async function triggerSecurityAlert(
  context: Context,
  alert: SecurityAlert,
): Promise<void> {
  // Store alert in Redis for real-time monitoring
  await context.redis.lpush("security_alerts", JSON.stringify(alert));
  await context.redis.expire("security_alerts", 86400 * 7); // 7 days retention

  // Log to audit trail
  context.logSecurityEvent("security_alert", alert);

  // In production, this would trigger external alerting (Slack, PagerDuty, etc.)
  if (process.env.NODE_ENV === "production" && alert.severity === "CRITICAL") {
    console.error("🚨 CRITICAL SECURITY ALERT:", alert);
    // await sendCriticalAlert(alert);
  }
}

/**
 * GraphQL operation performance monitoring
 */
export function monitorOperationPerformance(
  context: Context,
  latency: number,
): void {
  const operation = context.audit.operationName || "unknown";

  // Track performance metrics
  const perfKey = `perf:${operation}`;
  context.redis.lpush(
    perfKey,
    JSON.stringify({
      timestamp: Date.now(),
      latency,
      userId: context.user?.id,
    }),
  );
  context.redis.expire(perfKey, 3600); // 1 hour retention

  // Alert on slow operations
  if (latency > 5000) {
    // > 5 seconds
    triggerSecurityAlert(context, {
      severity: "MEDIUM",
      type: "SLOW_OPERATION",
      message: `GraphQL operation ${operation} took ${latency}ms`,
      userId: context.user?.id,
      ip: context.security.ipAddress,
      timestamp: new Date().toISOString(),
      metadata: { latency, operation },
    });
  }
}

/**
 * Suspicious query pattern detection
 */
export function detectSuspiciousPatterns(
  query: string,
  context: Context,
): void {
  const suspiciousPatterns = [
    /introspection/i, // Schema introspection attempts
    /__schema|__type/i, // GraphQL introspection
    /union.*fragment/i, // Complex union fragments
    /\.{10,}/, // Excessive field requests
  ];

  for (const pattern of suspiciousPatterns) {
    if (pattern.test(query)) {
      triggerSecurityAlert(context, {
        severity: "HIGH",
        type: "SUSPICIOUS_QUERY",
        message: `Suspicious GraphQL query pattern detected: ${pattern.source}`,
        userId: context.user?.id,
        ip: context.security.ipAddress,
        timestamp: new Date().toISOString(),
        metadata: { query: query.substring(0, 500), pattern: pattern.source },
      });
      break;
    }
  }
}

/**
 * Automated security report generation
 */
export async function generateSecurityReport(context: Context): Promise<any> {
  const last24h = Date.now() - 24 * 60 * 60 * 1000;

  // Get security events from Redis
  const events = await context.redis.lrange("security_events", 0, -1);
  const alerts = await context.redis.lrange("security_alerts", 0, -1);

  const recentEvents = events
    .map((e) => JSON.parse(e))
    .filter((e) => new Date(e.timestamp).getTime() > last24h);

  const recentAlerts = alerts
    .map((a) => JSON.parse(a))
    .filter((a) => new Date(a.timestamp).getTime() > last24h);

  return {
    period: "24h",
    summary: {
      totalEvents: recentEvents.length,
      totalAlerts: recentAlerts.length,
      criticalAlerts: recentAlerts.filter((a) => a.severity === "CRITICAL")
        .length,
      uniqueUsers: [
        ...new Set(recentEvents.map((e) => e.userId).filter(Boolean)),
      ].length,
    },
    topEvents: recentEvents.reduce((acc: any, event: any) => {
      acc[event.event] = (acc[event.event] || 0) + 1;
      return acc;
    }, {}),
    alertsByType: recentAlerts.reduce((acc: any, alert: any) => {
      acc[alert.type] = (acc[alert.type] || 0) + 1;
      return acc;
    }, {}),
    generatedAt: new Date().toISOString(),
  };
}

// =============================================================================
// INTROSPECTION SECURITY
// =============================================================================

/**
 * Security-conscious introspection control
 */
export function createIntrospectionSecurity() {
  return {
    // Disable introspection в production
    introspection: process.env.NODE_ENV !== "production",

    // Development-only introspection с auth
    introspectionMiddleware: (context: Context) => {
      if (process.env.NODE_ENV === "production") {
        context.logSecurityEvent("introspection_blocked_production", {
          ip: context.security.ipAddress,
          userAgent: context.security.userAgent,
        });
        return false;
      }

      // In development, require authentication for introspection
      if (!context.isAuthenticated) {
        context.logSecurityEvent("introspection_blocked_unauthenticated", {
          ip: context.security.ipAddress,
        });
        return false;
      }

      return true;
    },
  };
}

/**
 * Enhanced error formatting for security
 */
export function securityErrorFormatter(err: any, context?: Context): any {
  // Log all errors for security monitoring
  if (context) {
    context.logSecurityEvent("graphql_error", {
      message: err.message,
      code: err.extensions?.code,
      path: err.path,
      ip: context.security.ipAddress,
    });
  }

  // In production, sanitize error messages to prevent information disclosure
  if (process.env.NODE_ENV === "production") {
    const sanitizedError = {
      message: "An error occurred",
      extensions: {
        code: err.extensions?.code || "INTERNAL_ERROR",
        timestamp: new Date().toISOString(),
      },
    };

    // Allow specific error types to show their messages
    const allowedCodes = [
      "UNAUTHENTICATED",
      "FORBIDDEN",
      "RATE_LIMITED",
      "TENANT_ACCESS_DENIED",
      "QUERY_DEPTH_EXCEEDED",
      "QUERY_TIMEOUT",
    ];

    if (allowedCodes.includes(err.extensions?.code)) {
      sanitizedError.message = err.message;
    }

    return sanitizedError;
  }

  return err;
}
