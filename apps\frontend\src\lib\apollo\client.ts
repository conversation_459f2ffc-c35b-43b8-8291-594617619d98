import {
  ApolloClient,
  InMemoryCache,
  createHttpLink,
  from,
  ApolloLink,
  Observable,
  type FetchPolicy,
} from "@apollo/client";
import { onError } from "@apollo/client/link/error";
import { setContext } from "@apollo/client/link/context";

declare global {
  interface Window {
    __REDUX_DEVTOOLS_EXTENSION__?: any;
  }
}

interface ApolloClientConfig {
  uri: string;
  enableCaching?: boolean;
  debugMode?: boolean;
  ssrMode?: boolean;
}

const defaultConfig: ApolloClientConfig = {
  uri: process.env.NEXT_PUBLIC_GRAPHQL_URL || "http://localhost:4000/graphql",
  enableCaching: true,
  debugMode: process.env.NODE_ENV === "development",
  ssrMode: typeof window === "undefined",
};

function createReduxDevToolsLink(): ApolloLink {
  return new ApolloLink((operation, forward) => {
    const startTime = Date.now();

    return new Observable((observer) => {
      const subscription = forward(operation).subscribe({
        next: (result) => {
          const duration = Date.now() - startTime;

          if (
            typeof window !== "undefined" &&
            window.__REDUX_DEVTOOLS_EXTENSION__
          ) {
            window.__REDUX_DEVTOOLS_EXTENSION__.send(
              {
                type: `GraphQL: ${operation.operationName || "Anonymous"}`,
                operation: operation.operationName,
                variables: operation.variables,
                duration: `${duration}ms`,
              },
              {
                apollo: {
                  operation: operation.operationName,
                  variables: operation.variables,
                  result: result.data,
                  duration,
                  timestamp: new Date().toISOString(),
                },
              },
            );
          }

          observer.next(result);
        },
        error: (error) => {
          if (
            typeof window !== "undefined" &&
            window.__REDUX_DEVTOOLS_EXTENSION__
          ) {
            window.__REDUX_DEVTOOLS_EXTENSION__.send(
              {
                type: `GraphQL Error: ${operation.operationName || "Anonymous"}`,
                operation: operation.operationName,
                variables: operation.variables,
                error: error.message,
              },
              {
                apollo: {
                  operation: operation.operationName,
                  variables: operation.variables,
                  error: error.message,
                  timestamp: new Date().toISOString(),
                },
              },
            );
          }

          observer.error(error);
        },
        complete: () => observer.complete(),
      });

      return () => subscription.unsubscribe();
    });
  });
}

function createApolloClient(
  config: ApolloClientConfig = defaultConfig,
): ApolloClient<any> {
  const httpLink = createHttpLink({
    uri: config.uri,
    credentials: "include",
    headers: {
      "Content-Type": "application/json",
    },
  });

  const authLink = setContext((_, { headers }) => {
    return {
      headers: {
        ...headers,
        "Apollo-Client-Name": "PulsePanel-Frontend",
        "Apollo-Client-Version": "1.0.0",
        // SuperTokens handles CSRF automatically with httpOnly cookies
        // No need to manually add anti-csrf headers in httpOnly mode
      },
    };
  });

  const errorLink = onError(
    ({ graphQLErrors, networkError, operation, forward }) => {
      if (graphQLErrors) {
        graphQLErrors.forEach(({ message, locations, path, extensions }) => {
          const isAuthError =
            message.includes("401") ||
            message.includes("Unauthorized") ||
            extensions?.code === "UNAUTHENTICATED";

          if (config.debugMode && !isAuthError) {
            console.group(
              `[Apollo Client] GraphQL Error in ${operation.operationName}`,
            );
            console.error("Message:", message);
            console.error("Locations:", locations);
            console.error("Path:", path);
            console.error("Extensions:", extensions);
            console.groupEnd();
          }
        });
      }

      if (networkError) {
        const isAuthError =
          networkError.message.includes("401") ||
          networkError.message.includes("Unauthorized");

        if (config.debugMode && !isAuthError) {
          console.group(
            `[Apollo Client] Network Error in ${operation.operationName}`,
          );
          console.error("Network Error:", networkError);
          console.groupEnd();
        }
      }
    },
  );

  const links = [errorLink, authLink, httpLink];

  if (config.debugMode) {
    links.unshift(createReduxDevToolsLink());
  }

  return new ApolloClient({
    link: from(links),
    cache: new InMemoryCache({
      typePolicies: {
        Query: {
          fields: {
            users: {
              keyArgs: false,
              merge(existing = [], incoming = []) {
                return incoming;
              },
            },
          },
        },
        User: {
          keyFields: ["id"],
          fields: {
            userTenants: {
              merge: false,
            },
          },
        },
        Project: {
          keyFields: ["id"],
        },
        Tenant: {
          keyFields: ["id"],
        },
      },
      possibleTypes: {},
    }),
    ssrMode: config.ssrMode,
    connectToDevTools: config.debugMode && !config.ssrMode,
    defaultOptions: {
      watchQuery: {
        errorPolicy: "all",
        fetchPolicy: config.enableCaching
          ? ("cache-first" as FetchPolicy)
          : ("network-only" as FetchPolicy),
        notifyOnNetworkStatusChange: true,
      },
      query: {
        errorPolicy: "all",
        fetchPolicy: config.enableCaching
          ? ("cache-first" as FetchPolicy)
          : ("network-only" as FetchPolicy),
      },
      mutate: {
        errorPolicy: "all",
        awaitRefetchQueries: true,
        refetchQueries: "active",
      },
    },
  });
}

let apolloClient: ApolloClient<any>;

export function getApolloClient(
  config?: Partial<ApolloClientConfig>,
): ApolloClient<any> {
  const mergedConfig = { ...defaultConfig, ...config };

  if (mergedConfig.ssrMode) {
    return createApolloClient(mergedConfig);
  }

  if (!apolloClient) {
    apolloClient = createApolloClient(mergedConfig);
  }

  return apolloClient;
}

export function resetApolloClient(): void {
  if (apolloClient) {
    apolloClient.clearStore();
    apolloClient = undefined as any;
  }
}

export type { ApolloClientConfig };
