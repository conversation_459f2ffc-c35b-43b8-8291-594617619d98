import { GraphQLError } from "graphql";
import { AuthService } from "../auth/authService.js";
import { requireAuth } from "../auth/guards.js";
import EmailPassword from "supertokens-node/recipe/emailpassword/index.js";
import supertokens, { convertToRecipeUserId } from "supertokens-node";
import type {
  AuthResponse,
  MutationSignInArgs,
  MutationSignUpArgs,
  MutationUpdateUserMetadataArgs,
  MutationAssignUserRoleArgs,
  MutationRemoveUserRoleArgs,
  MutationCreateRoleArgs,
  MutationForgotPasswordArgs,
  MutationResetPasswordArgs,
  MutationChangePasswordArgs,
  QueryGetUserRolesArgs,
  QueryGetUsersWithRoleArgs,
  SessionInfo,
  RoleResponse,
  UserRolesResponse,
  UsersWithRoleResponse,
  CreateRoleResponse,
} from "../generated/graphql-types.js";
import type { Context } from "../types/context.js";

/**
 * Enterprise-Grade Auth Resolvers with Defense-in-Depth Security
 */
export default {
  Query: {
    /**
     * Get current session information
     */
    sessionInfo: async (
      _: any,
      __: any,
      context: Context,
    ): Promise<SessionInfo | null> => {
      try {
        requireAuth(context);

        if (!context.session) {
          return null;
        }

        context.logSecurityEvent("session_info_accessed", {
          userId: context.user!.id,
          tenantId: context.user!.tenantId,
        });

        return {
          userId: context.session.userId,
          sessionHandle: context.session.sessionHandle,
          tenantId: context.session.tenantId,
          roles: context.session.roles || [],
          issuedAt: new Date().toISOString(), // This would be stored in session data in a real implementation
          expiresAt: new Date(Date.now() + 300000).toISOString(), // 5 minutes from now as placeholder
          deviceInfo: {
            deviceType: "web", // This would be detected from user agent
            userAgent: context.security.userAgent,
            location: "unknown",
            ipAddress: context.security.ipAddress,
          },
          securityMetadata: {
            lastLoginAt: new Date().toISOString(),
            loginAttempts: 1,
            riskScore: 0.1,
            suspiciousActivity: false,
          },
        };
      } catch (error) {
        context.logSecurityEvent("session_info_error", {
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    },

    /**
     * Get user roles - manager/admin only
     */
    getUserRoles: async (
      _: any,
      args: QueryGetUserRolesArgs,
      context: Context,
    ): Promise<UserRolesResponse> => {
      try {
        requireAuth(context);

        const authService = new AuthService(context.prisma, context.redis);
        const result = await authService.getUserRoles(
          args.tenantId || "public",
          args.userId,
        );

        context.logSecurityEvent("get_user_roles", {
          requestedUserId: args.userId,
          requestedByUserId: context.user!.id,
          tenantId: args.tenantId,
          success: result.success,
        });

        return {
          success: result.success,
          roles: result.roles || [],
          error: result.error || null,
        };
      } catch (error) {
        context.logSecurityEvent("get_user_roles_error", {
          error: error instanceof Error ? error.message : String(error),
          requestedUserId: args.userId,
          requestedByUserId: context.user?.id,
        });

        return {
          success: false,
          roles: [],
          error: {
            code: "GET_USER_ROLES_ERROR",
            message: "Failed to get user roles",
          },
        };
      }
    },

    /**
     * Get users with specific role - admin only
     */
    getUsersWithRole: async (
      _: any,
      args: QueryGetUsersWithRoleArgs,
      context: Context,
    ): Promise<UsersWithRoleResponse> => {
      try {
        requireAuth(context);

        const authService = new AuthService(context.prisma, context.redis);
        const result = await authService.getUsersWithRole(
          args.tenantId || "public",
          args.role,
        );

        context.logSecurityEvent("get_users_with_role", {
          requestedRole: args.role,
          requestedByUserId: context.user!.id,
          tenantId: args.tenantId,
          success: result.success,
        });

        return {
          success: result.success,
          users: result.users || [],
          error: result.error || null,
        };
      } catch (error) {
        context.logSecurityEvent("get_users_with_role_error", {
          error: error instanceof Error ? error.message : String(error),
          requestedRole: args.role,
          requestedByUserId: context.user?.id,
        });

        return {
          success: false,
          users: [],
          error: {
            code: "GET_USERS_WITH_ROLE_ERROR",
            message: "Failed to get users with role",
          },
        };
      }
    },
  },

  Mutation: {
    /**
     * Update user metadata in SuperTokens
     */
    updateUserMetadata: async (
      _: any,
      args: MutationUpdateUserMetadataArgs,
      context: Context,
    ): Promise<boolean> => {
      try {
        requireAuth(context);

        if (!context.session) {
          throw new GraphQLError("Session not found");
        }

        const authService = new AuthService(context.prisma, context.redis);
        const result = await authService.updateUserMetadata(
          context.session.userId,
          args.metadata,
        );

        context.logSecurityEvent("user_metadata_updated", {
          userId: context.user!.id,
          tenantId: context.user!.tenantId,
          metadata: args.metadata,
        });

        return result;
      } catch (error) {
        context.logSecurityEvent("user_metadata_update_error", {
          error: error instanceof Error ? error.message : String(error),
        });
        throw error;
      }
    },
    /**
     * User Registration with Enterprise Security
     */
    signUp: async (
      _: any,
      args: MutationSignUpArgs,
      context: Context,
    ): Promise<AuthResponse> => {
      try {
        // Rate limiting check
        const rateLimitKey = `signup_rate_limit:${context.security.ipAddress}`;
        const attempts = await context.redis.incr(rateLimitKey);

        if (attempts === 1) {
          await context.redis.expire(rateLimitKey, 300); // 5 minutes
        }

        if (attempts > 5) {
          context.logSecurityEvent("signup_rate_limited", {
            ipAddress: context.security.ipAddress,
            attempts,
          });

          throw new GraphQLError(
            "Too many registration attempts. Please try again later.",
            {
              extensions: { code: "RATE_LIMITED" },
            },
          );
        }

        const authService = new AuthService(context.prisma, context.redis);

        const result = await authService.signUp(
          {
            email: args.input.email,
            password: args.input.password,
            name: args.input.name || undefined,
            tenantId: args.input.tenantId,
          },
          context.req,
          context.res,
          context,
        );

        // Clear rate limiting on successful registration
        if (result.success) {
          await context.redis.del(rateLimitKey);
        }

        return result;
      } catch (error) {
        context.logSecurityEvent("signup_resolver_error", {
          error: error instanceof Error ? error.message : String(error),
          email: args.input.email,
          ipAddress: context.security.ipAddress,
        });

        // Don't leak internal errors
        if (error instanceof GraphQLError) {
          throw error;
        }

        throw new GraphQLError("Registration failed", {
          extensions: { code: "SIGNUP_FAILED" },
        });
      }
    },

    /**
     * User Sign In with Security Monitoring
     */
    signIn: async (
      _: any,
      args: MutationSignInArgs,
      context: Context,
    ): Promise<AuthResponse> => {
      try {
        // Rate limiting for sign in attempts
        const rateLimitKey = `signin_rate_limit:${context.security.ipAddress}`;
        const attempts = await context.redis.incr(rateLimitKey);

        if (attempts === 1) {
          await context.redis.expire(rateLimitKey, 300); // 5 minutes
        }

        if (attempts > 10) {
          context.logSecurityEvent("signin_rate_limited", {
            ipAddress: context.security.ipAddress,
            email: args.email,
            attempts,
          });

          throw new GraphQLError(
            "Too many sign in attempts. Please try again later.",
            {
              extensions: { code: "RATE_LIMITED" },
            },
          );
        }

        // Create auth service instance
        const authService = new AuthService(context.prisma, context.redis);

        // Perform sign in with rememberMe support
        const result = await authService.signIn(
          {
            email: args.email,
            password: args.password,
            rememberMe: args.rememberMe || false,
          },
          context.req,
          context.res,
          context,
        );

        // Clear rate limiting on successful sign in
        if (result.success) {
          await context.redis.del(rateLimitKey);
        }

        return result;
      } catch (error) {
        // Enhanced error logging
        context.logSecurityEvent("signin_resolver_error", {
          error: error instanceof Error ? error.message : String(error),
          email: args.email,
          ipAddress: context.security.ipAddress,
        });

        // Don't leak internal errors
        if (error instanceof GraphQLError) {
          throw error;
        }

        throw new GraphQLError("Sign in failed", {
          extensions: { code: "SIGNIN_FAILED" },
        });
      }
    },

    /**
     * User Sign Out with Session Cleanup
     */
    signOut: async (_: any, __: any, context: Context): Promise<boolean> => {
      try {
        // Require authentication
        requireAuth(context);

        if (!context.session?.sessionHandle) {
          return false;
        }

        // Create auth service instance
        const authService = new AuthService(context.prisma, context.redis);

        // Perform sign out with cookie clearing
        const success = await authService.signOut(
          context.session.sessionHandle,
          context,
          context.res,
        );

        if (success) {
          context.logSecurityEvent("signout_resolver_success", {
            userId: context.user!.id,
            sessionHandle: context.session.sessionHandle,
          });
        }

        return success;
      } catch (error) {
        context.logSecurityEvent("signout_resolver_error", {
          error: error instanceof Error ? error.message : String(error),
          userId: context.user?.id,
        });

        // Don't leak internal errors
        throw new GraphQLError("Sign out failed", {
          extensions: { code: "SIGNOUT_FAILED" },
        });
      }
    },

    /**
     * Refresh Token with Security Checks
     */
    refreshToken: async (
      _: any,
      __: any,
      context: Context,
    ): Promise<AuthResponse> => {
      try {
        // Rate limiting for refresh attempts
        const rateLimitKey = `refresh_rate_limit:${context.security.ipAddress}`;
        const attempts = await context.redis.incr(rateLimitKey);

        if (attempts === 1) {
          await context.redis.expire(rateLimitKey, 300); // 5 minutes
        }

        if (attempts > 20) {
          context.logSecurityEvent("refresh_rate_limited", {
            ipAddress: context.security.ipAddress,
            attempts,
          });

          throw new GraphQLError(
            "Too many refresh attempts. Please try again later.",
            {
              extensions: { code: "RATE_LIMITED" },
            },
          );
        }

        const authService = new AuthService(context.prisma, context.redis);

        const result = await authService.refreshToken(
          context.req,
          context.res,
          context,
        );

        // Clear rate limiting on successful refresh
        if (result.success) {
          await context.redis.del(rateLimitKey);
        }

        return result;
      } catch (error) {
        context.logSecurityEvent("refresh_resolver_error", {
          error: error instanceof Error ? error.message : String(error),
          userId: context.user?.id,
        });

        // Don't leak internal errors
        if (error instanceof GraphQLError) {
          throw error;
        }

        throw new GraphQLError("Token refresh failed", {
          extensions: { code: "REFRESH_FAILED" },
        });
      }
    },

    /**
     * Assign role to user - admin only
     */
    assignUserRole: async (
      _: any,
      args: MutationAssignUserRoleArgs,
      context: Context,
    ): Promise<RoleResponse> => {
      try {
        requireAuth(context);

        const authService = new AuthService(context.prisma, context.redis);
        const result = await authService.assignRole(
          args.input.tenantId || "public",
          args.input.userId,
          args.input.role,
          context,
        );

        return {
          success: result.success,
          didUserAlreadyHaveRole: result.didUserAlreadyHaveRole || false,
          didUserHaveRole: undefined, // Only used for removeUserRole
          error: result.error || null,
        };
      } catch (error) {
        context.logSecurityEvent("assign_user_role_resolver_error", {
          error: error instanceof Error ? error.message : String(error),
          targetUserId: args.input.userId,
          role: args.input.role,
          adminUserId: context.user?.id,
        });

        return {
          success: false,
          didUserAlreadyHaveRole: false,
          didUserHaveRole: false,
          error: {
            code: "ASSIGN_ROLE_ERROR",
            message: "Failed to assign role",
          },
        };
      }
    },

    /**
     * Remove role from user - admin only
     */
    removeUserRole: async (
      _: any,
      args: MutationRemoveUserRoleArgs,
      context: Context,
    ): Promise<RoleResponse> => {
      try {
        requireAuth(context);

        const authService = new AuthService(context.prisma, context.redis);
        const result = await authService.removeRole(
          args.input.tenantId || "public",
          args.input.userId,
          args.input.role,
          context,
        );

        return {
          success: result.success,
          didUserAlreadyHaveRole: undefined, // Only used for assignUserRole
          didUserHaveRole: result.didUserHaveRole || false,
          error: result.error || null,
        };
      } catch (error) {
        context.logSecurityEvent("remove_user_role_resolver_error", {
          error: error instanceof Error ? error.message : String(error),
          targetUserId: args.input.userId,
          role: args.input.role,
          adminUserId: context.user?.id,
        });

        return {
          success: false,
          didUserAlreadyHaveRole: false,
          didUserHaveRole: false,
          error: {
            code: "REMOVE_ROLE_ERROR",
            message: "Failed to remove role",
          },
        };
      }
    },

    /**
     * Create new role with permissions - admin only
     */
    createRole: async (
      _: any,
      args: MutationCreateRoleArgs,
      context: Context,
    ): Promise<CreateRoleResponse> => {
      try {
        requireAuth(context);

        const authService = new AuthService(context.prisma, context.redis);
        const result = await authService.createRoleWithPermissions(
          args.input.role,
          args.input.permissions || [],
        );

        context.logSecurityEvent("create_role", {
          roleName: args.input.role,
          permissions: args.input.permissions,
          createdByUserId: context.user!.id,
          success: result.success,
        });

        return {
          success: result.success,
          createdNewRole: result.createdNewRole || false,
          error: result.error || null,
        };
      } catch (error) {
        context.logSecurityEvent("create_role_resolver_error", {
          error: error instanceof Error ? error.message : String(error),
          roleName: args.input.role,
          adminUserId: context.user?.id,
        });

        return {
          success: false,
          createdNewRole: false,
          error: {
            code: "CREATE_ROLE_ERROR",
            message: "Failed to create role",
          },
        };
      }
    },

    /**
     * Forgot password - public endpoint
     */
    forgotPassword: async (
      _: any,
      args: MutationForgotPasswordArgs,
      context: Context,
    ): Promise<boolean> => {
      try {
        // Basic input validation
        if (!args.email || !args.email.includes("@")) {
          context.logSecurityEvent("forgot_password_invalid_email", {
            email: args.email,
            ipAddress: context.security.ipAddress,
          });
          // For security, don't reveal validation failure - return success
          return true;
        }

        const usersInfo = await supertokens.listUsersByAccountInfo("public", {
          email: args.email,
        });

        let response;
        if (usersInfo.length > 0) {
          const user = usersInfo[0];
          response = await EmailPassword.sendResetPasswordEmail(
            "public",
            user.id,
            args.email,
          );
        } else {
          // For security, we still return success even if user doesn't exist
          response = { status: "OK" };
        }

        const success = response.status === "OK";

        context.logSecurityEvent("forgot_password_requested", {
          email: args.email,
          ipAddress: context.security.ipAddress,
          success,
          status: response.status,
        });

        if (response.status === "UNKNOWN_USER_ID_ERROR") {
          // For security, don't reveal if user exists or not
          return true;
        }

        return success;
      } catch (error) {
        context.logSecurityEvent("forgot_password_error", {
          error: error instanceof Error ? error.message : String(error),
          email: args.email,
          ipAddress: context.security.ipAddress,
        });

        throw new GraphQLError("Failed to process password reset request");
      }
    },

    /**
     * Reset password with token - public endpoint
     */
    resetPassword: async (
      _: any,
      args: MutationResetPasswordArgs,
      context: Context,
    ): Promise<AuthResponse> => {
      try {
        // Basic input validation
        if (!args.token || !args.newPassword) {
          context.logSecurityEvent("reset_password_invalid_input", {
            hasToken: !!args.token,
            hasPassword: !!args.newPassword,
            ipAddress: context.security.ipAddress,
          });
          return {
            success: false,
            error: {
              code: "INVALID_INPUT",
              message: "Token and new password are required",
            },
          };
        }

        // Basic password validation
        if (args.newPassword.length < 8) {
          return {
            success: false,
            error: {
              code: "WEAK_PASSWORD",
              message: "Password must be at least 8 characters long",
            },
          };
        }

        // Reset password using SuperTokens API
        const response = await EmailPassword.resetPasswordUsingToken(
          "public",
          args.token,
          args.newPassword,
        );

        const success = response.status === "OK";

        context.logSecurityEvent("reset_password_attempted", {
          hasToken: !!args.token,
          ipAddress: context.security.ipAddress,
          success,
          status: response.status,
        });

        if (response.status === "OK") {
          return {
            success: true,
          };
        } else if (response.status === "RESET_PASSWORD_INVALID_TOKEN_ERROR") {
          return {
            success: false,
            error: {
              code: "INVALID_TOKEN",
              message: "Password reset token is invalid or expired",
            },
          };
        } else if (response.status === "PASSWORD_POLICY_VIOLATED_ERROR") {
          return {
            success: false,
            error: {
              code: "PASSWORD_POLICY_VIOLATED",
              message:
                response.failureReason ||
                "Password does not meet policy requirements",
            },
          };
        } else {
          return {
            success: false,
            error: {
              code: "RESET_PASSWORD_ERROR",
              message: "Failed to reset password",
            },
          };
        }
      } catch (error) {
        context.logSecurityEvent("reset_password_error", {
          error: error instanceof Error ? error.message : String(error),
          ipAddress: context.security.ipAddress,
        });

        return {
          success: false,
          error: {
            code: "RESET_PASSWORD_ERROR",
            message: "Failed to reset password",
          },
        };
      }
    },

    /**
     * Change password for authenticated user
     */
    changePassword: async (
      _: any,
      args: MutationChangePasswordArgs,
      context: Context,
    ): Promise<boolean> => {
      try {
        requireAuth(context);

        // Basic input validation
        if (!args.newPassword) {
          context.logSecurityEvent("change_password_invalid_input", {
            userId: context.user!.id,
            hasNewPassword: !!args.newPassword,
          });
          throw new GraphQLError("New password is required");
        }

        // Password strength validation
        if (args.newPassword.length < 8) {
          context.logSecurityEvent("change_password_weak_password", {
            userId: context.user!.id,
            passwordLength: args.newPassword.length,
          });
          throw new GraphQLError("Password must be at least 8 characters long");
        }

        // Get user ID from session
        const supertokensUserId = context.session?.userId;
        if (!supertokensUserId) {
          throw new GraphQLError("Session user ID not found");
        }

        // Update password using SuperTokens API
        const recipeUserId = convertToRecipeUserId(supertokensUserId);

        const response = await EmailPassword.updateEmailOrPassword({
          recipeUserId,
          password: args.newPassword,
          tenantIdForPasswordPolicy: "public",
        });

        const success = response.status === "OK";

        context.logSecurityEvent("change_password_attempted", {
          userId: context.user!.id,
          supertokensUserId,
          success,
          status: response.status,
        });

        if (!success) {
          if (response.status === "UNKNOWN_USER_ID_ERROR") {
            throw new GraphQLError("User not found");
          } else if (response.status === "PASSWORD_POLICY_VIOLATED_ERROR") {
            throw new GraphQLError(
              response.failureReason ||
                "Password does not meet policy requirements",
            );
          } else {
            throw new GraphQLError("Failed to change password");
          }
        }

        return success;
      } catch (error) {
        context.logSecurityEvent("change_password_error", {
          error: error instanceof Error ? error.message : String(error),
          userId: context.user?.id,
        });

        if (error instanceof GraphQLError) {
          throw error;
        }

        throw new GraphQLError("Failed to change password");
      }
    },
  },
};
