import type { Metadata } from "next";
import "./globals.css";
import Navigation from "@/components/Navigation";
import { AppProviders } from "@/providers/AppProviders";

export const metadata: Metadata = {
  title: "PulsePanel Frontend",
  description: "The frontend for PulsePanel",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body style={{ minHeight: "100vh", backgroundColor: "#f3f4f6" }}>
        <AppProviders>
          <Navigation />
          <main
            style={{ maxWidth: "1200px", margin: "0 auto", padding: "1rem" }}
          >
            {children}
          </main>
        </AppProviders>
      </body>
    </html>
  );
}
