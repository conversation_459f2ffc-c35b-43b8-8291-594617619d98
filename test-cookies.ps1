# Cookie Debug Test Script for Windows PowerShell
# Тестирует установку и чтение HttpOnly куки SuperTokens

Write-Host "🧪 Testing SuperTokens HttpOnly Cookies" -ForegroundColor Cyan
Write-Host "=======================================" -ForegroundColor Cyan

# Проверяем, что сервер запущен
Write-Host "📡 Checking if BFF server is running..." -ForegroundColor Yellow
try {
    $healthCheck = Invoke-WebRequest -Uri "http://localhost:4000/health" -Method GET -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ BFF server is running" -ForegroundColor Green
} catch {
    Write-Host "❌ BFF server is not running on port 4000" -ForegroundColor Red
    Write-Host "   Please start the BFF server first: npm run dev:bff" -ForegroundColor Yellow
    exit 1
}

Write-Host ""

# Создаем сессию для хранения куки
$session = New-Object Microsoft.PowerShell.Commands.WebRequestSession

# Тест 1: Создание сессии
Write-Host "🔐 Test 1: Creating session with SuperTokens..." -ForegroundColor Yellow
Write-Host "POST http://localhost:4000/bff-auth/signin"

$signinBody = @{
    formFields = @(
        @{ id = "email"; value = "<EMAIL>" }
        @{ id = "password"; value = "testpassword123" }
    )
} | ConvertTo-Json -Depth 3

try {
    $signinResponse = Invoke-WebRequest -Uri "http://localhost:4000/bff-auth/signin" `
        -Method POST `
        -ContentType "application/json" `
        -Body $signinBody `
        -WebSession $session `
        -ErrorAction Stop

    Write-Host "Response Status: $($signinResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Response: $($signinResponse.Content)"
    
    # Проверяем установленные куки
    Write-Host ""
    Write-Host "🍪 Checking cookies set by SuperTokens..." -ForegroundColor Yellow
    
    $cookieCount = $session.Cookies.Count
    Write-Host "Total cookies in session: $cookieCount"
    
    $hasAccessToken = $false
    $hasRefreshToken = $false
    $hasFrontToken = $false
    
    foreach ($cookie in $session.Cookies) {
        Write-Host "Cookie: $($cookie.Name) = $($cookie.Value.Substring(0, [Math]::Min(20, $cookie.Value.Length)))..."
        
        if ($cookie.Name -eq "sAccessToken") { $hasAccessToken = $true }
        if ($cookie.Name -eq "sRefreshToken") { $hasRefreshToken = $true }
        if ($cookie.Name -eq "sFrontToken") { $hasFrontToken = $true }
    }
    
    if ($hasAccessToken) {
        Write-Host "✅ sAccessToken cookie found" -ForegroundColor Green
    } else {
        Write-Host "❌ sAccessToken cookie NOT found" -ForegroundColor Red
    }
    
    if ($hasRefreshToken) {
        Write-Host "✅ sRefreshToken cookie found" -ForegroundColor Green
    } else {
        Write-Host "❌ sRefreshToken cookie NOT found" -ForegroundColor Red
    }
    
    if ($hasFrontToken) {
        Write-Host "✅ sFrontToken cookie found" -ForegroundColor Green
    } else {
        Write-Host "❌ sFrontToken cookie NOT found" -ForegroundColor Red
    }

} catch {
    Write-Host "❌ Signin failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Тест 2: Проверка сессии
Write-Host "🔍 Test 2: Verifying session..." -ForegroundColor Yellow
Write-Host "POST http://localhost:4000/graphql (with cookies)"

$graphqlBody = @{
    query = "query { checkAuthStatus }"
} | ConvertTo-Json

try {
    $graphqlResponse = Invoke-WebRequest -Uri "http://localhost:4000/graphql" `
        -Method POST `
        -ContentType "application/json" `
        -Body $graphqlBody `
        -WebSession $session `
        -ErrorAction Stop

    Write-Host "GraphQL Response Status: $($graphqlResponse.StatusCode)" -ForegroundColor Green
    Write-Host "GraphQL Response: $($graphqlResponse.Content)"

} catch {
    Write-Host "❌ GraphQL request failed: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Тест 3: Проверка middleware
Write-Host "🛡️ Test 3: Testing frontend middleware..." -ForegroundColor Yellow
Write-Host "GET http://localhost:3000/dashboard (with cookies)"

try {
    # Извлекаем куки из сессии для передачи на frontend
    $cookieHeader = ""
    foreach ($cookie in $session.Cookies) {
        if ($cookieHeader -ne "") { $cookieHeader += "; " }
        $cookieHeader += "$($cookie.Name)=$($cookie.Value)"
    }
    
    $middlewareResponse = Invoke-WebRequest -Uri "http://localhost:3000/dashboard" `
        -Method GET `
        -Headers @{ "Cookie" = $cookieHeader } `
        -ErrorAction Stop

    Write-Host "Middleware Response Status: $($middlewareResponse.StatusCode)" -ForegroundColor Green
    Write-Host "Middleware Response Length: $($middlewareResponse.Content.Length) characters"

} catch {
    Write-Host "❌ Middleware test failed: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   This might be expected if frontend is not running"
}

Write-Host ""
Write-Host "✅ Cookie test completed!" -ForegroundColor Green
Write-Host ""
Write-Host "📋 Summary:" -ForegroundColor Cyan
Write-Host "   - If you see sAccessToken cookie: ✅ HttpOnly cookies are working" -ForegroundColor Green
Write-Host "   - If you see only sFrontToken: ❌ Using header mode instead of cookies" -ForegroundColor Red
Write-Host "   - If no cookies: ❌ Authentication failed or cookies not set" -ForegroundColor Red
Write-Host ""
Write-Host "🔧 Troubleshooting:" -ForegroundColor Cyan
Write-Host "   1. Make sure SuperTokens core is running (port 3567)" -ForegroundColor Yellow
Write-Host "   2. Check BFF logs for cookie debug information" -ForegroundColor Yellow
Write-Host "   3. Verify environment variables are set correctly" -ForegroundColor Yellow
