import { GraphQLResolveInfo } from 'graphql';
import { User as PrismaUser } from '@pulsepanel/db/generated/client';
import { Context } from '../types/context';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
export type Omit<T, K extends keyof T> = Pick<T, Exclude<keyof T, K>>;
export type RequireFields<T, K extends keyof T> = Omit<T, K> & { [P in K]-?: NonNullable<T[P]> };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
};

export type AssignRoleInput = {
  role: Scalars['String']['input'];
  tenantId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type AuthError = {
  __typename?: 'AuthError';
  code: Scalars['String']['output'];
  field?: Maybe<Scalars['String']['output']>;
  message: Scalars['String']['output'];
};

export type AuthResponse = {
  __typename?: 'AuthResponse';
  error?: Maybe<AuthError>;
  sessionDuration?: Maybe<Scalars['Int']['output']>;
  sessionInfo?: Maybe<SessionInfo>;
  success: Scalars['Boolean']['output'];
  user?: Maybe<User>;
};

export type CreateRoleInput = {
  permissions?: InputMaybe<Array<Scalars['String']['input']>>;
  role: Scalars['String']['input'];
};

export type CreateRoleResponse = {
  __typename?: 'CreateRoleResponse';
  createdNewRole?: Maybe<Scalars['Boolean']['output']>;
  error?: Maybe<AuthError>;
  success: Scalars['Boolean']['output'];
};

export type DeviceInfo = {
  __typename?: 'DeviceInfo';
  deviceType?: Maybe<Scalars['String']['output']>;
  ipAddress?: Maybe<Scalars['String']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  userAgent?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  assignUserRole: RoleResponse;
  changePassword: Scalars['Boolean']['output'];
  createRole: CreateRoleResponse;
  forgotPassword: Scalars['Boolean']['output'];
  refreshToken: AuthResponse;
  removeUserRole: RoleResponse;
  resetPassword: AuthResponse;
  signIn: AuthResponse;
  signOut: Scalars['Boolean']['output'];
  signUp: AuthResponse;
  updateUserMetadata: Scalars['Boolean']['output'];
};


export type MutationAssignUserRoleArgs = {
  input: AssignRoleInput;
};


export type MutationChangePasswordArgs = {
  newPassword: Scalars['String']['input'];
  oldPassword: Scalars['String']['input'];
};


export type MutationCreateRoleArgs = {
  input: CreateRoleInput;
};


export type MutationForgotPasswordArgs = {
  email: Scalars['String']['input'];
};


export type MutationRemoveUserRoleArgs = {
  input: RemoveRoleInput;
};


export type MutationResetPasswordArgs = {
  newPassword: Scalars['String']['input'];
  token: Scalars['String']['input'];
};


export type MutationSignInArgs = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
  rememberMe?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationSignUpArgs = {
  input: UserRegistrationInput;
};


export type MutationUpdateUserMetadataArgs = {
  metadata: UserMetadataInput;
};

export type Project = {
  __typename?: 'Project';
  createdAt: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  isActive: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  owner: User;
  ownerId: Scalars['String']['output'];
  tenantId: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type Query = {
  __typename?: 'Query';
  checkAuthStatus: Scalars['Boolean']['output'];
  checkResourceOwnership: ResourceOwnershipResponse;
  getUserRoles: UserRolesResponse;
  getUsersWithRole: UsersWithRoleResponse;
  hello?: Maybe<Scalars['String']['output']>;
  me?: Maybe<User>;
  project?: Maybe<Project>;
  projects: Array<Project>;
  sessionInfo?: Maybe<SessionInfo>;
  user?: Maybe<User>;
  users: Array<User>;
};


export type QueryCheckResourceOwnershipArgs = {
  resourceId: Scalars['String']['input'];
  resourceType: Scalars['String']['input'];
};


export type QueryGetUserRolesArgs = {
  tenantId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type QueryGetUsersWithRoleArgs = {
  role: Scalars['String']['input'];
  tenantId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryProjectArgs = {
  id: Scalars['ID']['input'];
};


export type QueryUserArgs = {
  id: Scalars['ID']['input'];
};

export type RemoveRoleInput = {
  role: Scalars['String']['input'];
  tenantId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type ResourceOwnershipResponse = {
  __typename?: 'ResourceOwnershipResponse';
  error?: Maybe<AuthError>;
  hasAccess: Scalars['Boolean']['output'];
  reason?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type RoleResponse = {
  __typename?: 'RoleResponse';
  didUserAlreadyHaveRole?: Maybe<Scalars['Boolean']['output']>;
  didUserHaveRole?: Maybe<Scalars['Boolean']['output']>;
  error?: Maybe<AuthError>;
  success: Scalars['Boolean']['output'];
};

export type SecurityMetadata = {
  __typename?: 'SecurityMetadata';
  lastLoginAt?: Maybe<Scalars['String']['output']>;
  loginAttempts: Scalars['Int']['output'];
  riskScore: Scalars['Float']['output'];
  suspiciousActivity: Scalars['Boolean']['output'];
};

export type SessionInfo = {
  __typename?: 'SessionInfo';
  deviceInfo?: Maybe<DeviceInfo>;
  expiresAt: Scalars['String']['output'];
  issuedAt: Scalars['String']['output'];
  roles: Array<Scalars['String']['output']>;
  securityMetadata?: Maybe<SecurityMetadata>;
  sessionHandle: Scalars['String']['output'];
  tenantId?: Maybe<Scalars['String']['output']>;
  userId: Scalars['String']['output'];
};

export type User = {
  __typename?: 'User';
  createdAt: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  roles?: Maybe<Array<Scalars['String']['output']>>;
  tenantId: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type UserMetadataInput = {
  avatar_url?: InputMaybe<Scalars['String']['input']>;
  first_name?: InputMaybe<Scalars['String']['input']>;
  last_name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type UserRegistrationInput = {
  email: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  password: Scalars['String']['input'];
  tenantId: Scalars['String']['input'];
};

export type UserRolesResponse = {
  __typename?: 'UserRolesResponse';
  error?: Maybe<AuthError>;
  roles?: Maybe<Array<Scalars['String']['output']>>;
  success: Scalars['Boolean']['output'];
};

export type UsersWithRoleResponse = {
  __typename?: 'UsersWithRoleResponse';
  error?: Maybe<AuthError>;
  success: Scalars['Boolean']['output'];
  users?: Maybe<Array<Scalars['String']['output']>>;
};



export type ResolverTypeWrapper<T> = Promise<T> | T;


export type ResolverWithResolve<TResult, TParent, TContext, TArgs> = {
  resolve: ResolverFn<TResult, TParent, TContext, TArgs>;
};
export type Resolver<TResult, TParent = {}, TContext = {}, TArgs = {}> = ResolverFn<TResult, TParent, TContext, TArgs> | ResolverWithResolve<TResult, TParent, TContext, TArgs>;

export type ResolverFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => Promise<TResult> | TResult;

export type SubscriptionSubscribeFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => AsyncIterable<TResult> | Promise<AsyncIterable<TResult>>;

export type SubscriptionResolveFn<TResult, TParent, TContext, TArgs> = (
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;

export interface SubscriptionSubscriberObject<TResult, TKey extends string, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<{ [key in TKey]: TResult }, TParent, TContext, TArgs>;
  resolve?: SubscriptionResolveFn<TResult, { [key in TKey]: TResult }, TContext, TArgs>;
}

export interface SubscriptionResolverObject<TResult, TParent, TContext, TArgs> {
  subscribe: SubscriptionSubscribeFn<any, TParent, TContext, TArgs>;
  resolve: SubscriptionResolveFn<TResult, any, TContext, TArgs>;
}

export type SubscriptionObject<TResult, TKey extends string, TParent, TContext, TArgs> =
  | SubscriptionSubscriberObject<TResult, TKey, TParent, TContext, TArgs>
  | SubscriptionResolverObject<TResult, TParent, TContext, TArgs>;

export type SubscriptionResolver<TResult, TKey extends string, TParent = {}, TContext = {}, TArgs = {}> =
  | ((...args: any[]) => SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>)
  | SubscriptionObject<TResult, TKey, TParent, TContext, TArgs>;

export type TypeResolveFn<TTypes, TParent = {}, TContext = {}> = (
  parent: TParent,
  context: TContext,
  info: GraphQLResolveInfo
) => Maybe<TTypes> | Promise<Maybe<TTypes>>;

export type IsTypeOfResolverFn<T = {}, TContext = {}> = (obj: T, context: TContext, info: GraphQLResolveInfo) => boolean | Promise<boolean>;

export type NextResolverFn<T> = () => Promise<T>;

export type DirectiveResolverFn<TResult = {}, TParent = {}, TContext = {}, TArgs = {}> = (
  next: NextResolverFn<TResult>,
  parent: TParent,
  args: TArgs,
  context: TContext,
  info: GraphQLResolveInfo
) => TResult | Promise<TResult>;



/** Mapping between all available schema types and the resolvers types */
export type ResolversTypes = {
  AssignRoleInput: AssignRoleInput;
  AuthError: ResolverTypeWrapper<AuthError>;
  AuthResponse: ResolverTypeWrapper<Omit<AuthResponse, 'user'> & { user?: Maybe<ResolversTypes['User']> }>;
  Boolean: ResolverTypeWrapper<Scalars['Boolean']['output']>;
  CreateRoleInput: CreateRoleInput;
  CreateRoleResponse: ResolverTypeWrapper<CreateRoleResponse>;
  DeviceInfo: ResolverTypeWrapper<DeviceInfo>;
  Float: ResolverTypeWrapper<Scalars['Float']['output']>;
  ID: ResolverTypeWrapper<Scalars['ID']['output']>;
  Int: ResolverTypeWrapper<Scalars['Int']['output']>;
  Mutation: ResolverTypeWrapper<{}>;
  Project: ResolverTypeWrapper<Omit<Project, 'owner'> & { owner: ResolversTypes['User'] }>;
  Query: ResolverTypeWrapper<{}>;
  RemoveRoleInput: RemoveRoleInput;
  ResourceOwnershipResponse: ResolverTypeWrapper<ResourceOwnershipResponse>;
  RoleResponse: ResolverTypeWrapper<RoleResponse>;
  SecurityMetadata: ResolverTypeWrapper<SecurityMetadata>;
  SessionInfo: ResolverTypeWrapper<SessionInfo>;
  String: ResolverTypeWrapper<Scalars['String']['output']>;
  User: ResolverTypeWrapper<PrismaUser>;
  UserMetadataInput: UserMetadataInput;
  UserRegistrationInput: UserRegistrationInput;
  UserRolesResponse: ResolverTypeWrapper<UserRolesResponse>;
  UsersWithRoleResponse: ResolverTypeWrapper<UsersWithRoleResponse>;
};

/** Mapping between all available schema types and the resolvers parents */
export type ResolversParentTypes = {
  AssignRoleInput: AssignRoleInput;
  AuthError: AuthError;
  AuthResponse: Omit<AuthResponse, 'user'> & { user?: Maybe<ResolversParentTypes['User']> };
  Boolean: Scalars['Boolean']['output'];
  CreateRoleInput: CreateRoleInput;
  CreateRoleResponse: CreateRoleResponse;
  DeviceInfo: DeviceInfo;
  Float: Scalars['Float']['output'];
  ID: Scalars['ID']['output'];
  Int: Scalars['Int']['output'];
  Mutation: {};
  Project: Omit<Project, 'owner'> & { owner: ResolversParentTypes['User'] };
  Query: {};
  RemoveRoleInput: RemoveRoleInput;
  ResourceOwnershipResponse: ResourceOwnershipResponse;
  RoleResponse: RoleResponse;
  SecurityMetadata: SecurityMetadata;
  SessionInfo: SessionInfo;
  String: Scalars['String']['output'];
  User: PrismaUser;
  UserMetadataInput: UserMetadataInput;
  UserRegistrationInput: UserRegistrationInput;
  UserRolesResponse: UserRolesResponse;
  UsersWithRoleResponse: UsersWithRoleResponse;
};

export type AuthenticatedDirectiveArgs = { };

export type AuthenticatedDirectiveResolver<Result, Parent, ContextType = Context, Args = AuthenticatedDirectiveArgs> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type BelongsToTenantDirectiveArgs = { };

export type BelongsToTenantDirectiveResolver<Result, Parent, ContextType = Context, Args = BelongsToTenantDirectiveArgs> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type HasRoleDirectiveArgs = {
  role: Scalars['String']['input'];
};

export type HasRoleDirectiveResolver<Result, Parent, ContextType = Context, Args = HasRoleDirectiveArgs> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type RateLimitDirectiveArgs = {
  max: Scalars['Int']['input'];
  window: Scalars['Int']['input'];
};

export type RateLimitDirectiveResolver<Result, Parent, ContextType = Context, Args = RateLimitDirectiveArgs> = DirectiveResolverFn<Result, Parent, ContextType, Args>;

export type AuthErrorResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AuthError'] = ResolversParentTypes['AuthError']> = {
  code?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  field?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  message?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type AuthResponseResolvers<ContextType = Context, ParentType extends ResolversParentTypes['AuthResponse'] = ResolversParentTypes['AuthResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['AuthError']>, ParentType, ContextType>;
  sessionDuration?: Resolver<Maybe<ResolversTypes['Int']>, ParentType, ContextType>;
  sessionInfo?: Resolver<Maybe<ResolversTypes['SessionInfo']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type CreateRoleResponseResolvers<ContextType = Context, ParentType extends ResolversParentTypes['CreateRoleResponse'] = ResolversParentTypes['CreateRoleResponse']> = {
  createdNewRole?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  error?: Resolver<Maybe<ResolversTypes['AuthError']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type DeviceInfoResolvers<ContextType = Context, ParentType extends ResolversParentTypes['DeviceInfo'] = ResolversParentTypes['DeviceInfo']> = {
  deviceType?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  ipAddress?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  location?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  userAgent?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type MutationResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Mutation'] = ResolversParentTypes['Mutation']> = {
  assignUserRole?: Resolver<ResolversTypes['RoleResponse'], ParentType, ContextType, RequireFields<MutationAssignUserRoleArgs, 'input'>>;
  changePassword?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationChangePasswordArgs, 'newPassword' | 'oldPassword'>>;
  createRole?: Resolver<ResolversTypes['CreateRoleResponse'], ParentType, ContextType, RequireFields<MutationCreateRoleArgs, 'input'>>;
  forgotPassword?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationForgotPasswordArgs, 'email'>>;
  refreshToken?: Resolver<ResolversTypes['AuthResponse'], ParentType, ContextType>;
  removeUserRole?: Resolver<ResolversTypes['RoleResponse'], ParentType, ContextType, RequireFields<MutationRemoveUserRoleArgs, 'input'>>;
  resetPassword?: Resolver<ResolversTypes['AuthResponse'], ParentType, ContextType, RequireFields<MutationResetPasswordArgs, 'newPassword' | 'token'>>;
  signIn?: Resolver<ResolversTypes['AuthResponse'], ParentType, ContextType, RequireFields<MutationSignInArgs, 'email' | 'password' | 'rememberMe'>>;
  signOut?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  signUp?: Resolver<ResolversTypes['AuthResponse'], ParentType, ContextType, RequireFields<MutationSignUpArgs, 'input'>>;
  updateUserMetadata?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType, RequireFields<MutationUpdateUserMetadataArgs, 'metadata'>>;
};

export type ProjectResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Project'] = ResolversParentTypes['Project']> = {
  createdAt?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  description?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  isActive?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  name?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  owner?: Resolver<ResolversTypes['User'], ParentType, ContextType>;
  ownerId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  tenantId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  updatedAt?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type QueryResolvers<ContextType = Context, ParentType extends ResolversParentTypes['Query'] = ResolversParentTypes['Query']> = {
  checkAuthStatus?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  checkResourceOwnership?: Resolver<ResolversTypes['ResourceOwnershipResponse'], ParentType, ContextType, RequireFields<QueryCheckResourceOwnershipArgs, 'resourceId' | 'resourceType'>>;
  getUserRoles?: Resolver<ResolversTypes['UserRolesResponse'], ParentType, ContextType, RequireFields<QueryGetUserRolesArgs, 'tenantId' | 'userId'>>;
  getUsersWithRole?: Resolver<ResolversTypes['UsersWithRoleResponse'], ParentType, ContextType, RequireFields<QueryGetUsersWithRoleArgs, 'role' | 'tenantId'>>;
  hello?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  me?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType>;
  project?: Resolver<Maybe<ResolversTypes['Project']>, ParentType, ContextType, RequireFields<QueryProjectArgs, 'id'>>;
  projects?: Resolver<Array<ResolversTypes['Project']>, ParentType, ContextType>;
  sessionInfo?: Resolver<Maybe<ResolversTypes['SessionInfo']>, ParentType, ContextType>;
  user?: Resolver<Maybe<ResolversTypes['User']>, ParentType, ContextType, RequireFields<QueryUserArgs, 'id'>>;
  users?: Resolver<Array<ResolversTypes['User']>, ParentType, ContextType>;
};

export type ResourceOwnershipResponseResolvers<ContextType = Context, ParentType extends ResolversParentTypes['ResourceOwnershipResponse'] = ResolversParentTypes['ResourceOwnershipResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['AuthError']>, ParentType, ContextType>;
  hasAccess?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  reason?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type RoleResponseResolvers<ContextType = Context, ParentType extends ResolversParentTypes['RoleResponse'] = ResolversParentTypes['RoleResponse']> = {
  didUserAlreadyHaveRole?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  didUserHaveRole?: Resolver<Maybe<ResolversTypes['Boolean']>, ParentType, ContextType>;
  error?: Resolver<Maybe<ResolversTypes['AuthError']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SecurityMetadataResolvers<ContextType = Context, ParentType extends ResolversParentTypes['SecurityMetadata'] = ResolversParentTypes['SecurityMetadata']> = {
  lastLoginAt?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  loginAttempts?: Resolver<ResolversTypes['Int'], ParentType, ContextType>;
  riskScore?: Resolver<ResolversTypes['Float'], ParentType, ContextType>;
  suspiciousActivity?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type SessionInfoResolvers<ContextType = Context, ParentType extends ResolversParentTypes['SessionInfo'] = ResolversParentTypes['SessionInfo']> = {
  deviceInfo?: Resolver<Maybe<ResolversTypes['DeviceInfo']>, ParentType, ContextType>;
  expiresAt?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  issuedAt?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  roles?: Resolver<Array<ResolversTypes['String']>, ParentType, ContextType>;
  securityMetadata?: Resolver<Maybe<ResolversTypes['SecurityMetadata']>, ParentType, ContextType>;
  sessionHandle?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  tenantId?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  userId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserResolvers<ContextType = Context, ParentType extends ResolversParentTypes['User'] = ResolversParentTypes['User']> = {
  createdAt?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  id?: Resolver<ResolversTypes['ID'], ParentType, ContextType>;
  name?: Resolver<Maybe<ResolversTypes['String']>, ParentType, ContextType>;
  roles?: Resolver<Maybe<Array<ResolversTypes['String']>>, ParentType, ContextType>;
  tenantId?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  updatedAt?: Resolver<ResolversTypes['String'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UserRolesResponseResolvers<ContextType = Context, ParentType extends ResolversParentTypes['UserRolesResponse'] = ResolversParentTypes['UserRolesResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['AuthError']>, ParentType, ContextType>;
  roles?: Resolver<Maybe<Array<ResolversTypes['String']>>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type UsersWithRoleResponseResolvers<ContextType = Context, ParentType extends ResolversParentTypes['UsersWithRoleResponse'] = ResolversParentTypes['UsersWithRoleResponse']> = {
  error?: Resolver<Maybe<ResolversTypes['AuthError']>, ParentType, ContextType>;
  success?: Resolver<ResolversTypes['Boolean'], ParentType, ContextType>;
  users?: Resolver<Maybe<Array<ResolversTypes['String']>>, ParentType, ContextType>;
  __isTypeOf?: IsTypeOfResolverFn<ParentType, ContextType>;
};

export type Resolvers<ContextType = Context> = {
  AuthError?: AuthErrorResolvers<ContextType>;
  AuthResponse?: AuthResponseResolvers<ContextType>;
  CreateRoleResponse?: CreateRoleResponseResolvers<ContextType>;
  DeviceInfo?: DeviceInfoResolvers<ContextType>;
  Mutation?: MutationResolvers<ContextType>;
  Project?: ProjectResolvers<ContextType>;
  Query?: QueryResolvers<ContextType>;
  ResourceOwnershipResponse?: ResourceOwnershipResponseResolvers<ContextType>;
  RoleResponse?: RoleResponseResolvers<ContextType>;
  SecurityMetadata?: SecurityMetadataResolvers<ContextType>;
  SessionInfo?: SessionInfoResolvers<ContextType>;
  User?: UserResolvers<ContextType>;
  UserRolesResponse?: UserRolesResponseResolvers<ContextType>;
  UsersWithRoleResponse?: UsersWithRoleResponseResolvers<ContextType>;
};

export type DirectiveResolvers<ContextType = Context> = {
  authenticated?: AuthenticatedDirectiveResolver<any, any, ContextType>;
  belongsToTenant?: BelongsToTenantDirectiveResolver<any, any, ContextType>;
  hasRole?: HasRoleDirectiveResolver<any, any, ContextType>;
  rateLimit?: RateLimitDirectiveResolver<any, any, ContextType>;
};
