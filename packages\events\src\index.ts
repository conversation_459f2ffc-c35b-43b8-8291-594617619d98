export interface EventEnvelope<T = unknown> {
  id: string; // ULID, e.g., generated by ulid()
  type: string; // Event type, e.g., "user.created.v1"
  timestamp: string; // ISO-8601 string
  source?: string; // Optional: service that originated the event
  payload: T;
}

export type Unsubscribe = () => Promise<void>;

export interface EventBus {
  publish<E>(topic: string, message: EventEnvelope<E>): Promise<void>;
  subscribe<E>(
    topic: string,
    handler: (message: EventEnvelope<E>) => Promise<void>,
    options?: { consumerTag?: string; concurrency?: number },
  ): Promise<Unsubscribe>;
  disconnect(): Promise<void>; // For graceful shutdown
}

import { createNoopBus } from "./noopBus";
import { createRabbitBus } from "./rabbitBus";

export type EventBusConfig =
  | { type: "noop" }
  | { type: "rabbitmq"; rabbitUrl: string; defaultExchange?: string };

/**
 * Creates an EventBus instance based on the provided configuration.
 * @param config - The configuration for the EventBus.
 * @returns An EventBus instance.
 */
export function createEventBus(config: EventBusConfig): EventBus {
  if (config.type === "rabbitmq" && config.rabbitUrl) {
    return createRabbitBus(
      config.rabbitUrl,
      config.defaultExchange || "pulse.topic",
    );
  }
  return createNoopBus();
}

// Re-export the individual factory functions for more direct usage if needed
export { createNoopBus, createRabbitBus };
