"use client";

/**
 * Auth Test Component
 *
 * Simple component to test the auth service layer functionality.
 * This demonstrates how UI components interact with the auth service
 * without knowing about the underlying transport (GraphQL, REST, etc.)
 *
 */

import React, { useState } from "react";
import { useAuth, useAuthActions } from "@/hooks/features";
import { useAuthServiceState } from "../providers/AuthServiceProvider";
import type { AuthResult } from "../interfaces/IAuthService";

export function AuthTest() {
  const auth = useAuth();
  const authActions = useAuthActions();
  const { authService, error: serviceError } = useAuthServiceState();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Test credentials for demo
  const [email, setEmail] = useState("<EMAIL>");
  const [password, setPassword] = useState("<EMAIL>");

  const handleSignIn = async () => {
    if (!authService) return;

    try {
      setLoading(true);
      setError(null);

      const result: AuthResult = await authService.signIn({
        email,
        password,
        rememberMe: false,
      });

      if (result.success && result.user) {
        console.log("Sign in successful:", result);
      } else {
        setError(result.error?.message || "Sign in failed");
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Sign in error");
    } finally {
      setLoading(false);
    }
  };

  const handleSignOut = async () => {
    try {
      setLoading(true);
      setError(null);

      await authActions.signOut();
      console.log("Sign out successful");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Sign out error");
    } finally {
      setLoading(false);
    }
  };

  const handleGetUserRoles = async () => {
    if (!authService) return;

    try {
      setLoading(true);
      const roles = await authService.getUserRoles();
      console.log("User roles:", roles);
      alert(`User roles: ${roles.join(", ") || "No roles"}`);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to get roles");
    } finally {
      setLoading(false);
    }
  };

  const handleRefreshStatus = async () => {
    try {
      setLoading(true);
      setError(null);
      await authActions.checkAuthStatus();
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to refresh status");
    } finally {
      setLoading(false);
    }
  };

  if (!auth.isInitialized || serviceError) {
    return (
      <div
        style={{ padding: "20px", border: "1px solid #ccc", margin: "20px" }}
      >
        <h3>Auth Service Test</h3>
        {serviceError ? (
          <p style={{ color: "red" }}>Service error: {serviceError}</p>
        ) : (
          <p>Initializing auth service...</p>
        )}
      </div>
    );
  }

  return (
    <div style={{ padding: "20px", border: "1px solid #ccc", margin: "20px" }}>
      <h3>Auth Service Test</h3>

      {/* Показываем ошибки из провайдера или локальные */}
      {(auth.authError || error) && (
        <div style={{ color: "red", marginBottom: "10px" }}>
          Error: {auth.authError || error}
        </div>
      )}

      {auth.isCheckingAuth ? (
        <div style={{ textAlign: "center", padding: "20px" }}>
          <p>🔍 Checking authentication status...</p>
          <div style={{ fontSize: "12px", color: "#666" }}>
            Verifying your session with the server
          </div>
        </div>
      ) : (
        <>
          <div style={{ marginBottom: "20px" }}>
            <strong>Status:</strong>{" "}
            {auth.isAuthenticated ? "Authenticated" : "Not authenticated"}
            {auth.user && (
              <div>
                <strong>User:</strong> {auth.user.name || "No name"} (ID:{" "}
                {auth.user.id})
              </div>
            )}
          </div>

          {!auth.isAuthenticated ? (
            <div>
              <h4>Sign In</h4>
              <div style={{ marginBottom: "10px" }}>
                <input
                  type="email"
                  placeholder="Email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  style={{ marginRight: "10px", padding: "5px" }}
                />
                <input
                  type="password"
                  placeholder="Password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  style={{ marginRight: "10px", padding: "5px" }}
                />
                <button
                  onClick={handleSignIn}
                  disabled={loading}
                  style={{ padding: "5px 10px" }}
                >
                  {loading ? "Signing in..." : "Sign In"}
                </button>
              </div>
            </div>
          ) : (
            <div>
              <h4>Authenticated Actions</h4>
              <button
                onClick={handleSignOut}
                disabled={loading}
                style={{ marginRight: "10px", padding: "5px 10px" }}
              >
                {loading ? "Signing out..." : "Sign Out"}
              </button>
              <button
                onClick={handleGetUserRoles}
                disabled={loading}
                style={{ marginRight: "10px", padding: "5px 10px" }}
              >
                Get Roles
              </button>
              <button
                onClick={handleRefreshStatus}
                disabled={loading}
                style={{ padding: "5px 10px" }}
              >
                Refresh Status
              </button>
            </div>
          )}

          <div style={{ marginTop: "20px", fontSize: "12px", color: "#666" }}>
            <p>✅ This component reads auth state from Redux store.</p>
            <p>✅ No local auth state duplication across 200+ components.</p>
            <p>✅ Single source of truth for authentication status.</p>
          </div>
        </>
      )}
    </div>
  );
}
