import type { Request, Response, NextFunction } from "express";
import type { Redis } from "ioredis";
import Session from "supertokens-node/recipe/session/index.js";
import { SessionCacheService } from "./sessionCache.js";
import {
  extractSessionId,
  createSessionCacheKey,
  isPublicEndpoint,
} from "./utils.js";
import type { SessionData, AuthContext } from "./types.js";

/**
 * Creates a Redis-optimized auth middleware
 */
export const createAuthMiddleware = (redis: Redis) => {
  const sessionCache = new SessionCacheService(redis);

  return async (
    req: Request & { authContext?: AuthContext },
    res: Response,
    next: NextFunction,
  ) => {
    const startTime = Date.now();

    console.log("🔐 Auth Middleware: Processing request", {
      path: req.path,
      method: req.method,
      hasAuthContext: !!req.authContext,
      cookieHeader: req.headers.cookie ? "present" : "missing",
    });

    try {
      // Check if the endpoint is public
      if (isPublicEndpoint(req)) {
        console.log("🔐 Auth Middleware: Public endpoint, skipping auth");
        req.authContext = { isAuthenticated: false };
        return next();
      }

      // Extract client info for security analysis
      const ipAddress =
        (req.headers["x-forwarded-for"] as string)?.split(",")[0] ||
        req.socket.remoteAddress ||
        "unknown";
      const userAgent = req.headers["user-agent"] || "unknown";

      // ✅ ИСПРАВЛЕНО: SuperTokens читает HttpOnly куки автоматически
      let sessionData: SessionData | null = null;
      let fromCache = false;
      let sessionHandle: string | null = null;

      try {
        // SuperTokens сам читает HttpOnly куки и проверяет сессию
        console.log(
          "🔐 Auth Middleware: Calling SuperTokens Session.getSession",
        );
        const session = await Session.getSession(req, res, {
          sessionRequired: false,
          checkDatabase: true,
          // CSRF protection enabled
        });
        console.log("🔐 Auth Middleware: SuperTokens session result", {
          hasSession: !!session,
        });

        if (session) {
          sessionHandle = session.getHandle();

          // Пытаемся получить из Redis кеша
          sessionData = await sessionCache.getSession(sessionHandle);
          fromCache = !!sessionData;

          if (!sessionData) {
            // Cache miss - создаем sessionData из SuperTokens
            const accessTokenPayload = session.getAccessTokenPayload();
            sessionData = {
              userId: session.getUserId(),
              tenantId: session.getTenantId(),
              userDataInJWT: accessTokenPayload,
              sessionHandle: session.getHandle(),
              roles: accessTokenPayload.roles || [],
            };

            // Кешируем валидную сессию
            await sessionCache.setSession(sessionHandle, sessionData, 300);

            // Инициализируем security metadata для новых сессий
            await sessionCache.initializeSecurityMetadata(
              sessionData.userId,
              sessionHandle,
              ipAddress,
              userAgent,
            );
          }
        }
      } catch (error) {
        // SuperTokens verification failed
        console.log("🔐 SuperTokens session verification failed:", error);
        req.authContext = { isAuthenticated: false, error };
        return next();
      }

      if (sessionData && sessionHandle) {
        // Perform security check for existing sessions
        const isSecure = await sessionCache.performSecurityCheck(
          sessionHandle,
          sessionData.userId,
          ipAddress,
          userAgent,
        );

        if (!isSecure) {
          // Session was invalidated due to security violation
          req.authContext = {
            isAuthenticated: false,
            error: "Session invalidated due to security violation",
          };
          return next();
        }

        // Enforce concurrent session limits
        await sessionCache.enforceConcurrentSessionLimit(sessionData.userId, 5);

        // Update auth context with valid session
        req.authContext = {
          isAuthenticated: true,
          session: sessionData,
          userId: sessionData.userId,
          tenantId: sessionData.tenantId,
          fromCache,
        };
      } else {
        req.authContext = { isAuthenticated: false };
      }

      // Performance logging
      const duration = Date.now() - startTime;
      if (process.env.NODE_ENV === "development") {
        console.log(
          `🔐 Auth: ${duration}ms (${fromCache ? "cache hit" : "cache miss"}) for ${sessionData?.userId || "anonymous"}`,
        );
      }

      next();
    } catch (error) {
      console.error("Auth middleware error:", error);
      req.authContext = { isAuthenticated: false, error };
      next();
    }
  };
};

/**
 * Guard for requiring authentication
 */
export function requireAuth() {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.authContext?.isAuthenticated) {
      return res.status(401).json({
        error: "Authentication required",
        message: "You must be logged in to access this resource",
      });
    }
    next();
  };
}

/**
 * Guard for checking roles
 */
export function requireRole(role: string) {
  return (req: Request, res: Response, next: NextFunction) => {
    if (!req.authContext?.isAuthenticated) {
      return res.status(401).json({
        error: "Authentication required",
        message: "You must be logged in to access this resource",
      });
    }

    const userRoles = req.authContext.session?.roles || [];
    if (!userRoles.includes(role)) {
      return res.status(403).json({
        error: "Insufficient permissions",
        message: `Role '${role}' is required to access this resource`,
      });
    }

    next();
  };
}

/**
 * Middleware for logging auth statistics
 */
export const createAuthStatsMiddleware = (redis: Redis) => {
  const sessionCache = new SessionCacheService(redis);

  return async (req: Request, res: Response, next: NextFunction) => {
    // Note: req.path for middleware mounted on "/auth" will contain path WITHOUT "/auth" prefix
    // Example: GET /auth/stats -> req.path = "/stats"

    if (req.path === "/stats") {
      try {
        const [cacheStats, healthStats] = await Promise.all([
          sessionCache.getCacheStats(),
          sessionCache.getSessionHealthStats(),
        ]);

        return res.json({
          cache: cacheStats,
          health: healthStats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error("Auth stats error:", error);
        return res.status(500).json({ error: "Failed to get auth stats" });
      }
    }

    if (req.path === "/audit") {
      try {
        const [events, stats] = await Promise.all([
          sessionCache.getAuditEvents(100),
          sessionCache.getAuditStats(7),
        ]);

        return res.json({
          events,
          stats,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error("Auth audit error:", error);
        return res.status(500).json({ error: "Failed to get audit data" });
      }
    }

    if (req.path === "/cleanup" && req.method === "POST") {
      try {
        const result = await sessionCache.performAutomaticCleanup();
        return res.json({
          message: "Cleanup completed",
          result,
          timestamp: new Date().toISOString(),
        });
      } catch (error) {
        console.error("Auth cleanup error:", error);
        return res.status(500).json({ error: "Failed to perform cleanup" });
      }
    }

    next();
  };
};
