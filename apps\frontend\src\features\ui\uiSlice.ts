import {
  createSlice,
  createSelector,
  type PayloadAction,
} from "@reduxjs/toolkit";

// UI State Interface
export interface UIState {
  // Navigation state
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;

  // Modal state
  activeModal: string | null;
  modalData: Record<string, any>;

  // Theme state
  theme: "light" | "dark" | "system";

  // Loading states
  globalLoading: boolean;
  loadingMessage: string | null;

  // Notification state
  notifications: Array<{
    id: string;
    type: "success" | "error" | "warning" | "info";
    message: string;
    timeout?: number;
  }>;

  // Page state
  pageTitle: string;
  breadcrumbs: Array<{ label: string; href?: string }>;
}

// Initial state
const initialState: UIState = {
  sidebarOpen: true,
  sidebarCollapsed: false,
  activeModal: null,
  modalData: {},
  theme: "system",
  globalLoading: false,
  loadingMessage: null,
  notifications: [],
  pageTitle: "PulsePanel",
  breadcrumbs: [],
};

// UI Slice with modern RTK 2.0 syntax
export const uiSlice = createSlice({
  name: "ui",
  initialState,
  reducers: {
    // Sidebar actions
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    toggleSidebarCollapse: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },

    // Modal actions
    openModal: (
      state,
      action: PayloadAction<{ modalId: string; data?: any }>,
    ) => {
      state.activeModal = action.payload.modalId;
      state.modalData = action.payload.data || {};
    },
    closeModal: (state) => {
      state.activeModal = null;
      state.modalData = {};
    },

    // Theme actions
    setTheme: (state, action: PayloadAction<"light" | "dark" | "system">) => {
      state.theme = action.payload;
    },

    // Loading actions
    setGlobalLoading: (
      state,
      action: PayloadAction<{ loading: boolean; message?: string }>,
    ) => {
      state.globalLoading = action.payload.loading;
      state.loadingMessage = action.payload.message || null;
    },

    // Notification actions
    addNotification: (
      state,
      action: PayloadAction<Omit<UIState["notifications"][0], "id">>,
    ) => {
      const notification = {
        ...action.payload,
        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      };
      state.notifications.push(notification);
    },
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(
        (n) => n.id !== action.payload,
      );
    },
    clearNotifications: (state) => {
      state.notifications = [];
    },

    // Page actions
    setPageTitle: (state, action: PayloadAction<string>) => {
      state.pageTitle = action.payload;
    },
    setBreadcrumbs: (state, action: PayloadAction<UIState["breadcrumbs"]>) => {
      state.breadcrumbs = action.payload;
    },
  },
});

// Export actions
export const {
  toggleSidebar,
  setSidebarOpen,
  toggleSidebarCollapse,
  openModal,
  closeModal,
  setTheme,
  setGlobalLoading,
  addNotification,
  removeNotification,
  clearNotifications,
  setPageTitle,
  setBreadcrumbs,
} = uiSlice.actions;

// Base selectors
const selectUISlice = (state: { ui: UIState }) => state.ui;

// Memoized selectors using createSelector
export const selectSidebarState = createSelector([selectUISlice], (ui) => ({
  open: ui.sidebarOpen,
  collapsed: ui.sidebarCollapsed,
}));

export const selectModalState = createSelector([selectUISlice], (ui) => ({
  activeModal: ui.activeModal,
  data: ui.modalData,
}));

export const selectTheme = createSelector([selectUISlice], (ui) => ui.theme);

export const selectLoadingState = createSelector([selectUISlice], (ui) => ({
  loading: ui.globalLoading,
  message: ui.loadingMessage,
}));

export const selectNotifications = createSelector(
  [selectUISlice],
  (ui) => ui.notifications,
);

export const selectPageInfo = createSelector([selectUISlice], (ui) => ({
  title: ui.pageTitle,
  breadcrumbs: ui.breadcrumbs,
}));

// Export reducer
export default uiSlice.reducer;
