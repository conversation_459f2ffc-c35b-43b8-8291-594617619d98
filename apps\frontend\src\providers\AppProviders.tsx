"use client";

import { ReactNode } from "react";
import { StoreProvider } from "./StoreProvider";
import { AuthServiceProvider } from "@/auth/providers/AuthServiceProvider";
import { TransportProvider } from "@/auth/providers/TransportProvider";

interface AppProvidersProps {
  children: ReactNode;
}

export function AppProviders({ children }: AppProvidersProps) {
  return (
    <StoreProvider>
      <TransportProvider>
        <AuthServiceProvider>{children}</AuthServiceProvider>
      </TransportProvider>
    </StoreProvider>
  );
}

export function withAppProviders<P extends object>(
  Component: React.ComponentType<P>,
) {
  return function AppProvidersWrapper(props: P) {
    return (
      <AppProviders>
        <Component {...props} />
      </AppProviders>
    );
  };
}
