import { makeExecutableSchema } from "@graphql-tools/schema";
import { readFileSync } from "fs";
import { applySecurityDirectives } from "../auth/directives.js";
import type { Context } from "../types/context.js";

import accountResolvers from "../resolvers/account.resolver.js";
import authResolvers from "../resolvers/auth.resolver.js";

// Read GraphQL schema
const typeDefs = readFileSync("./src/schema/schema.graphql", "utf-8");

// Combine all resolvers
const resolvers = {
  Query: {
    ...accountResolvers.Query,
    ...authResolvers.Query,

    // Auth status check - public endpoint that safely checks authentication
    checkAuthStatus: async (_: any, __: any, context: Context) => {
      // This resolver intentionally has NO @authenticated directive
      // It safely returns boolean without exposing sensitive data
      return !!context.user && !!context.session;
    },
  },

  Mutation: {
    ...authResolvers.Mutation,
  },
};

/**
 * Builds executable GraphQL schema with security directives
 */
export function buildSecureSchema() {
  // Create base executable schema
  const baseSchema = makeExecutableSchema({
    typeDefs,
    resolvers,
  });

  return applySecurityDirectives(baseSchema);
}
