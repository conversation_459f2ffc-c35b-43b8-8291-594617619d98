import { EventBus, createEventBus, EventBusConfig } from "./index";

// Define types for NestJS dependencies to avoid requiring them as direct dependencies
// These will be provided by the consuming application
type NestJSProvider = {
  provide: string | symbol;
  useFactory: (...args: any[]) => any;
  inject?: any[];
};

type NestJSConfigService = {
  get<T>(key: string): T | undefined;
};

/**
 * Token for injecting the EventBus instance in NestJS applications
 */
export const EVENT_BUS_PROVIDER_TOKEN = "EVENT_BUS";

/**
 * NestJS provider for EventBus
 *
 * Usage in a module:
 * ```typescript
 * @Module({
 *   imports: [ConfigModule],
 *   providers: [eventBusProvider],
 *   exports: [EVENT_BUS_PROVIDER_TOKEN],
 * })
 * export class EventBusModule {}
 * ```
 *
 * Usage in a service:
 * ```typescript
 * @Injectable()
 * export class MyService {
 *   constructor(
 *     @Inject(EVENT_BUS_PROVIDER_TOKEN)
 *     private readonly eventBus: EventBus,
 *   ) {}
 * }
 * ```
 */
export const eventBusProvider: NestJSProvider = {
  provide: EVENT_BUS_PROVIDER_TOKEN,
  useFactory: (configService?: NestJSConfigService) => {
    const rabbitUrl =
      process.env.RABBITMQ_URL || configService?.get<string>("RABBITMQ_URL");
    const nodeEnv =
      process.env.NODE_ENV || configService?.get<string>("NODE_ENV");

    const busConfig: EventBusConfig =
      nodeEnv === "test" || !rabbitUrl
        ? { type: "noop" }
        : {
            type: "rabbitmq",
            rabbitUrl,
            defaultExchange:
              process.env.RABBITMQ_EXCHANGE ||
              configService?.get<string>("RABBITMQ_EXCHANGE") ||
              "pulse.topic",
          };

    const bus = createEventBus(busConfig);

    // Handle graceful shutdown via NestJS lifecycle hooks
    return bus;
  },
  inject: ["ConfigService"], // Using string token instead of the actual class
};

/**
 * NestJS module for EventBus
 *
 * Usage:
 * ```typescript
 * @Module({
 *   imports: [
 *     ConfigModule.forRoot(),
 *     EventBusModule.register(),
 *   ],
 * })
 * export class AppModule {}
 * ```
 */
/**
 * NestJS module for EventBus
 *
 * This is a placeholder class that demonstrates how to use the EventBus in a NestJS application.
 * In a real application, you would import the actual NestJS Module decorator and use it.
 */
export class EventBusModule {
  /**
   * Register the EventBus module
   *
   * In a real NestJS application, you would use this method to configure the module.
   */
  static register() {
    return {
      module: EventBusModule,
      providers: [eventBusProvider],
      exports: [EVENT_BUS_PROVIDER_TOKEN],
    };
  }
}
