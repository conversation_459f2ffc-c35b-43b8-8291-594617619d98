import {
  getUserMetadata,
  updateUserMetadata,
} from "supertokens-node/recipe/usermetadata/index.js";
import type { Redis } from "ioredis";

/**
 * SuperTokens User Metadata with Redis Caching
 *
 * Кеширует metadata пользователей из SuperTokens для производительности.
 * Обновляет кеш при каждом запросе (refresh-ahead strategy).
 */

export interface UserMetadata {
  first_name?: string;
  last_name?: string;
  avatar_url?: string;
  phone?: string;
  [key: string]: any;
}

export interface CachedUserData {
  name: string | null;
  metadata: UserMetadata;
  lastUpdated: number;
}

export class UserMetadataService {
  private redis: Redis;
  private readonly CACHE_TTL = 900; // 15 minutes
  private readonly REFRESH_THRESHOLD = 60000; // 1 minute

  constructor(redis: Redis) {
    this.redis = redis;
  }

  /**
   * Get user display name with caching and refresh-ahead strategy
   */
  async getUserDisplayName(supertokensUserId: string): Promise<string | null> {
    const cacheKey = `user_meta:${supertokensUserId}`;
    const lastRefreshKey = `last_refresh:${supertokensUserId}`;

    try {
      // Check if we should refresh (don't block on this)
      const shouldRefresh = await this.shouldRefreshMetadata(lastRefreshKey);
      if (shouldRefresh) {
        // Start async refresh (fire and forget)
        this.refreshUserMetadataAsync(
          supertokensUserId,
          cacheKey,
          lastRefreshKey,
        ).catch((error) =>
          console.warn(
            `Failed to refresh metadata for user ${supertokensUserId}:`,
            error,
          ),
        );
      }

      // Return cached data if available
      const cached = await this.getCachedUserData(cacheKey);
      if (cached) {
        return cached.name;
      }

      // No cache - fetch synchronously (first time or cache miss)
      console.log(
        `📝 UserMetadata: Cache miss for user ${supertokensUserId}, fetching from SuperTokens`,
      );
      const result = await this.fetchAndCacheUserName(
        supertokensUserId,
        cacheKey,
        lastRefreshKey,
      );
      console.log(`📝 UserMetadata: fetchAndCacheUserName returned:`, result);
      return result;
    } catch (error) {
      console.error(
        `Error getting user display name for ${supertokensUserId}:`,
        error,
      );
      return null;
    }
  }

  /**
   * Get full user metadata (for admin/profile views)
   */
  async getUserMetadata(supertokensUserId: string): Promise<UserMetadata> {
    try {
      console.log(
        `📝 UserMetadata: Fetching metadata for user ${supertokensUserId}`,
      );
      const metadata = await getUserMetadata(supertokensUserId);
      console.log(`📝 UserMetadata: Raw metadata response:`, metadata);
      return (metadata.metadata as UserMetadata) || {};
    } catch (error) {
      console.error(
        `Error fetching metadata for user ${supertokensUserId}:`,
        error,
      );
      return {};
    }
  }

  /**
   * Update user metadata in SuperTokens and invalidate cache
   */
  async updateUserMetadata(
    supertokensUserId: string,
    metadata: Partial<UserMetadata>,
  ): Promise<boolean> {
    try {
      await updateUserMetadata(supertokensUserId, metadata);

      // Invalidate cache
      const cacheKey = `user_meta:${supertokensUserId}`;
      const lastRefreshKey = `last_refresh:${supertokensUserId}`;
      await Promise.all([
        this.redis.del(cacheKey),
        this.redis.del(lastRefreshKey),
      ]);

      console.log(
        `📝 UserMetadata: Updated and invalidated cache for user ${supertokensUserId}`,
      );
      return true;
    } catch (error) {
      console.error(
        `Error updating metadata for user ${supertokensUserId}:`,
        error,
      );
      return false;
    }
  }

  // Private helper methods

  private async shouldRefreshMetadata(
    lastRefreshKey: string,
  ): Promise<boolean> {
    try {
      const lastRefresh = await this.redis.get(lastRefreshKey);
      if (!lastRefresh) return true;

      const timeSinceRefresh = Date.now() - parseInt(lastRefresh);
      return timeSinceRefresh > this.REFRESH_THRESHOLD;
    } catch (error) {
      return true; // Refresh on error
    }
  }

  private async getCachedUserData(
    cacheKey: string,
  ): Promise<CachedUserData | null> {
    try {
      const cached = await this.redis.get(cacheKey);
      return cached ? JSON.parse(cached) : null;
    } catch (error) {
      console.warn(`Failed to parse cached user data:`, error);
      return null;
    }
  }

  private async refreshUserMetadataAsync(
    supertokensUserId: string,
    cacheKey: string,
    lastRefreshKey: string,
  ): Promise<void> {
    console.log(
      `🔄 UserMetadata: Refreshing cache for user ${supertokensUserId}`,
    );

    try {
      const metadata = await this.getUserMetadata(supertokensUserId);
      const displayName = this.buildDisplayName(metadata);

      const cachedData: CachedUserData = {
        name: displayName,
        metadata,
        lastUpdated: Date.now(),
      };

      await Promise.all([
        this.redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(cachedData)),
        this.redis.set(lastRefreshKey, Date.now().toString()),
      ]);

      console.log(
        `✅ UserMetadata: Refreshed cache for user ${supertokensUserId}, name: "${displayName}"`,
      );
    } catch (error) {
      console.error(
        `Failed to refresh metadata for user ${supertokensUserId}:`,
        error,
      );
    }
  }

  private async fetchAndCacheUserName(
    supertokensUserId: string,
    cacheKey: string,
    lastRefreshKey: string,
  ): Promise<string | null> {
    try {
      const metadata = await this.getUserMetadata(supertokensUserId);
      const displayName = this.buildDisplayName(metadata);

      const cachedData: CachedUserData = {
        name: displayName,
        metadata,
        lastUpdated: Date.now(),
      };

      // Cache the result
      await Promise.all([
        this.redis.setex(cacheKey, this.CACHE_TTL, JSON.stringify(cachedData)),
        this.redis.set(lastRefreshKey, Date.now().toString()),
      ]);

      console.log(
        `✅ UserMetadata: Fetched and cached for user ${supertokensUserId}, name: "${displayName}"`,
      );
      return displayName;
    } catch (error) {
      console.error(
        `Error fetching and caching user name for ${supertokensUserId}:`,
        error,
      );
      return null;
    }
  }

  private buildDisplayName(metadata: UserMetadata): string | null {
    const { first_name, last_name } = metadata;

    if (first_name && last_name) {
      return `${first_name} ${last_name}`.trim();
    }

    if (first_name) {
      return first_name.trim();
    }

    if (last_name) {
      return last_name.trim();
    }

    return null;
  }
}
