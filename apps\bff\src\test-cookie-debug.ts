/**
 * <PERSON>ie Debug Test Script
 * 
 * Этот скрипт тестирует установку и чтение HttpOnly куки SuperTokens
 */

import express from "express";
import { initSuperTokens } from "./auth/supertokens.js";
import EmailPassword from "supertokens-node/recipe/emailpassword/index.js";
import Session from "supertokens-node/recipe/session/index.js";
import { convertToRecipeUserId } from "supertokens-node";

// Инициализируем SuperTokens
initSuperTokens();

const app = express();
app.use(express.json());

// Middleware для логирования куки
app.use((req, res, next) => {
  console.log("📥 Incoming Request:", {
    method: req.method,
    url: req.url,
    cookies: req.headers.cookie || "No cookies",
    headers: Object.keys(req.headers),
  });
  
  // Перехватываем setHeader для логирования установки куки
  const originalSetHeader = res.setHeader;
  res.setHeader = function(name: string, value: any) {
    if (name.toLowerCase().includes('cookie') || name.toLowerCase() === 'set-cookie') {
      console.log("🍪 Setting Cookie:", { name, value });
    }
    return originalSetHeader.call(this, name, value);
  };
  
  next();
});

// Тестовый endpoint для создания сессии
app.post("/test-signin", async (req, res) => {
  try {
    console.log("🔐 Testing SuperTokens Session Creation...");
    
    // Создаем тестовую сессию
    const session = await Session.createNewSession(
      req,
      res,
      "public",
      convertToRecipeUserId("test-user-id"),
      {
        email: "<EMAIL>",
        tenantId: "public",
        roles: ["user"],
        rememberMe: false,
      },
      {},
      {
        accessTokenValidity: 15 * 60 * 1000, // 15 minutes
        refreshTokenValidity: 7 * 24 * 60 * 60 * 1000, // 7 days
      }
    );

    console.log("✅ Session Created:", {
      sessionHandle: session.getHandle(),
      userId: session.getUserId(),
      accessTokenPayload: session.getAccessTokenPayload(),
    });

    // Проверяем установленные заголовки
    const responseHeaders = res.getHeaders();
    console.log("📤 Response Headers:", responseHeaders);

    res.json({
      success: true,
      sessionHandle: session.getHandle(),
      userId: session.getUserId(),
      message: "Session created successfully",
      cookiesSet: Object.keys(responseHeaders).filter(h => 
        h.toLowerCase().includes('cookie')
      ),
    });

  } catch (error) {
    console.error("❌ Session Creation Error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

// Тестовый endpoint для проверки сессии
app.get("/test-session", async (req, res) => {
  try {
    console.log("🔍 Testing Session Verification...");
    
    const session = await Session.getSession(req, res, {
      sessionRequired: false,
      checkDatabase: true,
    });

    if (session) {
      console.log("✅ Session Found:", {
        sessionHandle: session.getHandle(),
        userId: session.getUserId(),
        accessTokenPayload: session.getAccessTokenPayload(),
      });

      res.json({
        success: true,
        authenticated: true,
        sessionHandle: session.getHandle(),
        userId: session.getUserId(),
        accessTokenPayload: session.getAccessTokenPayload(),
      });
    } else {
      console.log("❌ No Session Found");
      res.json({
        success: true,
        authenticated: false,
        message: "No session found",
      });
    }

  } catch (error) {
    console.error("❌ Session Verification Error:", error);
    res.status(500).json({
      success: false,
      error: error instanceof Error ? error.message : String(error),
    });
  }
});

// Endpoint для анализа куки
app.get("/test-cookies", (req, res) => {
  const cookies = req.headers.cookie || "";
  const cookieArray = cookies.split(";").map(c => c.trim()).filter(Boolean);
  
  const analysis = {
    totalCookies: cookieArray.length,
    allCookies: cookieArray.map(c => {
      const [name, ...valueParts] = c.split("=");
      return {
        name: name.trim(),
        hasValue: valueParts.length > 0,
        valueLength: valueParts.join("=").length,
      };
    }),
    superTokensCookies: cookieArray.filter(c => 
      c.startsWith("sAccessToken=") ||
      c.startsWith("sRefreshToken=") ||
      c.startsWith("sFrontToken=") ||
      c.startsWith("sIdRefreshToken=")
    ),
  };

  console.log("🍪 Cookie Analysis:", analysis);

  res.json({
    success: true,
    analysis,
    rawCookieHeader: cookies,
  });
});

const PORT = process.env.PORT || 4001;

app.listen(PORT, () => {
  console.log(`🧪 Cookie Debug Server running on port ${PORT}`);
  console.log(`📋 Test endpoints:`);
  console.log(`   POST http://localhost:${PORT}/test-signin - Create session`);
  console.log(`   GET  http://localhost:${PORT}/test-session - Check session`);
  console.log(`   GET  http://localhost:${PORT}/test-cookies - Analyze cookies`);
  console.log(`\n🔧 Usage:`);
  console.log(`   1. POST to /test-signin to create a session`);
  console.log(`   2. GET to /test-session to verify the session`);
  console.log(`   3. GET to /test-cookies to analyze cookie headers`);
});
