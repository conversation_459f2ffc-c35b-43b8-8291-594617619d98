# Instagram Integration Plan

**Goal:** Enable users to connect their Instagram accounts (multiple per user) to PulsePanel via OAuth 2.0, fetch basic profile information, and lay the groundwork for future Instagram-related features. This integration will be a direct API integration.

**Official Documentation:** The user-provided link for integration is [Instagram Graph API - Instagram Login](https://developers.facebook.com/docs/instagram-platform/instagram-api-with-instagram-login). This API allows access to user profiles and media for non-business accounts and will be the basis for this plan.

**Phase 1: Core Deliverables (Initial Implementation)**

This phase focuses on the essential steps to establish the Instagram connection and verify it, as per your request:

1.  **Frontend: Menu Item & Initiation**
    *   Create a new menu item "Instagram" (or "Connect Instagram") within the user's account settings or a dedicated "Integrations" section in the Frontend (`apps/frontend`).
    *   Clicking this item will trigger a request to a BFF endpoint to start the Instagram OAuth 2.0 authorization flow.

2.  **BFF: OAuth 2.0 Flow & Token Acquisition**
    *   **OAuth Initiation Endpoint (e.g., `GET /api/v1/auth/instagram/connect`):**
        *   Generate a unique `state` parameter for CSRF protection. Store this `state` temporarily (e.g., in a Redis-backed session or a short-lived Redis key associated with the user).
        *   Redirect the user to the Instagram Authorization URL: `https://api.instagram.com/oauth/authorize`.
        *   Parameters: `client_id` (your Instagram App ID), `redirect_uri` (your registered callback URL), `scope=user_profile`, `response_type=code`, `state`.
    *   **OAuth Callback Endpoint (e.g., `GET /api/v1/auth/instagram/callback`):**
        *   Instagram redirects the user here with an `authorization_code` and the `state`.
        *   Verify the received `state` against the one stored. If invalid, abort.
        *   Exchange the `authorization_code` for a short-lived access token by making a POST request to `https://api.instagram.com/oauth/access_token`.
        *   **Recommended:** Exchange the short-lived token for a long-lived token using `https://graph.instagram.com/access_token?grant_type=ig_exchange_token...`. Long-lived tokens are valid for 60 days and can be refreshed.

3.  **BFF & Database: Token Storage**
    *   Securely store the obtained access token (preferably the long-lived one) in the `InstagramConnection` table in your PostgreSQL database. This token should be associated with the PulsePanel user account.
    *   The `accessToken` field in the `InstagramConnection` model should be encrypted at rest.
    *   Utilize Redis for temporary storage of the OAuth `state` parameter during the authorization flow, mirroring the Shopify integration's use of Redis for session/state management (e.g., similar to online token or session state storage).

4.  **BFF & Core SDK: Fetch Basic User Info & Console Log**
    *   Using the obtained (long-lived) access token, make a GET request to the Instagram Graph API's `/me` endpoint: `https://graph.instagram.com/me?fields=id,username&access_token={access-token}`.
    *   **Log the fetched `id` (Instagram User ID) and `username` to the BFF's console.** This serves as an initial verification that the connection and token are working.
    *   Store the `instagramUserId` (from the `id` field) and `username` in the `InstagramConnection` table alongside the token.
    *   After successful connection and data retrieval, redirect the user back to a relevant page in the frontend (e.g., a settings page indicating a successful connection).

---


**Guiding Principle: Leverage Existing Shopify Integration Patterns**

This plan will heavily draw inspiration from and align with the existing Shopify integration to ensure consistency, reusability, and faster development. Key aspects like the Core SDK structure (`ShopifyClient`), BFF OAuth flow, token management (DB for persistent tokens, Redis for session/state), error handling (`handleShopifyError`, OTel/Sentry), and frontend component design will serve as primary references.

**Inspired by:** The robust and established patterns of the existing Shopify integration, particularly its OAuth flow, SDK structure, token management, and error handling. The goal is to replicate these successful patterns for Instagram, allowing multiple Instagram connections per user, similar to how multiple Shopify stores are handled.

**Key Components Involved:**

* **Database (`packages/db`):** Storing Instagram access tokens and basic profile identifiers, allowing multiple Instagram connections per PulsePanel account.
* **Core SDK (`packages/core-sdk`):** A new `InstagramClient` module for interacting with the Instagram Graph API, structured similarly to the `ShopifyClient`. This includes considering `p-queue` for rate limiting if applicable to Instagram API, and robust error handling.
* **BFF (`apps/bff`):** Handling the Instagram OAuth flow, token storage (long-lived tokens in DB, OAuth state in Redis), and proxying requests to the Instagram API via the `core-sdk`. The OAuth logic will mirror the Shopify implementation closely. Adjusting logic to support multiple Instagram connections per user.
* **Frontend (`apps/frontend`):** UI for initiating connections, listing connected accounts, and displaying profile information for selected accounts.

## 1. Database Schema (`packages/db/prisma/schema.prisma`)

* **Create a new Prisma model `InstagramConnection`:**
  * This model will be linked to the existing `Account` model, allowing one `Account` to have many `InstagramConnection` records.
  * It will store:
    * `id` (Primary Key)
    * `instagramUserId` (String, identifier from Instagram)
    * `username` (String, Instagram username)
    * `accessToken` (String, the OAuth access token – consider encryption needs)
    * `profilePictureUrl` (String, optional, URL of the profile picture)
    * `accountId` (String, Foreign Key to `Account`)
    * `createdAt`, `updatedAt` (Timestamps)
  * **Example:**

        ```prisma
        model InstagramConnection {
          id                String   @id @default(cuid())
          instagramUserId   String
          username          String
          accessToken       String   // Ensure this is stored securely
          profilePictureUrl String?
          createdAt         DateTime @default(now())
          updatedAt         DateTime @updatedAt
          accountId         String
          account           Account  @relation(fields: [accountId], references: [id], onDelete: Cascade)

          @@unique([accountId, instagramUserId], name: "unique_account_instagram_user") // Prevents connecting the same Instagram account multiple times to the same PulsePanel account
          @@index([instagramUserId])
        }
        ```

* **Update `Account` model:** Add a list relation to `InstagramConnection`.

    ```prisma
    model Account {
      // ... existing fields
      stores               Store[]
      instagramConnections InstagramConnection[] // An account can have multiple Instagram connections
    }
    ```

## 2. Core SDK (`@pulsepanel/core-sdk`)

A new module within the `core-sdk`, `InstagramClient`, will be responsible for all direct communication with the Instagram Graph API. Its design and structure should be heavily influenced by the existing `ShopifyClient` to ensure consistency.

*   **Consider `p-queue`:** If the Instagram Graph API has rate limits that would benefit from client-side queueing, implement `p-queue` similarly to its use in `ShopifyClient`.

*   **New Class:** `InstagramClient`
*   **Configuration:**
    *   The client will be initialized with an Instagram App ID, App Secret, and registered Redirect URI.
    *   `constructor(config: { clientId: string, clientSecret: string, redirectUri: string })`
*   **Methods:**
    1.  `getAuthorizationUrl(state: string): string`
        *   Constructs the Instagram OAuth authorization URL.
        *   URL: `https://api.instagram.com/oauth/authorize`
        *   Parameters: `client_id`, `redirect_uri`, `scope=user_profile`, `response_type=code`, `state`.
    2.  `exchangeCodeForTokens(code: string): Promise<{ accessToken: string, longLivedAccessToken?: string, userId: string }>`
        *   **Step 1: Get Short-Lived Token:** Exchanges the authorization `code` for a short-lived access token and user ID.
            *   POST to `https://api.instagram.com/oauth/access_token`
            *   Body: `client_id`, `client_secret`, `grant_type=authorization_code`, `redirect_uri`, `code`.
            *   Response includes `access_token` (short-lived) and `user_id`.
        *   **Step 2: Get Long-Lived Token (Recommended):** Exchanges the short-lived token for a long-lived token.
            *   GET to `https://graph.instagram.com/access_token`
            *   Parameters: `grant_type=ig_exchange_token`, `client_secret`, `access_token` (short-lived).
            *   Response includes `access_token` (long-lived) and `expires_in`.
        *   The method should return the `userId` and ideally the `longLivedAccessToken`. If long-lived exchange fails, it might return the short-lived one as a fallback for Phase 1, but this should be clearly documented.
    3.  `getUserProfile(accessToken: string): Promise<{ id: string, username: string }>`
        *   Fetches basic user profile information (`id` and `username`).
        *   GET to `https://graph.instagram.com/me?fields=id,username&access_token={accessToken}`.
*   **Error Handling:**
    *   Implement robust error handling for API request failures, unexpected responses, and network issues.
    *   Use custom error classes (e.g., `InstagramApiError`, `InstagramOAuthError`) for better error identification.
    *   Develop a utility function `handleInstagramError(error): SpecificErrorType` analogous to `handleShopifyError`, to standardize error processing from the Instagram API.
    *   Include logging for requests and responses (especially errors).

## 3. BFF (`apps/bff`)

The BFF will orchestrate the OAuth flow, manage tokens, and interact with both the `core-sdk` and the database, closely following the patterns established by the Shopify integration.

*   **Environment Variables:**
    *   `INSTAGRAM_APP_ID`
    *   `INSTAGRAM_APP_SECRET`
    *   `INSTAGRAM_REDIRECT_URI` (must match the one registered with the Instagram App and used in SDK)
    *   `REDIS_URL` (for storing OAuth state, consistent with Redis usage for Shopify session/state)
    *   `ACCESS_TOKEN_ENCRYPTION_KEY` (for encrypting tokens in DB)
*   **Dependencies:**
    *   `@pulsepanel/core-sdk` (for the new `InstagramClient`)
    *   `ioredis` (or your existing Redis client)
    *   `@prisma/client`
    *   Node.js `crypto` module (for token encryption)
*   **API Endpoints:**
    1.  `GET /api/v1/auth/instagram/connect` (Protected: User must be authenticated with PulsePanel)
        *   Generate a cryptographically secure unique `state` string.
        *   Store the `state` in Redis, associated with the authenticated PulsePanel user ID (e.g., key `instagram_oauth_state:${pulsePanelUserId}:${state}` with a short TTL like 10 minutes). This approach should mirror how session state or temporary OAuth data is handled for Shopify (e.g., for online tokens or CSRF protection in OAuth).
        *   Instantiate `InstagramClient`.
        *   Call `instagramClient.getAuthorizationUrl(state)` to get the Instagram authorization URL.
        *   Redirect the user to this URL.
    2.  `GET /api/v1/auth/instagram/callback`
        *   Receives `code` and `state` from Instagram (or `error` if something went wrong on Instagram's side).
        *   **Error Handling:** If `error` parameter is present, log it and redirect user to a frontend error page.
        *   **State Validation:** Retrieve the expected `state` from Redis using the PulsePanel user's context (e.g., from session, or if `state` itself contains a temporary user identifier to look up the full state key). Compare with the received `state`. If mismatch or not found, abort (CSRF attempt) and redirect to an error page. Delete the `state` from Redis immediately after validation.
        *   Instantiate `InstagramClient`.
        *   Call `instagramClient.exchangeCodeForTokens(code)` to get `accessToken` (preferably long-lived) and `instagramUserId`.
        *   If successful, call `instagramClient.getUserProfile(accessToken)` to fetch `username`.
        *   **Token Encryption:** Encrypt the `accessToken` using `crypto` before storing.
        *   **Database:** Store `instagramUserId`, `username`, and the encrypted `accessToken` (long-lived) in the `InstagramConnection` table, linked to the authenticated PulsePanel `accountId`. This is analogous to storing Shopify offline access tokens.
        *   **Console Log:** Log the fetched `instagramUserId` and `username` to the BFF console (as per Phase 1 requirement).
        *   Redirect user to a success page on the frontend (e.g., `/settings/integrations?instagram_connected=true`).
*   **Token Encryption/Decryption Utility:**
    *   Create a simple utility service/functions using Node.js `crypto` (e.g., AES-256-GCM) to encrypt tokens before DB storage and decrypt them when needed by the SDK. The encryption key will be an environment variable.

## 4. Frontend (`apps/frontend`)

The frontend will provide the user interface to initiate the Instagram connection.

*   **UI Component:**
    *   Add an "Instagram" or "Connect Instagram Account" button/link within the user's account settings page or a dedicated "Integrations" section.
*   **Initiating Connection:**
    *   On button/link click, the frontend makes a GET request (or navigates directly) to the BFF's `/api/v1/auth/instagram/connect` endpoint. This will trigger the redirect to Instagram.
*   **Callback Handling & User Feedback:**
    *   The BFF will redirect the user back to a predefined frontend route after the callback processing (e.g., `/settings/integrations/status?source=instagram&success=true` or `...&success=false&error_code=...`).
    *   The frontend page at this route should display a success or failure message to the user based on the query parameters.
    *   For Phase 1, simply confirming the connection status is sufficient. No Instagram data needs to be displayed in the UI yet. However, any UI elements for initiating or managing connections should follow the design patterns established in the Shopify integration (e.g., button styles, layout in settings, as seen in `apps/frontend/src/app/store/[id]/page.tsx` for Shopify store management).

## 5. Security Considerations

*   **CSRF Protection:** The `state` parameter in the OAuth 2.0 flow is critical and must be properly generated, stored, and validated.
*   **Token Security:**
    *   **Encryption at Rest:** Instagram access tokens stored in the database MUST be encrypted.
    *   **HTTPS:** All communication involving tokens (frontend to BFF, BFF to Instagram) MUST use HTTPS.
    *   **Secrets Management:** `INSTAGRAM_APP_SECRET`, `ACCESS_TOKEN_ENCRYPTION_KEY`, and other sensitive credentials must be stored securely (e.g., environment variables, managed secrets services), not hardcoded.
*   **Input Validation:** Rigorously validate all incoming data to BFF endpoints, especially parameters from external redirects (`code`, `state`, `error`).
*   **Scope Limitation:** Only request the `user_profile` scope as it's the minimum required for Phase 1.
*   **Redirect URI Validation:** Ensure Instagram is configured to only redirect to your specific, registered `redirect_uri`.

## 6. Error Handling Strategy

*   **Core SDK:**
    *   Should throw distinct custom error types for different failure scenarios (e.g., `InstagramApiError`, `NetworkError`, `TokenExchangeError`).
    *   Errors should include relevant details for debugging.
*   **BFF:**
    *   Implement global error handling middleware.
    *   Catch errors from the SDK, database operations, Redis, and token encryption/decryption.
    *   Log errors comprehensively, aligning with existing practices for Shopify integration, including the use of OTel/Sentry as per project standards.
    *   Provide clear, user-friendly error messages or redirect to dedicated error pages on the frontend. Do not expose raw error details to the client.
    *   Specifically handle OAuth errors returned by Instagram (e.g., user denied access, invalid client).
*   **Frontend:**
    *   Display user-friendly error messages based on feedback from the BFF (e.g., via redirect query parameters or API error responses if applicable in future).

## 7. Testing Strategy

*   **Unit Tests:**
    *   **Core SDK:** Mock HTTP requests to the Instagram API to test `InstagramClient` methods (authorization URL generation, token exchange logic, profile fetching). Test various API response scenarios (success, errors).
    *   **BFF:** Mock the `InstagramClient`, Prisma client, Redis client, and encryption utilities. Test endpoint logic: `state` generation/validation, correct SDK calls, DB interactions, and redirects.
*   **Integration Tests:**
    *   Test BFF endpoints with live (or test-double) Redis and a mocked `InstagramClient` to ensure the OAuth state management and basic flow logic work.
    *   Test the token encryption/decryption utility.
*   **Manual End-to-End Testing:**
    *   Perform the full OAuth flow using a real (test) Instagram account to verify the integration from frontend initiation to successful token storage and console logging. Test error cases (e.g., denying access on Instagram).

## 8. Future Considerations (Phase 2 and Beyond)

While Phase 1 focuses on connection and basic info, future enhancements could include:

*   Displaying fetched Instagram profile information (username, profile picture) in the frontend UI.
*   Implementing the refresh mechanism for long-lived Instagram access tokens (they expire after 60 days).
*   Allowing users to manage (e.g., disconnect) their connected Instagram accounts.
*   Fetching and displaying user's Instagram media (photos, videos), requiring `user_media` scope.
*   Handling Instagram deauthorization webhooks to automatically remove connections when a user revokes access from Instagram's side.
*   More sophisticated rate limit handling in the `core-sdk`.
*   Expanding UI to show insights or data related to the connected Instagram account.

---
This plan provides a detailed roadmap for the initial Instagram integration.
This revised plan emphasizes alignment with the Shopify integration to ensure consistency and leverage proven solutions.


...
