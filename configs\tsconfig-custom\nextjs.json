{"$schema": "https://json.schemastore.org/tsconfig", "display": "Next.js", "extends": "./base.json", "compilerOptions": {"allowJs": true, "declaration": false, "declarationMap": false, "incremental": true, "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "module": "esnext", "noEmit": true, "resolveJsonModule": true, "target": "ES2021"}, "include": ["src", "next-env.d.ts"], "exclude": ["node_modules", ".next", "out"]}