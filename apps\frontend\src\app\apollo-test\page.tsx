"use client";

import { useState } from "react";
import { useAppApolloClient, useApolloCache } from "@/hooks/apollo";
import { useAppSelector, useAppDispatch } from "@/hooks/store";
import { setTheme } from "@/features/ui/uiSlice";
import { useGetUsersQuery, useGetUserQuery } from "@/generated/graphql-types";

export default function ApolloTestPage() {
  const [selectedUserId, setSelectedUserId] = useState<string>("");
  const dispatch = useAppDispatch();
  const theme = useAppSelector((state) => state.ui.theme);
  const client = useAppApolloClient();
  const cache = useApolloCache();

  const {
    data: usersData,
    loading: usersLoading,
    error: usersError,
    refetch: refetchUsers,
  } = useGetUsersQuery({
    errorPolicy: "all",
    notifyOnNetworkStatusChange: true,
  });

  const {
    data: userData,
    loading: userLoading,
    error: userError,
  } = useGetUserQuery({
    variables: { id: selectedUserId },
    skip: !selectedUserId,
    errorPolicy: "all",
  });

  const handleThemeToggle = () => {
    dispatch(setTheme(theme === "light" ? "dark" : "light"));
  };

  const handleClearCache = () => {
    cache.reset();
  };

  const handleRefetchUsers = () => {
    refetchUsers();
  };

  return (
    <div style={{ padding: "2rem", maxWidth: "800px", margin: "0 auto" }}>
      <h1>Apollo Client + Redux Integration Test</h1>

      <div
        style={{
          marginBottom: "2rem",
          padding: "1rem",
          backgroundColor: "#f3f4f6",
          borderRadius: "0.5rem",
        }}
      >
        <h2>Redux State Test</h2>
        <p>
          Current theme: <strong>{theme}</strong>
        </p>
        <button
          onClick={handleThemeToggle}
          style={{
            padding: "0.5rem 1rem",
            backgroundColor: "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "0.25rem",
            cursor: "pointer",
          }}
        >
          Toggle Theme (Redux)
        </button>
      </div>

      <div
        style={{
          marginBottom: "2rem",
          padding: "1rem",
          backgroundColor: "#f9fafb",
          borderRadius: "0.5rem",
        }}
      >
        <h2>Apollo Client Test</h2>

        <div style={{ marginBottom: "1rem" }}>
          <button
            onClick={handleRefetchUsers}
            disabled={usersLoading}
            style={{
              padding: "0.5rem 1rem",
              backgroundColor: "#10b981",
              color: "white",
              border: "none",
              borderRadius: "0.25rem",
              cursor: "pointer",
              marginRight: "0.5rem",
            }}
          >
            {usersLoading ? "Loading..." : "Refetch Users"}
          </button>

          <button
            onClick={handleClearCache}
            style={{
              padding: "0.5rem 1rem",
              backgroundColor: "#ef4444",
              color: "white",
              border: "none",
              borderRadius: "0.25rem",
              cursor: "pointer",
            }}
          >
            Clear Apollo Cache
          </button>
        </div>

        {usersError && (
          <div
            style={{
              padding: "1rem",
              backgroundColor: "#fef2f2",
              border: "1px solid #fecaca",
              borderRadius: "0.25rem",
              marginBottom: "1rem",
            }}
          >
            <h3 style={{ color: "#dc2626", margin: "0 0 0.5rem 0" }}>
              GraphQL Error:
            </h3>
            <p style={{ color: "#7f1d1d", margin: 0 }}>{usersError.message}</p>
            <small style={{ color: "#6b7280" }}>
              This is expected if the BFF server is not running. The error
              boundary should handle this gracefully.
            </small>
          </div>
        )}

        {usersLoading && (
          <div style={{ textAlign: "center", padding: "2rem" }}>
            <div
              style={{
                width: "40px",
                height: "40px",
                border: "4px solid #f3f3f3",
                borderTop: "4px solid #3b82f6",
                borderRadius: "50%",
                animation: "spin 1s linear infinite",
                margin: "0 auto 1rem",
              }}
            />
            <p>Loading users from GraphQL...</p>
          </div>
        )}

        {usersData?.users && (
          <div>
            <h3>Users ({usersData.users.length})</h3>
            <div style={{ marginBottom: "1rem" }}>
              <select
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                style={{
                  padding: "0.5rem",
                  border: "1px solid #d1d5db",
                  borderRadius: "0.25rem",
                  width: "200px",
                }}
              >
                <option value="">Select a user...</option>
                {usersData.users.map((user: any) => (
                  <option key={user.id} value={user.id}>
                    {user.name} (ID: {user.id})
                  </option>
                ))}
              </select>
            </div>

            <div
              style={{
                display: "grid",
                gridTemplateColumns: "repeat(auto-fit, minmax(300px, 1fr))",
                gap: "1rem",
              }}
            >
              {usersData.users.map((user: any) => (
                <div
                  key={user.id}
                  style={{
                    padding: "1rem",
                    border: "1px solid #e5e7eb",
                    borderRadius: "0.5rem",
                    backgroundColor: "white",
                  }}
                >
                  <h4 style={{ margin: "0 0 0.5rem 0" }}>{user.name}</h4>
                  <p
                    style={{
                      margin: "0.25rem 0",
                      fontSize: "0.875rem",
                      color: "#6b7280",
                    }}
                  >
                    ID: {user.id}
                  </p>
                  <p
                    style={{
                      margin: "0.25rem 0",
                      fontSize: "0.875rem",
                      color: "#6b7280",
                    }}
                  >
                    Tenant: {user.tenantId}
                  </p>
                  <p
                    style={{
                      margin: "0.25rem 0",
                      fontSize: "0.875rem",
                      color: "#6b7280",
                    }}
                  >
                    Created: {new Date(user.createdAt).toLocaleDateString()}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        {selectedUserId && (
          <div
            style={{
              marginTop: "2rem",
              padding: "1rem",
              backgroundColor: "#eff6ff",
              borderRadius: "0.5rem",
            }}
          >
            <h3>Selected User Details</h3>
            {userLoading && <p>Loading user details...</p>}
            {userError && (
              <p style={{ color: "#dc2626" }}>Error: {userError.message}</p>
            )}
            {userData?.user && (
              <div>
                <h4>{userData.user.name}</h4>
                <pre
                  style={{
                    backgroundColor: "#f3f4f6",
                    padding: "1rem",
                    borderRadius: "0.25rem",
                    overflow: "auto",
                    fontSize: "0.875rem",
                  }}
                >
                  {JSON.stringify(userData.user, null, 2)}
                </pre>
              </div>
            )}
          </div>
        )}
      </div>

      <div
        style={{
          padding: "1rem",
          backgroundColor: "#f0f9ff",
          borderRadius: "0.5rem",
        }}
      >
        <h2>Integration Status</h2>
        <ul style={{ margin: 0, paddingLeft: "1.5rem" }}>
          <li>✅ Redux Store: Working (theme toggle)</li>
          <li>✅ Apollo Client: Configured with error boundaries</li>
          <li>
            ✅ Provider Chain: StoreProvider → ApolloProvider →
            AuthServiceProvider
          </li>
          <li>✅ Type Safety: TypeScript integration complete</li>
          <li>✅ Error Handling: Graceful fallbacks for GraphQL errors</li>
          <li>✅ Cache Management: Manual cache operations available</li>
          <li>✅ Redux DevTools: Apollo operations logged to Redux DevTools</li>
        </ul>
      </div>
    </div>
  );
}
