"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth, useAuthActions } from "@/hooks/features";

export default function SignInPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const auth = useAuth();
  const authActions = useAuthActions();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [rememberMe, setRememberMe] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const urlEmail = searchParams.get("email");
    const urlPassword = searchParams.get("password");
    const urlRememberMe = searchParams.get("rememberMe");
    const autoExecute = searchParams.get("auto");

    if (urlEmail) setEmail(urlEmail);
    if (urlPassword) setPassword(urlPassword);
    if (urlRememberMe) setRememberMe(urlRememberMe === "true");

    if (autoExecute === "true" && urlEmail && urlPassword) {
      const executeSignIn = async () => {
        try {
          setLoading(true);
          setError(null);

          await authActions.signIn({
            email: urlEmail,
            password: urlPassword,
            rememberMe: urlRememberMe === "true",
          });
        } catch (err) {
          setError(err instanceof Error ? err.message : "Sign in failed");
        } finally {
          setLoading(false);
        }
      };
      executeSignIn();
    }
  }, [searchParams, authActions]);

  useEffect(() => {
    if (auth.isAuthenticated) {
      const redirectUrl = searchParams.get("redirect") || "/dashboard";
      router.push(redirectUrl);
    }
  }, [auth.isAuthenticated, searchParams, router]);

  const handleSignIn = async () => {
    if (!email || !password) {
      setError("Email and password are required");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await authActions.signIn({
        email,
        password,
        rememberMe,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Sign in failed");
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
    authActions.clearAuthError();
  };

  const currentError = error || auth.authError;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign In (Test Page)
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Test sign in with URL parameters support
          </p>
        </div>

        {auth.isCheckingAuth ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">
              Checking authentication...
            </p>
          </div>
        ) : (
          <div className="mt-8 space-y-6">
            {currentError && (
              <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded relative">
                <span className="block sm:inline">{currentError}</span>
                <button
                  onClick={clearError}
                  className="absolute top-0 bottom-0 right-0 px-4 py-3"
                >
                  <span className="sr-only">Dismiss</span>×
                </button>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your email"
                  disabled={loading}
                />
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Password
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter your password"
                  disabled={loading}
                />
              </div>

              <div className="flex items-center">
                <input
                  id="remember-me"
                  type="checkbox"
                  checked={rememberMe}
                  onChange={(e) => setRememberMe(e.target.checked)}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  disabled={loading}
                />
                <label
                  htmlFor="remember-me"
                  className="ml-2 block text-sm text-gray-900"
                >
                  Remember me
                </label>
              </div>

              <button
                onClick={handleSignIn}
                disabled={loading || !email || !password}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing in...
                  </>
                ) : (
                  "Sign In"
                )}
              </button>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h3 className="text-sm font-medium text-blue-900 mb-2">
                URL Parameters:
              </h3>
              <ul className="text-xs text-blue-700 space-y-1">
                <li>
                  <code>?email=<EMAIL></code> - Pre-fill email
                </li>
                <li>
                  <code>?password=password123</code> - Pre-fill password
                </li>
                <li>
                  <code>?rememberMe=true</code> - Set remember me
                </li>
                <li>
                  <code>?auto=true</code> - Auto-execute sign in
                </li>
                <li>
                  <code>?redirect=/dashboard</code> - Redirect after success
                </li>
              </ul>
              <p className="text-xs text-blue-600 mt-2">
                Example:{" "}
                <code>
                  /auth/signin?email=<EMAIL>&password=test123&auto=true
                </code>
              </p>
            </div>

            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">
                Don't have an account?{" "}
                <a
                  href="/auth/signup"
                  className="text-blue-600 hover:text-blue-500"
                >
                  Sign up
                </a>
              </p>
              <p className="text-sm text-gray-600">
                <a
                  href="/auth/forgot-password"
                  className="text-blue-600 hover:text-blue-500"
                >
                  Forgot your password?
                </a>
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
