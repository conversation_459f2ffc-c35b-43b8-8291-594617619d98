{"compilerOptions": {"target": "ES2021", "module": "ESNext", "moduleResolution": "node", "outDir": "./dist", "rootDir": "./src", "baseUrl": "./src", "strict": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "declaration": true, "declarationMap": true, "paths": {"@/*": ["./*"]}}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}