{"name": "@pulsepanel/bff", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "pnpm run codegen && node --watch --import tsx/esm src/index.ts", "build": "pnpm run codegen && tsc", "start": "node dist/index.js", "lint": "eslint .", "codegen": "graphql-codegen", "codegen:watch": "graphql-codegen --watch", "test": "vitest run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest run --coverage", "test:coverage:watch": "vitest --coverage", "test:auth": "tsx src/test-auth-api.ts", "test:rbac": "tsx src/test-rbac.ts", "test:all": "tsx src/test-auth-api.ts && tsx src/test-rbac.ts"}, "dependencies": {"@apollo/server": "^4.10.4", "@graphql-tools/schema": "^10.0.6", "@graphql-tools/utils": "^10.5.4", "@pulsepanel/db": "workspace:*", "@types/node-fetch": "^2.6.12", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.19.2", "graphql": "^16.11.0", "graphql-depth-limit": "^1.1.0", "ioredis": "^5.6.1", "node-fetch": "^3.3.2", "supertokens-node": "^22.1.0", "type-graphql": "2.0.0-rc.2", "typegraphql-prisma": "^0.28.0"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-resolvers": "^4.5.1", "@pulsepanel/eslint-config-custom": "workspace:*", "@pulsepanel/tsconfig-custom": "workspace:*", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/graphql-depth-limit": "^1.1.6", "@types/node": "^20.11.20", "@types/supertest": "^6.0.2", "supertest": "^7.0.0", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "tsx": "^4.7.0", "typescript": "^5.4.5", "vitest": "^2.1.8", "@vitest/coverage-v8": "^2.1.8", "@vitest/ui": "^2.1.8"}}