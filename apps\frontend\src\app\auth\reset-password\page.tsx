"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function ResetPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [token, setToken] = useState("");
  const [password, setPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resetCompleted, setResetCompleted] = useState(false);

  useEffect(() => {
    const urlToken = searchParams.get("token");
    const urlPassword = searchParams.get("password");
    const urlConfirmPassword = searchParams.get("confirmPassword");
    const autoExecute = searchParams.get("auto");

    if (urlToken) setToken(urlToken);
    if (urlPassword) setPassword(urlPassword);
    if (urlConfirmPassword) setConfirmPassword(urlConfirmPassword);

    if (
      autoExecute === "true" &&
      urlToken &&
      urlPassword &&
      urlConfirmPassword
    ) {
      const executeResetPassword = async () => {
        if (!urlToken) {
          setError("Reset token is required");
          return;
        }

        if (!urlPassword || !urlConfirmPassword) {
          setError("Password and confirmation are required");
          return;
        }

        if (urlPassword !== urlConfirmPassword) {
          setError("Passwords do not match");
          return;
        }

        if (urlPassword.length < 6) {
          setError("Password must be at least 6 characters long");
          return;
        }

        try {
          setLoading(true);
          setError(null);

          console.log("🔄 Reset password with token:", urlToken);

          await new Promise((resolve) => setTimeout(resolve, 1000));

          setResetCompleted(true);

          const redirectUrl = searchParams.get("redirect") || "/auth/signin";
          setTimeout(() => {
            router.push(redirectUrl);
          }, 2000);
        } catch (err) {
          setError(
            err instanceof Error ? err.message : "Failed to reset password",
          );
        } finally {
          setLoading(false);
        }
      };
      executeResetPassword();
    }
  }, [searchParams, router]);

  const handleResetPassword = async () => {
    if (!token) {
      setError("Reset token is required");
      return;
    }

    if (!password || !confirmPassword) {
      setError("Password and confirmation are required");
      return;
    }

    if (password !== confirmPassword) {
      setError("Passwords do not match");
      return;
    }

    if (password.length < 6) {
      setError("Password must be at least 6 characters long");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log("🔄 Reset password with token:", token);

      await new Promise((resolve) => setTimeout(resolve, 1000));

      setResetCompleted(true);

      const redirectUrl = searchParams.get("redirect") || "/auth/signin";
      setTimeout(() => {
        router.push(redirectUrl);
      }, 2000);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to reset password");
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  if (resetCompleted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="text-green-600 mb-4">
              <svg
                className="mx-auto h-12 w-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
            <h2 className="text-3xl font-extrabold text-gray-900">
              Password Reset Successful
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              Your password has been successfully reset. Redirecting to sign
              in...
            </p>
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-orange-600 mx-auto mt-4"></div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Reset Password (Test Page)
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Test reset password with URL parameters support
          </p>
        </div>

        <div className="mt-8 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded relative">
              <span className="block sm:inline">{error}</span>
              <button
                onClick={clearError}
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
              >
                <span className="sr-only">Dismiss</span>×
              </button>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label
                htmlFor="token"
                className="block text-sm font-medium text-gray-700"
              >
                Reset Token
              </label>
              <input
                id="token"
                type="text"
                value={token}
                onChange={(e) => setToken(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                placeholder="Enter reset token"
                disabled={loading}
              />
            </div>

            <div>
              <label
                htmlFor="password"
                className="block text-sm font-medium text-gray-700"
              >
                New Password
              </label>
              <input
                id="password"
                type="password"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                placeholder="Enter new password"
                disabled={loading}
              />
            </div>

            <div>
              <label
                htmlFor="confirmPassword"
                className="block text-sm font-medium text-gray-700"
              >
                Confirm New Password
              </label>
              <input
                id="confirmPassword"
                type="password"
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                placeholder="Confirm new password"
                disabled={loading}
              />
            </div>

            <button
              onClick={handleResetPassword}
              disabled={loading || !token || !password || !confirmPassword}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-orange-600 hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-orange-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Resetting...
                </>
              ) : (
                "Reset Password"
              )}
            </button>
          </div>

          <div className="mt-6 p-4 bg-orange-50 rounded-lg">
            <h3 className="text-sm font-medium text-orange-900 mb-2">
              URL Parameters:
            </h3>
            <ul className="text-xs text-orange-700 space-y-1">
              <li>
                <code>?token=reset_token_123</code> - Pre-fill reset token
              </li>
              <li>
                <code>?password=newpass123</code> - Pre-fill new password
              </li>
              <li>
                <code>?confirmPassword=newpass123</code> - Pre-fill password
                confirmation
              </li>
              <li>
                <code>?auto=true</code> - Auto-execute password reset
              </li>
              <li>
                <code>?redirect=/auth/signin</code> - Redirect after success
              </li>
            </ul>
            <p className="text-xs text-orange-600 mt-2">
              Example:{" "}
              <code>
                /auth/reset-password?token=abc123&password=newpass&confirmPassword=newpass&auto=true
              </code>
            </p>
          </div>

          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              Remember your password?{" "}
              <a
                href="/auth/signin"
                className="text-orange-600 hover:text-orange-500"
              >
                Sign in
              </a>
            </p>
            <p className="text-sm text-gray-600">
              Need a new reset link?{" "}
              <a
                href="/auth/forgot-password"
                className="text-orange-600 hover:text-orange-500"
              >
                Forgot password
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
