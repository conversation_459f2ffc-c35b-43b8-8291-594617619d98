"use client";

import React, {
  createContext,
  useContext,
  useEffect,
  useState,
  ReactNode,
} from "react";
import type { IAuthService, IAuthServiceFactory, AuthConfig } from "@/auth";
import { GraphQLAuthService } from "@/auth";
import { injectAuthService } from "@/store";
import { useAppDispatch } from "@/hooks/store";
import { checkAuthStatus } from "@/features/auth/authSlice";
import { useTransportFactory } from "./TransportProvider";

interface AuthServiceContextValue {
  authService: IAuthService | null;
  isInitialized: boolean;
  config: AuthConfig | null;
  error: string | null;
}

const AuthServiceContext = createContext<AuthServiceContextValue>({
  authService: null,
  isInitialized: false,
  config: null,
  error: null,
});

class DefaultAuthServiceFactory implements IAuthServiceFactory {
  constructor(
    private config: AuthConfig,
    private transportFactory: any,
  ) {}

  createAuthService(): IAuthService {
    const transport = this.transportFactory.createGraphQLTransport(this.config);
    return new GraphQLAuthService(this.config, transport);
  }
}

function createAuthConfig(): AuthConfig {
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000";
  const graphqlEndpoint = `${apiUrl}/graphql`;

  return {
    apiUrl,
    graphqlEndpoint,
    enableCaching: process.env.NODE_ENV !== "production",
    sessionRefreshInterval: 5 * 60 * 1000,
    rememberMeDefault: false,
    debugMode: process.env.NODE_ENV === "development",
  };
}

interface AuthServiceProviderProps {
  children: ReactNode;
  config?: Partial<AuthConfig>;
  factory?: IAuthServiceFactory;
}

export function AuthServiceProvider({
  children,
  config: customConfig,
  factory: customFactory,
}: AuthServiceProviderProps) {
  const [authService, setAuthService] = useState<IAuthService | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [config, setConfig] = useState<AuthConfig | null>(null);
  const [error, setError] = useState<string | null>(null);

  const dispatch = useAppDispatch();
  const transportFactory = useTransportFactory();

  useEffect(() => {
    async function initializeAuthService() {
      try {
        const baseConfig = createAuthConfig();
        const finalConfig: AuthConfig = {
          ...baseConfig,
          ...customConfig,
        };

        const factory =
          customFactory ||
          new DefaultAuthServiceFactory(finalConfig, transportFactory);
        const service = factory.createAuthService();

        injectAuthService(service);

        setAuthService(service);
        setConfig(finalConfig);
        setIsInitialized(true);
        setError(null);

        if (finalConfig.debugMode) {
          console.log("Auth service initialized:", {
            config: finalConfig,
            serviceType: service.constructor.name,
          });
        }
      } catch (error) {
        const errorMessage =
          error instanceof Error
            ? error.message
            : "Unknown initialization error";
        setError(errorMessage);
        setIsInitialized(false);

        console.error("Failed to initialize auth service:", error);
      }
    }

    initializeAuthService();
  }, [customConfig, customFactory, transportFactory]);

  useEffect(() => {
    if (isInitialized && authService) {
      dispatch(checkAuthStatus());
    }
  }, [isInitialized, authService, dispatch]);

  useEffect(() => {
    return () => {
      if (authService && "cleanup" in authService) {
        const cleanup = (authService as any).cleanup;
        if (typeof cleanup === "function") {
          cleanup();
        }
      }
    };
  }, [authService]);

  const contextValue: AuthServiceContextValue = {
    authService,
    isInitialized,
    config,
    error,
  };

  return (
    <AuthServiceContext.Provider value={contextValue}>
      {children}
    </AuthServiceContext.Provider>
  );
}

export function useAuthService(): IAuthService {
  const context = useContext(AuthServiceContext);

  if (!context) {
    throw new Error(
      "useAuthService must be used within an AuthServiceProvider",
    );
  }

  if (context.error) {
    throw new Error(`Auth service initialization failed: ${context.error}`);
  }

  if (!context.isInitialized || !context.authService) {
    throw new Error("Auth service is not initialized yet");
  }

  return context.authService;
}

export function useAuthServiceState() {
  const context = useContext(AuthServiceContext);

  if (!context) {
    throw new Error(
      "useAuthServiceState must be used within an AuthServiceProvider",
    );
  }

  return {
    authService: context.authService,
    isInitialized: context.isInitialized,
    config: context.config,
    error: context.error,
    isLoading: !context.isInitialized && !context.error,
  };
}

export function useAuthConfig(): AuthConfig | null {
  const context = useContext(AuthServiceContext);

  if (!context) {
    throw new Error("useAuthConfig must be used within an AuthServiceProvider");
  }

  return context.config;
}

export const AuthServiceDevTools = {
  getCurrentService(): IAuthService | null {
    const context = React.useContext(AuthServiceContext);
    return context?.authService || null;
  },

  async testConnectivity(): Promise<boolean> {
    const service = this.getCurrentService();
    if (!service) {
      return false;
    }

    try {
      return true;
    } catch {
      return false;
    }
  },

  getServiceStats() {
    const service = this.getCurrentService();
    if (!service) return null;

    return {
      serviceType: service.constructor.name,
      initialized: true,
    };
  },
};
