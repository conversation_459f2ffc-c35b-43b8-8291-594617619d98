# План внедрения Redux Toolkit 2.0 + React-Redux 9.0

## Обзор

Redux Toolkit 2.0 + React-Redux 9.0 будет использоваться как централизованный менеджер client state для PulsePanel, работая совместно с Apollo Client для server state. Интеграция с Next.js 15 + React 19 + TypeScript 5.4.5.

## Архитектура платформы

### Основная структура платформы

- **Frontend**: Next.js 15 + React 19 + Redux Toolkit 2.0
- **BFF**: Express + Apollo Server + TypeGraphQL
- **Database**: PostgreSQL + Prisma ORM
- **Events**: RabbitMQ для межсервисных событий
- **State Architecture**: Hybrid State Management

### Hybrid State Architecture

```
┌─ Apollo Client Cache ──────────────────────────────────────────┐
│ SERVER STATE (через GraphQL)                                  │
│ • Users, Projects, Roles данные                                │
│ • GraphQL queries & mutations                                  │
│ • Normalized cache с типизацией                                │
│ • Automatic refetching & invalidation                          │
│ • Optimistic updates для mutations                             │
└────────────────────────────────────────────────────────────────┘

┌─ Redux Store ──────────────────────────────────────────────────┐
│ CLIENT STATE (UI и локальное состояние)                        │
│ • UI state (modals, sidebar, navigation)                       │
│ • Auth status (isAuthenticated, loading states)                │
│ • User preferences (theme, language, settings)                 │
│ • Form state & temporary data                                  │
└────────────────────────────────────────────────────────────────┘
```

### Структура проекта

```
apps/frontend/src/
├── store/                    # Redux Store Configuration
│   ├── index.ts             # Store setup & configuration
│   ├── middleware.ts        # Custom middleware
│   └── rootReducer.ts       # Combined reducers
├── features/                # Feature-based Slices
│   ├── auth/                # Auth state slice
│   ├── ui/                  # UI state slice
│   └── user-preferences/    # User settings slice
├── hooks/                   # Pre-typed Redux hooks
│   ├── store.ts            # useAppDispatch, useAppSelector
│   └── features.ts         # Feature-specific hooks
├── providers/               # React Providers
│   ├── StoreProvider.tsx   # Redux Provider
│   └── AppProviders.tsx    # Combined providers
└── types/                   # Redux Types
    ├── store.ts            # RootState, AppDispatch
    └── slices.ts           # Slice types
```

## Основные этапы реализации

## Этап 1: Проверка совместимости и установка зависимостей

### 🚩 Checkpoint 1.1: Проверка совместимости стека

- [x] **1.1.1: Проверить совместимость Redux Toolkit 2.0 с текущим стеком**

  - [x] Создать тестовую ветку для проверки
  - [x] Проверить совместимость с React 19 и Next.js 15
  - [x] Убедиться в отсутствии конфликтов с Apollo Client
  - [x] Проверить TypeScript 5.4.5 compatibility
  - [x] **Результат**: Совместимость подтверждена ✅

- [x] **1.1.2: Установить Redux Toolkit 2.0 и React-Redux 9.0**

  - [x] Добавить `@reduxjs/toolkit@^2.8.2` в package.json
  - [x] Добавить `react-redux@^9.2.0` в package.json
  - [x] Обновить `@types/react-redux@^7.1.34` до последней версии
  - [x] Проверить установку через `pnpm install`
  - [x] **Результат**: Зависимости установлены и работают ✅

- [x] **1.1.3: Создать базовую структуру папок**
  - [x] Создать папку `apps/frontend/src/store/`
  - [x] Создать папку `apps/frontend/src/features/`
  - [x] Папки `apps/frontend/src/hooks/` и `apps/frontend/src/types/` уже существуют
  - [x] **Результат**: Структура проекта готова ✅

## Этап 2: Создание слайсов (до настройки store!)

### 🚩 Checkpoint 2.1: Создание Auth Slice

- [x] **2.1.1: Создать authSlice с современным API**

  - [x] Создать `apps/frontend/src/features/auth/authSlice.ts`
  - [x] Определить AuthState interface на основе AuthServiceProvider
  - [x] Использовать `createSlice` с RTK 2.0 syntax и встроенными selectors
  - [x] Добавить selectors в slice
  - [x] **Результат**: Auth slice создан ✅

- [x] **2.1.2: Создать async thunks для auth операций**
  - [x] Создать `checkAuthStatus` thunk с `createAsyncThunk`
  - [x] Создать `signIn`, `signUp`, `signOut` thunks
  - [x] Создать `refreshToken` thunk
  - [x] Добавить error handling и proper TypeScript типы для thunks
  - [x] **Результат**: Async auth operations готовы ✅

### 🚩 Checkpoint 2.2: Создание UI Slice

- [x] **2.2.1: Создать uiSlice**
  - [x] Создать `apps/frontend/src/features/ui/uiSlice.ts`
  - [x] Определить UIState interface для modals, sidebar, theme, notifications
  - [x] Добавить reducers для UI operations с RTK 2.0 syntax
  - [x] Создать встроенные selectors для UI state
  - [x] **Результат**: UI slice готов ✅

## Этап 3: Настройка Store с готовыми слайсами

### 🚩 Checkpoint 3.1: Конфигурация Store

- [x] **3.1.1: Создать store configuration с слайсами**

  - [x] Создать `apps/frontend/src/store/index.ts`
  - [x] Импортировать все созданные слайсы (auth, ui)
  - [x] Настроить `configureStore` с RTK 2.0 `combineSlices`
  - [x] Добавить Redux DevTools configuration с security
  - [x] Создать `makeStore` и `getStore` функции для Next.js SSR
  - [x] Настроить thunk middleware с authService injection
  - [x] **Результат**: Store configuration готов с слайсами ✅

- [x] **3.1.2: Создать TypeScript типы НА ОСНОВЕ store**

  - [x] Создать `apps/frontend/src/types/store.ts`
  - [x] Добавить `RootState` и `AppDispatch` типы из store
  - [x] Экспортировать типы slices (AuthState, UIState)
  - [x] Добавить utility types для Redux работы
  - [x] **Результат**: Типы созданы на основе реального store ✅

- [x] **3.1.3: Создать типизированные hooks**
  - [x] Создать pre-typed hooks в `apps/frontend/src/hooks/store.ts`
  - [x] Использовать RootState и AppDispatch из store
  - [x] Настроить TypeScript inference (useAppDispatch, useAppSelector)
  - [x] **Результат**: Type-safe hooks готовы ✅

### 🚩 Checkpoint 3.2: StoreProvider интеграция с Next.js

- [x] **3.2.1: Создать StoreProvider для SSR**

  - [x] Создать `apps/frontend/src/providers/StoreProvider.tsx`
  - [x] Реализовать per-request store creation с useRef
  - [x] Добавить client-side store reuse logic
  - [x] Создать utility functions (withStoreProvider, createStoreProvider)
  - [x] Создать `AppProviders.tsx` для комбинирования providers
  - [x] **Результат**: SSR-safe Redux Provider готов ✅

- [x] **3.2.2: Интегрировать в root layout**

  - [x] Обновить `apps/frontend/src/app/layout.tsx`
  - [x] Добавить StoreProvider в provider chain
  - [x] Настроить правильный порядок providers (StoreProvider -> AuthServiceProvider)
  - [x] Добавить Redux demo в Navigation для тестирования
  - [x] **Результат**: Redux Store доступен в приложении ✅

- [x] **3.2.3: Настроить совместимость с Apollo Client**
  - [x] Добавить ApolloProvider в layout.tsx
  - [x] Настроить глобальный Apollo Client для компонентов
  - [x] Интегрировать Apollo Cache с существующим auth layer
  - [x] Убедиться в отсутствии конфликтов между providers
  - [x] **Результат**: Apollo Client доступен глобально для компонентов ✅

## Этап 4: Миграция Auth состояния

### 🚩 Checkpoint 4.1: Миграция с Context API на Redux

- [x] **4.1.1: Обновить AuthServiceProvider**

  - [x] Сохранить service initialization в Context
  - [x] Переместить auth state в Redux store
  - [x] Обновить auth методы для dispatch Redux actions
  - [x] Добавить `injectAuthService` для dynamic service injection
  - [x] **Результат**: Auth state в Redux, service layer в Context ✅

- [x] **4.1.2: Создать auth hooks для компонентов**
  - [x] Создать `useAuth()` hook использующий Redux selectors
  - [x] Создать `useAuthActions()` hook для auth операций
  - [x] Создать feature hooks в `apps/frontend/src/hooks/features.ts`
  - [x] Обновить backward compatibility в AuthServiceProvider
  - [x] **Результат**: Components могут использовать Redux auth state ✅

### 🚩 Checkpoint 4.2: Auth state synchronization

- [x] **4.2.1: Настроить sync между AuthService и Redux**
  - [x] Создать middleware для sync auth changes через injection
  - [x] Добавить automatic state updates при auth events
  - [x] Настроить proper cleanup при logout
  - [x] **Результат**: Auth state синхронизирован ✅

## Этап 5: UI State Integration

### 🚩 Checkpoint 5.1: Интеграция UI slice в компоненты

- [x] **5.1.1: Интегрировать UI slice в компоненты**
  - [x] Создать feature hooks для UI state management
  - [x] Создать modal management hooks
  - [x] Добавить theme switching через Redux
  - [x] Создать тестовые страницы `/redux-test` и `/auth-test-redux`
  - [x] **Результат**: UI state централизован в Redux ✅

## Этап 6: Advanced Redux Features & Apollo Integration

### 🚩 Checkpoint 6.1: Apollo Client & Redux DevTools Integration

- [ ] **6.1.1: Интегрировать Apollo Client с Redux DevTools**

  - [ ] Настроить Apollo Client DevTools link
  - [ ] Добавить GraphQL operations в Redux DevTools timeline
  - [ ] Создать middleware для логирования GraphQL запросов в Redux DevTools
  - [ ] **Результат**: Unified debugging experience

- [ ] **6.1.2: Оптимизировать Apollo Cache политики**
  - [ ] Настроить cache policies для лучшей производительности
  - [ ] Добавить normalized caching для сложных типов
  - [ ] Оптимизировать fetch policies для разных типов запросов
  - [ ] **Результат**: Оптимизированный server state management

### 🚩 Checkpoint 6.2: Middleware и DevTools

- [x] **6.2.1: Настроить custom middleware**

  - [x] Добавить thunk middleware с authService injection
  - [x] Настроить Redux DevTools для production и development
  - [x] Добавить state sanitizers для sensitive data в DevTools
  - [x] **Результат**: Basic middleware настроены ✅

- [ ] **6.2.2: Redux DevTools optimization**
  - [ ] Настроить action names для debugging
  - [ ] Добавить advanced sanitizers для sensitive data
  - [ ] Настроить time-travel debugging
  - [ ] **Результат**: DevTools оптимизированы

### 🚩 Checkpoint 6.3: Performance optimization

- [x] **6.3.1: Selectors optimization**

  - [x] Создать memoized selectors с `createSelector`
  - [x] Добавить computed values через selectors
  - [x] Оптимизировать selector re-computations
  - [x] **Результат**: Селекторы оптимизированы ✅

- [ ] **6.3.2: Code splitting для slices**
  - [ ] Настроить lazy loading для feature slices
  - [ ] Использовать `combineSlices` для dynamic loading
  - [ ] Добавить bundle analysis для Redux части
  - [ ] **Результат**: Redux код разделен по chunks

## Этап 7: SSR и Hydration

### 🚩 Checkpoint 7.1: Next.js SSR integration

- [ ] **7.1.1: Настроить server-side rendering**

  - [ ] Создать SSR-safe initial state
  - [ ] Настроить proper hydration strategy
  - [ ] Добавить preloaded state injection
  - [ ] **Результат**: SSR работает с Redux

- [ ] **7.1.2: Hydration optimization**
  - [ ] Настроить selective hydration для critical state
  - [ ] Добавить hydration error handling
  - [ ] Оптимизировать initial bundle size
  - [ ] **Результат**: Hydration оптимизирован

## Этап 8: Testing и Quality Assurance

### 🚩 Checkpoint 8.1: Unit тесты

- [ ] **8.1.1: Тестирование slices**

  - [ ] Написать тесты для auth slice reducers
  - [ ] Тестировать async thunks
  - [ ] Тестировать селекторы
  - [ ] **Результат**: Slices покрыты тестами

- [ ] **8.1.2: Тестирование hooks**
  - [ ] Тестировать custom Redux hooks
  - [ ] Тестировать integration с компонентами
  - [ ] **Результат**: Hooks протестированы

### 🚩 Checkpoint 8.2: Integration тесты

- [ ] **8.2.1: E2E тесты Redux flow**
  - [ ] Тестировать auth flow через Redux
  - [ ] Тестировать UI state management
  - [ ] Тестировать SSR/hydration
  - [ ] **Результат**: Redux flow работает end-to-end

## Этап 9: Production и мониторинг

### 🚩 Checkpoint 9.1: Production optimization

- [ ] **9.1.1: Performance monitoring**

  - [ ] Добавить Redux performance metrics
  - [ ] Настроить action tracking
  - [ ] Добавить selector performance monitoring
  - [ ] **Результат**: Production monitoring готов

- [ ] **9.1.2: Error handling**
  - [ ] Настроить error boundaries для Redux errors
  - [ ] Добавить fallback states
  - [ ] Настроить error reporting
  - [ ] **Результат**: Error handling настроен

---

## Особенности и риски

### Ключевые моменты (Must Have)

1. **SSR Compatibility:** НЕ РЕАЛИЗОВАНО ❌ (только client-side store)
2. **Apollo Integration:** НЕ РЕАЛИЗОВАНО ❌ (только изолированно в auth service)
3. **Type Safety:** Полная TypeScript интеграция с инференцией типов ✅
4. **Performance:** Базовая оптимизация с memoized selectors ✅

### Nice to have

1. **Redux DevTools:** Time-travel debugging и state inspection
2. **Code Splitting:** Lazy loading слайсов для оптимизации bundle
3. **Persistence:** Selective state persistence для user preferences
4. **Analytics:** Redux action tracking для product insights

### Потенциальные риски

1. **Bundle Size:** +~8KB для Redux Toolkit (minified + gzipped) - оправдано для client state
2. **Learning Curve:** Команда должна освоить Redux patterns
3. **State Separation:** Важно четко разделять client state (Redux) и server state (Apollo)
4. **Hydration Issues:** Требует аккуратной настройки SSR

## MVP: Минимально рабочий запуск

- ✅ **Базовая Redux настройка** с TypeScript
- ✅ **Auth state migration** от Context к Redux
- ✅ **UI slice** для global UI state
- ❌ **SSR-safe hydration** НЕ РЕАЛИЗОВАНО (только client-side)
- ❌ **Apollo Client integration** НЕ РЕАЛИЗОВАНО (только в auth service)
- ✅ **Build verification** - проект успешно собирается

### Для полноценного релиза

- Apollo Client + Redux DevTools integration
- Comprehensive testing coverage
- Performance optimization и monitoring
- Production error handling
- Analytics integration
