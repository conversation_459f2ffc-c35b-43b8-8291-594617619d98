{"$schema": "https://json.schemastore.org/tsconfig", "display": "Node.js", "extends": "./base.json", "compilerOptions": {"composite": false, "declaration": true, "declarationMap": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "inlineSources": false, "isolatedModules": true, "moduleResolution": "node", "noUnusedLocals": false, "noUnusedParameters": false, "preserveWatchOutput": true, "skipLibCheck": true, "strict": true, "target": "ES2021", "module": "commonjs", "sourceMap": true, "outDir": "dist"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}