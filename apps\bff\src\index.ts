import express from "express";
import cors from "cors";
import http from "http";
import { ApolloServer } from "@apollo/server";
import { expressMiddleware } from "@apollo/server/express4";
import { ApolloServerPluginDrainHttpServer } from "@apollo/server/plugin/drainHttpServer";
import type {
  GraphQLRequestListener,
  GraphQLRequestContext,
} from "@apollo/server";
import { PrismaClient } from "@pulsepanel/db/generated/client";
import dotenv from "dotenv";
import Redis from "ioredis";

import {
  initSuperTokens,
  createAuthMiddleware,
  createAuthStatsMiddleware,
  type AuthContext,
} from "./auth";
import { middleware } from "supertokens-node/framework/express";

import { initializeRoles } from "./auth/initializeRoles.js";

import {
  createGraphQLContext,
  addOperationInfoToContext,
} from "./auth/contextFactory.js";
import { buildSecureSchema } from "./schema/buildSchema.js";
import {
  createDepthLimitRule,
  analyzeQueryCost,
  monitorOperationPerformance,
  detectSuspiciousPatterns,
  generateSecurityReport,
  createIntrospectionSecurity,
  securityErrorFormatter,
} from "./auth/security.js";
import type { Context } from "./types/context.js";

dotenv.config();

initSuperTokens();

const prisma = new PrismaClient();
if (!process.env.REDIS_URL) {
  throw new Error("REDIS_URL environment variable is not set.");
}
const redis = new Redis(process.env.REDIS_URL);

// Build GraphQL schema with security directives and resolvers
const schema = buildSecureSchema();

// Advanced security configuration
const introspectionSecurity = createIntrospectionSecurity();

async function startApp() {
  // Initialize SuperTokens FIRST
  initSuperTokens();

  const app = express();
  const httpServer = http.createServer(app);

  const apollo = new ApolloServer<Context>({
    schema,
    // Advanced Security: Query complexity & introspection controls
    validationRules: [createDepthLimitRule(10)],
    introspection: introspectionSecurity.introspection,
    includeStacktraceInErrorResponses: process.env.NODE_ENV !== "production",

    plugins: [
      ApolloServerPluginDrainHttpServer({ httpServer }),
      {
        async requestDidStart(): Promise<GraphQLRequestListener<Context>> {
          return {
            async didResolveOperation(
              requestContext: GraphQLRequestContext<Context>,
            ) {
              // Add operation info to context for audit
              if (requestContext.request.operationName) {
                addOperationInfoToContext(
                  requestContext.contextValue,
                  requestContext.request.operationName,
                );
              }

              // Advanced Security: Query analysis
              const query = requestContext.request.query || "";
              const cost = analyzeQueryCost(
                query,
                requestContext.request.variables,
              );

              // Log high-cost queries
              if (cost > 10) {
                requestContext.contextValue.logSecurityEvent(
                  "high_cost_query",
                  {
                    cost,
                    operation: requestContext.request.operationName,
                    query: query.substring(0, 200),
                  },
                );
              }

              // Detect suspicious patterns
              detectSuspiciousPatterns(query, requestContext.contextValue);
            },
            async didEncounterErrors(
              requestContext: GraphQLRequestContext<Context>,
            ) {
              // Log GraphQL errors for security monitoring
              const errors = requestContext.errors || [];
              requestContext.contextValue.logSecurityEvent("graphql_error", {
                errors: errors.map((error: any) => ({
                  message: error.message,
                  code: error.extensions?.code,
                  path: error.path,
                  extensions: error.extensions,
                })),
                operation: requestContext.request.operationName,
              });
            },
            async willSendResponse(
              requestContext: GraphQLRequestContext<Context>,
            ) {
              // Advanced Security: Performance monitoring
              const latency =
                Date.now() - requestContext.contextValue.audit.requestStartTime;
              const errors = requestContext.errors || [];

              // Monitor operation performance
              monitorOperationPerformance(requestContext.contextValue, latency);

              // Audit operation completion
              requestContext.contextValue.logSecurityEvent(
                "graphql_operation_complete",
                {
                  operation: requestContext.request.operationName,
                  latency,
                  success: errors.length === 0,
                  errorCount: errors.length,
                },
              );
            },
          };
        },
      },
    ],
    // Enhanced error formatting for security
    formatError: (err: any) => {
      console.error("GraphQL Error:", err);
      return securityErrorFormatter(err);
    },
  });

  await apollo.start();

  // Initialize SuperTokens roles and permissions
  try {
    await initializeRoles();
  } catch (error) {
    console.error("❌ Failed to initialize roles:", error);
    // Continue without roles - they can be initialized later
  }

  // Cache warmup for optimal performance
  try {
    const { AuthService } = await import("./auth/authService.js");
    const authService = new AuthService(prisma, redis);
    await authService.warmupPermissionsCache();
  } catch (error) {
    console.error("❌ Failed to warm up permissions cache:", error);
    // Continue without cache warmup - not critical for functionality
  }

  // --- Express Middlewares ---
  app.set("trust proxy", 1); // trust first proxy if using one (e.g. for Vercel, Heroku)
  app.use(
    cors({
      origin: process.env.FRONTEND_URL || "http://localhost:3000",
      credentials: true,
    }),
  );
  app.use(express.json({ limit: "10mb" })); // Add size limit for security
  app.use(express.urlencoded({ extended: true, limit: "10mb" }));

  // SuperTokens middleware for cookie handling
  app.use(middleware());

  // Auth stats middleware для auth endpoints
  app.use("/auth", createAuthStatsMiddleware(redis));

  // GraphQL endpoint with integrated auth context (NO separate auth middleware)
  app.use(
    "/graphql",
    expressMiddleware(apollo, {
      context: async ({ req, res }) => {
        console.log("🔐 Apollo Context: Processing GraphQL request", {
          path: req.path,
          method: req.method,
          hasCookies: !!req.headers.cookie,
        });

        // ✅ ИСПРАВЛЕНО: Auth проверка прямо в Apollo context
        return await createGraphQLContext({
          req: req as express.Request & { authContext?: AuthContext },
          res,
          prisma,
          redis,
        });
      },
    }),
  );

  app.get("/", (req, res) => {
    res.send("BFF is running. GraphQL at /graphql.");
  });

  // Advanced Security: Security reports endpoint
  app.get("/security/report", async (req, res) => {
    try {
      const context = await createGraphQLContext({
        req: req as express.Request & { authContext?: AuthContext },
        res,
        prisma,
        redis,
      });

      // Require admin role for security reports
      if (!context.user?.roles.includes("admin")) {
        return res.status(403).json({ error: "Admin access required" });
      }

      const report = await generateSecurityReport(context);
      res.json(report);
    } catch (error) {
      res.status(500).json({ error: "Failed to generate security report" });
    }
  });

  // Debug endpoint to clear rate limiting keys (development only)
  // TODO: Remove this endpoint before production
  app.post("/debug/clear-rate-limits", async (req, res) => {
    if (process.env.NODE_ENV === "production") {
      return res
        .status(403)
        .json({ error: "Debug endpoints not available in production" });
    }

    try {
      // Clear all rate limiting keys
      const pattern = "*rate_limit*";
      const keys = await redis.keys(pattern);

      if (keys.length > 0) {
        await redis.del(...keys);
        console.log(`🧹 Cleared ${keys.length} rate limiting keys`);
        res.json({
          success: true,
          cleared: keys.length,
          message: `Cleared ${keys.length} rate limiting keys`,
        });
      } else {
        res.json({
          success: true,
          cleared: 0,
          message: "No rate limiting keys to clear",
        });
      }
    } catch (error) {
      console.error("❌ Failed to clear rate limiting keys:", error);
      res.status(500).json({
        error: "Failed to clear rate limiting keys",
        message: error instanceof Error ? error.message : String(error),
      });
    }
  });

  // Enhanced health check with security status
  app.get("/health", async (req, res) => {
    try {
      // Check database connectivity
      await prisma.$queryRaw`SELECT 1`;

      // Check Redis connectivity
      const redisStatus = redis.status === "ready";

      // Get security stats
      const securityEvents = await redis.llen("security_events");
      const securityAlerts = await redis.llen("security_alerts");

      res.json({
        status: "ok",
        service: "BFF",
        auth: {
          supertokens:
            process.env.SUPERTOKENS_CORE_URL || "http://localhost:3567",
          redis: redisStatus,
          securityEvents,
          securityAlerts,
        },
        database: {
          status: "connected",
          provider: "postgresql",
        },
        security: {
          introspection: introspectionSecurity.introspection,
          queryDepthLimit: 10,
          rateLimiting: true,
          auditLogging: true,
          piiProtection: true,
          gdprCompliance: true,
        },
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      res.status(503).json({
        status: "error",
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString(),
      });
    }
  });

  const bffPort = process.env.PORT || "4000";
  const bffUrl = process.env.BFF_URL || `http://localhost:${bffPort}`;

  // --- Start Server ---
  await new Promise<void>((resolve) =>
    httpServer.listen({ port: parseInt(bffPort) }, resolve),
  );

  console.log(`🚀 BFF ready at ${bffUrl}`);
  console.log(`🚀 GraphQL ready at ${bffUrl}/graphql`);
  console.log(`📊 Auth stats at ${bffUrl}/auth/stats`);
  console.log(`🛡️  Security features enabled:`);
  console.log(`   - Authentication directives: @authenticated`);
  console.log(`   - Role-based access: @hasRole(role: "admin")`);
  console.log(`   - Tenant isolation: @belongsToTenant`);
  console.log(`   - Rate limiting: @rateLimit(max: 100, window: 60)`);
  console.log(`   - Audit logging: Real-time security events`);
  console.log(
    `   - Introspection: ${process.env.NODE_ENV !== "production" ? "Enabled (dev)" : "Disabled (prod)"}`,
  );
}

startApp().catch(async (error: unknown) => {
  const errorMessage =
    error instanceof Error ? error.message : "Unknown error occurred";
  process.stderr.write(`Failed to start BFF server: ${errorMessage}\n`);
  await prisma
    .$disconnect()
    .catch((e: unknown) =>
      process.stderr.write(
        `Error disconnecting Prisma: ${e instanceof Error ? e.message : String(e)}\n`,
      ),
    );
  await redis
    .quit()
    .catch((e: unknown) =>
      process.stderr.write(
        `Error disconnecting Redis: ${e instanceof Error ? e.message : String(e)}\n`,
      ),
    );
  process.exit(1);
});
