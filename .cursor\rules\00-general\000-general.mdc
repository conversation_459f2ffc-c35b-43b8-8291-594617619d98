---
description: 
globs: 
alwaysApply: false
---
## General

- First, analyze the code base for the task to determine whether it is partially or fully implemented.
- Ignore documentation files, as it is often irrelevant. Draw conclusions only from your analysis of the code base.
- Use critical thinking in analysis. Figure out task, then propose a few professional solutions with the best practices and security. Let me choose an approach before you code.
- Before starting, break the task into clear, sequential subtasks, and display a checklist with status markers (e.g., Not started/In progress/Completed); before each subtask step, show the current subtask checklist with updated statuses.
- Remove unnecessary comments that do not satisfy the Comments section.
- Simplify the implementation or create new data only when you are asked to do so.
- Create or update documentation only when asked to do so.
- Delete temporary test files, mock data, debug messages. After successfully passing the hypothesis testing.


## Validation Over Modification

- Prioritize extensive validation and comprehensive understanding of existing code and its operational context before introducing alterations or creating new artifacts.


## Task Focus

- Only make changes that are directly related to the assigned task.
- DO NOT make changes to code that is not directly relevant to the current task.
- DO NOT fix pre-existing issues (including linter errors) unless they were introduced by your changes within the current task or directly impede its completion.


## Security

- During development, pay attention to potential vulnerabilities and write secure code.
- Consider security best practices (e.g., input validation, output escaping, avoiding SQL injection, checking permissions).

## Error Handling
- Implement robust error handling mechanisms (e.g., try-catch blocks, Result types, checking return codes) to improve code reliability.
- Pay special attention to error handling when dealing with file I/O, network requests, database interactions, and other external systems or resources.
- Ensure that exceptional and unexpected situations are handled appropriately.

## Comments
- Add comments only to explain **non-obvious** or **complex** logic, important **business decisions**, or the **reasoning** ('why') behind a specific, perhaps non-standard, approach.
- **DO NOT add** trivial comments that merely duplicate what is already **clear from the code** itself (e.g., `// Increment counter` for `counter++;`).
- Strive for self-documenting code. Comments should supplement the code, not repeat it.
