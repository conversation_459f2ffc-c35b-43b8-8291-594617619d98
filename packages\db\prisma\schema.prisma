generator client {
  provider = "prisma-client-js"
  output   = "../generated/client"
  previewFeatures = ["multiSchema"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  schemas  = ["public", "auth"]
}

model User {
  id               String    @id @default(cuid())
  authProviderId   String?   @unique // SuperTokens/KeyCloak ID
  ourTrackingId    String    @unique @default(cuid())
  authProviderType String    @default("supertokens")
  tenantId         String    @default("public") // SuperTokens tenant ID
  lastAuthSync     DateTime? 
  createdAt        DateTime  @default(now())
  updatedAt        DateTime  @updatedAt
  
  // Relations for resource ownership
  ownedProjects    Project[] @relation("ProjectOwner")
  
  @@index([tenantId])
  @@schema("public")
}

model Project {
  id          String   @id @default(cuid())
  name        String
  description String?
  ownerId     String   // User who owns this project
  tenantId    String   // Tenant isolation
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  // Relations
  owner       User     @relation("ProjectOwner", fields: [ownerId], references: [id], onDelete: Cascade)
  
  // Indexes for performance
  @@index([ownerId])
  @@index([tenantId])
  @@index([ownerId, tenantId])
  @@schema("public")
}
