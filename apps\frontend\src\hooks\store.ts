import { useDispatch, useSelector, useStore } from "react-redux";
import type {
  RootState,
  AppDispatch,
  AppStore,
  TypedUseSelectorHook,
} from "@/types/store";

// Pre-typed hooks for the application
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;
export const useAppStore = () => useStore<AppStore>();

// Alias hooks for convenience (common pattern)
export const useReduxDispatch = useAppDispatch;
export const useReduxSelector = useAppSelector;
export const useReduxStore = useAppStore;
