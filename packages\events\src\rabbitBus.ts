import * as amqp from "amqplib";
import { EventBus, EventEnvelope, Unsubscribe } from "./index";

interface RabbitConnection {
  on(event: "error" | "close", listener: (err?: any) => void): unknown;
  createChannel(): Promise<RabbitChannel>;
  close(): Promise<void>;
}

interface RabbitChannel {
  assertExchange(
    exchange: string,
    type: string,
    options?: amqp.Options.AssertExchange,
  ): Promise<amqp.Replies.AssertExchange>;

  publish(
    exchange: string,
    routingKey: string,
    content: Buffer,
    options?: amqp.Options.Publish,
  ): boolean;

  once(event: string, listener: (...args: any[]) => void): unknown;

  assertQueue(
    queue: string,
    options?: amqp.Options.AssertQueue,
  ): Promise<amqp.Replies.AssertQueue>;

  bindQueue(
    queue: string,
    source: string,
    pattern: string,
    args?: any,
  ): Promise<amqp.Replies.Empty>;

  prefetch(count: number, global?: boolean): Promise<amqp.Replies.Empty>;

  consume(
    queue: string,
    onMessage: (msg: amqp.ConsumeMessage | null) => void,
    options?: amqp.Options.Consume,
  ): Promise<amqp.Replies.Consume>;

  ack(message: amqp.Message, allUpTo?: boolean): void;
  nack(message: amqp.Message, allUpTo?: boolean, requeue?: boolean): void;

  cancel(consumerTag: string): Promise<amqp.Replies.Empty>;
  unbindQueue(
    queue: string,
    source: string,
    pattern: string,
    args?: any,
  ): Promise<amqp.Replies.Empty>;

  close(): Promise<void>;
}

interface RabbitBusState {
  connection: RabbitConnection | null;
  channel: RabbitChannel | null;
  connecting: Promise<void> | null;
}

export function createRabbitBus(
  connectionUrl: string,
  exchangeName: string = "pulse.topic",
): EventBus {
  const state: RabbitBusState = {
    connection: null,
    channel: null,
    connecting: null,
  };

  // Message buffer for messages that need to be sent while connecting
  const messageBuffer: Array<{
    topic: string;
    message: EventEnvelope<unknown>;
    resolve: () => void;
    reject: (err: Error) => void;
  }> = [];

  // Connect to RabbitMQ and create a channel
  async function connect(): Promise<void> {
    if (state.connection && state.channel) {
      return; // Already connected
    }

    if (state.connecting) {
      return state.connecting; // Connection in progress
    }

    state.connecting = (async () => {
      try {
        console.log(`[RabbitBus] Connecting to ${connectionUrl}...`);
        const connection = (await amqp.connect(
          connectionUrl,
        )) as unknown as RabbitConnection;
        state.connection = connection;

        // Handle connection errors and close
        connection.on("error", (err) => {
          console.error("[RabbitBus] Connection error:", err);
          // Reset state so we can reconnect
          state.connection = null;
          state.channel = null;
          state.connecting = null;
        });

        connection.on("close", () => {
          console.log("[RabbitBus] Connection closed");
          // Reset state so we can reconnect
          state.connection = null;
          state.channel = null;
          state.connecting = null;
        });

        const channel = await connection.createChannel();
        state.channel = channel;

        await channel.assertExchange(exchangeName, "topic", {
          durable: true,
        });

        console.log(
          `[RabbitBus] Connected and ready with exchange: ${exchangeName}`,
        );

        // Process any buffered messages
        if (messageBuffer.length > 0) {
          console.log(
            `[RabbitBus] Processing ${messageBuffer.length} buffered messages`,
          );

          const messagestoProcess = [...messageBuffer];
          messageBuffer.length = 0; // Clear the buffer

          for (const { topic, message, resolve, reject } of messagestoProcess) {
            try {
              await publishMessage(topic, message);
              resolve();
            } catch (err) {
              reject(err as Error);
            }
          }
        }
      } catch (err) {
        console.error("[RabbitBus] Failed to connect:", err);
        state.connection = null;
        state.channel = null;
        throw err;
      } finally {
        state.connecting = null;
      }
    })();

    return state.connecting;
  }

  // Internal function to publish a message to RabbitMQ
  async function publishMessage<E>(
    topic: string,
    message: EventEnvelope<E>,
  ): Promise<void> {
    if (!state.channel) {
      throw new Error("Not connected to RabbitMQ");
    }

    const content = Buffer.from(JSON.stringify(message));
    const result = state.channel.publish(exchangeName, topic, content, {
      persistent: true,
      contentType: "application/json",
    });

    // If the publish was not successful, wait for drain event
    if (!result) {
      return new Promise<void>((resolve, reject) => {
        state.channel?.once("drain", () => resolve());
        // Add a timeout to avoid hanging forever
        setTimeout(
          () => reject(new Error("Publish timed out waiting for drain")),
          30000,
        );
      });
    }
  }

  // The EventBus interface implementation
  return {
    publish: async <E>(
      topic: string,
      message: EventEnvelope<E>,
    ): Promise<void> => {
      try {
        // Ensure connection is established
        if (!state.connection || !state.channel) {
          // If we're not connected, buffer the message and try to connect
          return new Promise<void>((resolve, reject) => {
            messageBuffer.push({
              topic,
              message,
              resolve,
              reject,
            });

            // Try to connect
            connect().catch(reject);
          });
        }

        // We're connected, publish the message
        return publishMessage(topic, message);
      } catch (err) {
        console.error(`[RabbitBus] Error publishing to ${topic}:`, err);
        throw err;
      }
    },

    subscribe: async <E>(
      topic: string,
      handler: (message: EventEnvelope<E>) => Promise<void>,
      options?: { consumerTag?: string; concurrency?: number },
    ): Promise<Unsubscribe> => {
      try {
        // Ensure connection is established
        await connect();

        if (!state.channel) {
          throw new Error("Failed to create channel");
        }

        // Assert exchange again (idempotent)
        await state.channel.assertExchange(exchangeName, "topic", {
          durable: true,
        });

        // Create a queue - use consumerTag as queue name if provided, otherwise let RabbitMQ generate one
        const queueName = options?.consumerTag || "";
        const { queue } = await state.channel.assertQueue(queueName, {
          exclusive: !options?.consumerTag, // If no consumerTag, make it exclusive (auto-delete)
          durable: !!options?.consumerTag, // If consumerTag provided, make it durable
        });

        // Bind the queue to the exchange with the topic as routing key
        await state.channel.bindQueue(queue, exchangeName, topic);

        // Limit the number of unacknowledged messages (concurrency control)
        const prefetchCount = options?.concurrency || 1;
        await state.channel.prefetch(prefetchCount);

        // Start consuming
        const { consumerTag } = await state.channel.consume(
          queue,
          async (msg) => {
            if (msg === null) {
              console.warn("[RabbitBus] Consumer cancelled by server");
              return;
            }

            try {
              // Parse the message content
              const content = msg.content.toString();
              const eventEnvelope = JSON.parse(content) as EventEnvelope<E>;

              // Process the message
              await handler(eventEnvelope);

              // Acknowledge the message
              state.channel?.ack(msg);
            } catch (err) {
              console.error("[RabbitBus] Error processing message:", err);
              // Negative acknowledge and requeue
              state.channel?.nack(msg, false, true);
            }
          },
          {
            noAck: false, // Require explicit acknowledgment
          },
        );

        // Return a function to unsubscribe (cancel consumer)
        return async (): Promise<void> => {
          if (state.channel) {
            await state.channel.cancel(consumerTag);

            // If the queue was exclusive, it'll be deleted automatically
            // If it was not exclusive, we can delete it explicitly if desired
            if (options?.consumerTag) {
              try {
                await state.channel.unbindQueue(queue, exchangeName, topic);
                // Uncomment if you want to delete the queue when unsubscribing
                // await state.channel.deleteQueue(queue);
              } catch (err) {
                console.error("[RabbitBus] Error cleaning up queue:", err);
              }
            }
          }
        };
      } catch (err) {
        console.error(`[RabbitBus] Error subscribing to ${topic}:`, err);
        throw err;
      }
    },

    disconnect: async (): Promise<void> => {
      // Close channel and connection if they exist
      if (state.channel) {
        try {
          await state.channel.close();
        } catch (err) {
          console.error("[RabbitBus] Error closing channel:", err);
        } finally {
          state.channel = null;
        }
      }

      if (state.connection) {
        try {
          await state.connection.close();
        } catch (err) {
          console.error("[RabbitBus] Error closing connection:", err);
        } finally {
          state.connection = null;
        }
      }

      state.connecting = null;
      console.log("[RabbitBus] Disconnected");
    },
  };
}
