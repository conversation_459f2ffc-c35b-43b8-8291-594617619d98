import { PrismaClient } from "@pulsepanel/db/generated/client";
import { AuthService, DEFAULT_ROLE } from "../auth/authService.js";
import type { Context } from "../types/context.js";

const prisma = new PrismaClient();

export const accountResolvers = {
  Query: {
    me: async (_: any, __: any, context: Context) => {
      if (!context.user) {
        throw new Error("Authentication required");
      }

      try {
        const user = await prisma.user.findUnique({
          where: { id: context.user.id },
        });

        if (!user) {
          throw new Error("User not found");
        }

        // Get user roles from SuperTokens
        const authService = new AuthService(context.prisma, context.redis);
        const rolesResponse = await authService.getUserRoles(
          "public",
          context.user.id,
        );
        const roles =
          rolesResponse.success && rolesResponse.roles
            ? rolesResponse.roles
            : [DEFAULT_ROLE];

        return {
          id: user.id,
          name: context.user.name, // from SuperTokens metadata
          tenantId: user.tenantId,
          roles,
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
        };
      } catch (error) {
        console.error("Error fetching current user:", error);
        throw new Error("Failed to fetch user profile");
      }
    },

    user: async (_: any, { id }: { id: string }, context: Context) => {
      if (!context.user) {
        throw new Error("Authentication required");
      }

      try {
        const user = await prisma.user.findUnique({
          where: { id },
        });

        if (!user) {
          throw new Error(`User with id ${id} not found`);
        }

        // Check tenant access
        if (user.tenantId !== context.user.tenantId) {
          throw new Error("Access denied to user from different tenant");
        }

        // Get user roles from SuperTokens
        const authService = new AuthService(context.prisma, context.redis);
        const rolesResponse = await authService.getUserRoles("public", id);
        const roles =
          rolesResponse.success && rolesResponse.roles
            ? rolesResponse.roles
            : [DEFAULT_ROLE];

        return {
          id: user.id,
          name: null, // TODO: fetch from SuperTokens metadata
          tenantId: user.tenantId,
          roles,
          createdAt: user.createdAt.toISOString(),
          updatedAt: user.updatedAt.toISOString(),
        };
      } catch (error) {
        console.error("Error fetching user:", error);
        throw new Error("Failed to fetch user");
      }
    },

    users: async (_: any, __: any, context: Context) => {
      if (!context.user) {
        throw new Error("Authentication required");
      }

      // Check if user has manager role
      if (
        !context.user.roles.includes("manager") &&
        !context.user.roles.includes("admin")
      ) {
        throw new Error("Insufficient permissions");
      }

      try {
        const users = await prisma.user.findMany({
          where: {
            tenantId: context.user.tenantId,
          },
          orderBy: {
            createdAt: "desc",
          },
        });

        const authService = new AuthService(context.prisma, context.redis);

        // Fetch roles for each user
        const usersWithRoles = await Promise.all(
          users.map(async (user) => {
            const rolesResponse = await authService.getUserRoles(
              "public",
              user.id,
            );
            const roles =
              rolesResponse.success && rolesResponse.roles
                ? rolesResponse.roles
                : [DEFAULT_ROLE];

            return {
              id: user.id,
              name: null, // TODO: fetch from SuperTokens metadata
              tenantId: user.tenantId,
              roles, // Include user roles from SuperTokens
              createdAt: user.createdAt.toISOString(),
              updatedAt: user.updatedAt.toISOString(),
            };
          }),
        );

        return usersWithRoles;
      } catch (error) {
        console.error("Error fetching users:", error);
        throw new Error("Failed to fetch users");
      }
    },

    // =============================================================================
    // RESOURCE OWNERSHIP VALIDATION - for middleware
    // =============================================================================
    checkResourceOwnership: async (
      _: any,
      {
        resourceType,
        resourceId,
      }: { resourceType: string; resourceId: string },
      context: Context,
    ) => {
      if (!context.user) {
        return {
          success: false,
          hasAccess: false,
          reason: "authentication_required",
          error: { code: "AUTH_REQUIRED", message: "Authentication required" },
        };
      }

      try {
        const userRoles = context.user.roles || [];
        const isAdmin = userRoles.includes("admin");
        const isManager = userRoles.includes("manager");

        // Admin users have access to all resources
        if (isAdmin) {
          return {
            success: true,
            hasAccess: true,
            reason: "admin_access",
            error: null,
          };
        }

        switch (resourceType) {
          case "project": {
            const project = await prisma.project.findUnique({
              where: { id: resourceId },
              include: { owner: true },
            });

            if (!project) {
              return {
                success: true,
                hasAccess: false,
                reason: "resource_not_found",
                error: { code: "NOT_FOUND", message: "Project not found" },
              };
            }

            // Check tenant isolation
            if (project.tenantId !== context.user.tenantId) {
              return {
                success: true,
                hasAccess: false,
                reason: "tenant_access_denied",
                error: {
                  code: "TENANT_DENIED",
                  message: "Cross-tenant access denied",
                },
              };
            }

            // Check ownership or manager access
            const isOwner = project.ownerId === context.user.id;
            const hasAccess = isOwner || isManager;

            return {
              success: true,
              hasAccess,
              reason: isOwner
                ? "owner_access"
                : isManager
                  ? "manager_access"
                  : "access_denied",
              error: null,
            };
          }

          case "user": {
            const targetUser = await prisma.user.findUnique({
              where: { id: resourceId },
            });

            if (!targetUser) {
              return {
                success: true,
                hasAccess: false,
                reason: "resource_not_found",
                error: { code: "NOT_FOUND", message: "User not found" },
              };
            }

            // Check tenant isolation
            if (targetUser.tenantId !== context.user.tenantId) {
              return {
                success: true,
                hasAccess: false,
                reason: "tenant_access_denied",
                error: {
                  code: "TENANT_DENIED",
                  message: "Cross-tenant access denied",
                },
              };
            }

            // Check self-access or manager access
            const isSelf = targetUser.id === context.user.id;
            const hasAccess = isSelf || isManager;

            return {
              success: true,
              hasAccess,
              reason: isSelf
                ? "self_access"
                : isManager
                  ? "manager_access"
                  : "access_denied",
              error: null,
            };
          }

          default:
            return {
              success: true,
              hasAccess: false,
              reason: "unsupported_resource_type",
              error: {
                code: "UNSUPPORTED",
                message: `Resource type '${resourceType}' not supported`,
              },
            };
        }
      } catch (error) {
        console.error("Error checking resource ownership:", error);
        return {
          success: false,
          hasAccess: false,
          reason: "internal_error",
          error: {
            code: "INTERNAL_ERROR",
            message: "Failed to check resource ownership",
          },
        };
      }
    },

    // =============================================================================
    // PROJECT QUERIES - basic CRUD
    // =============================================================================
    projects: async (_: any, __: any, context: Context) => {
      if (!context.user) {
        throw new Error("Authentication required");
      }

      try {
        const projects = await prisma.project.findMany({
          where: {
            tenantId: context.user.tenantId,
            isActive: true,
          },
          include: {
            owner: true,
          },
          orderBy: {
            updatedAt: "desc",
          },
        });

        return projects.map((project) => ({
          id: project.id,
          name: project.name,
          description: project.description,
          ownerId: project.ownerId,
          tenantId: project.tenantId,
          isActive: project.isActive,
          owner: {
            id: project.owner.id,
            name: null, // TODO: fetch from SuperTokens metadata
            tenantId: project.owner.tenantId,
            roles: [], // TODO: fetch roles if needed
            createdAt: project.owner.createdAt.toISOString(),
            updatedAt: project.owner.updatedAt.toISOString(),
          },
          createdAt: project.createdAt.toISOString(),
          updatedAt: project.updatedAt.toISOString(),
        }));
      } catch (error) {
        console.error("Error fetching projects:", error);
        throw new Error("Failed to fetch projects");
      }
    },

    project: async (_: any, { id }: { id: string }, context: Context) => {
      if (!context.user) {
        throw new Error("Authentication required");
      }

      try {
        const project = await prisma.project.findUnique({
          where: { id },
          include: {
            owner: true,
          },
        });

        if (!project) {
          throw new Error("Project not found");
        }

        // Check tenant access (enforced by @belongsToTenant directive)
        if (project.tenantId !== context.user.tenantId) {
          throw new Error("Access denied to project from different tenant");
        }

        return {
          id: project.id,
          name: project.name,
          description: project.description,
          ownerId: project.ownerId,
          tenantId: project.tenantId,
          isActive: project.isActive,
          owner: {
            id: project.owner.id,
            name: null, // TODO: fetch from SuperTokens metadata
            tenantId: project.owner.tenantId,
            roles: [], // TODO: fetch roles if needed
            createdAt: project.owner.createdAt.toISOString(),
            updatedAt: project.owner.updatedAt.toISOString(),
          },
          createdAt: project.createdAt.toISOString(),
          updatedAt: project.updatedAt.toISOString(),
        };
      } catch (error) {
        console.error("Error fetching project:", error);
        throw new Error("Failed to fetch project");
      }
    },
  },
};

export default accountResolvers;
