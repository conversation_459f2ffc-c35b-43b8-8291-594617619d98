import type { Request, Response } from "express";
import { PrismaClient } from "@pulsepanel/db/generated/client";
import Redis from "ioredis";
import type { SessionData } from "../auth/types.js";

export interface SecurityContext {
  ipAddress: string;
  userAgent: string;
  requestId: string;
  timestamp: Date;
  rateLimit?: {
    remaining: number;
    resetTime: Date;
  };
}

export interface UserContext {
  id: string;
  name?: string;
  tenantId: string;
  roles: string[];
  permissions: string[];
}

export interface AuditContext {
  operationName?: string;
  operationType: "query" | "mutation" | "subscription";
  variables?: Record<string, any>;
  requestStartTime: number;
}

export interface Context {
  // Express request/response (needed for auth operations)
  req: Request;
  res: Response;

  // Database connections
  prisma: PrismaClient;
  redis: Redis;

  // Authentication state
  isAuthenticated: boolean;
  session?: SessionData;

  // User context (populated when authenticated)
  user?: UserContext;
  currentTenant?: {
    id: string;
    name: string;
    roles: string[];
  };

  // Security context
  security: SecurityContext;

  // Audit context
  audit: AuditContext;

  // Helper methods
  requireAuth(): void;
  requireRole(role: string): void;
  requireTenantAccess(tenantId: string): void;
  logSecurityEvent(event: string, details?: Record<string, any>): void;
}
