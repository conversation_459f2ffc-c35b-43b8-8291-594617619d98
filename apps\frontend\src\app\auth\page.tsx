"use client";

import React from "react";
import { useAuth } from "@/hooks/features";
import Link from "next/link";

export default function AuthIndexPage() {
  const auth = useAuth();

  const testPages = [
    {
      title: "Sign In",
      path: "/auth/signin",
      color: "blue",
      description: "Test sign in functionality",
      examples: [
        "?email=<EMAIL>&password=test123",
        "?email=<EMAIL>&password=test123&auto=true",
        "?email=<EMAIL>&password=test123&redirect=/dashboard",
      ],
    },
    {
      title: "Sign Up",
      path: "/auth/signup",
      color: "green",
      description: "Test sign up functionality",
      examples: [
        "?email=<EMAIL>&password=test123&tenantId=default",
        "?email=<EMAIL>&password=test123&tenantId=default&auto=true",
        "?email=<EMAIL>&password=test123&name=John%20Doe&tenantId=company1",
      ],
    },
    {
      title: "Logout",
      path: "/auth/logout",
      color: "red",
      description: "Test logout functionality",
      examples: [
        "?auto=true",
        "?auto=true&redirect=/",
        "?auto=true&redirect=/auth/signin&delay=1000",
      ],
    },
    {
      title: "Forgot Password",
      path: "/auth/forgot-password",
      color: "purple",
      description: "Test forgot password functionality",
      examples: [
        "?email=<EMAIL>",
        "?email=<EMAIL>&auto=true",
        "?email=<EMAIL>&auto=true&redirect=/auth/signin",
      ],
    },
    {
      title: "Reset Password",
      path: "/auth/reset-password",
      color: "orange",
      description: "Test reset password functionality",
      examples: [
        "?token=abc123&password=newpass&confirmPassword=newpass",
        "?token=abc123&password=newpass&confirmPassword=newpass&auto=true",
        "?token=abc123&password=newpass&confirmPassword=newpass&redirect=/dashboard",
      ],
    },
  ];

  const getColorClasses = (color: string) => {
    const colors = {
      blue: "bg-blue-50 border-blue-200 text-blue-900 hover:bg-blue-100",
      green: "bg-green-50 border-green-200 text-green-900 hover:bg-green-100",
      red: "bg-red-50 border-red-200 text-red-900 hover:bg-red-100",
      purple:
        "bg-purple-50 border-purple-200 text-purple-900 hover:bg-purple-100",
      orange:
        "bg-orange-50 border-orange-200 text-orange-900 hover:bg-orange-100",
    };
    return colors[color as keyof typeof colors] || colors.blue;
  };

  return (
    <div className="min-h-screen bg-gray-50 py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-extrabold text-gray-900">
            Auth Test Pages
          </h1>
          <p className="mt-4 text-lg text-gray-600">
            Test all authentication functionality with URL parameters support
          </p>

          {auth.isAuthenticated && (
            <div className="mt-6 p-4 bg-green-50 border border-green-200 rounded-lg inline-block">
              <p className="text-sm text-green-800">
                ✅ <strong>Currently signed in</strong> as{" "}
                {auth.user?.name || "No name"} (ID: {auth.user?.id})
              </p>
            </div>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testPages.map((page) => (
            <div
              key={page.path}
              className={`${getColorClasses(page.color)} border rounded-lg p-6 transition-colors duration-200`}
            >
              <h3 className="text-lg font-semibold mb-2">{page.title}</h3>
              <p className="text-sm mb-4">{page.description}</p>

              <div className="space-y-3">
                <Link
                  href={page.path}
                  className="block w-full text-center py-2 px-4 bg-white border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 transition-colors duration-200"
                >
                  Open Page
                </Link>

                <div>
                  <h4 className="text-xs font-medium mb-2">Example URLs:</h4>
                  <div className="space-y-1">
                    {page.examples.map((example, index) => (
                      <Link
                        key={index}
                        href={`${page.path}${example}`}
                        className="block text-xs font-mono bg-white bg-opacity-50 p-2 rounded border hover:bg-opacity-75 transition-colors duration-200"
                      >
                        {page.path}
                        {example}
                      </Link>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-900 mb-4">
            URL Parameters Reference
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                Common Parameters:
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>
                  <code className="bg-gray-100 px-1 rounded">auto=true</code> -
                  Auto-execute action
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded">
                    redirect=/path
                  </code>{" "}
                  - Redirect after success
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded">
                    email=<EMAIL>
                  </code>{" "}
                  - Pre-fill email
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded">
                    password=password123
                  </code>{" "}
                  - Pre-fill password
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-medium text-gray-900 mb-2">
                Specific Parameters:
              </h3>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>
                  <code className="bg-gray-100 px-1 rounded">
                    tenantId=company1
                  </code>{" "}
                  - Tenant ID (signup)
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded">
                    name=John%20Doe
                  </code>{" "}
                  - Name (signup)
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded">
                    rememberMe=true
                  </code>{" "}
                  - Remember me (signin)
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded">delay=3000</code> -
                  Redirect delay in ms (logout)
                </li>
                <li>
                  <code className="bg-gray-100 px-1 rounded">token=abc123</code>{" "}
                  - Reset token (reset-password)
                </li>
              </ul>
            </div>
          </div>
        </div>

        <div className="mt-8 text-center">
          <p className="text-sm text-gray-500">
            Все страницы поддерживают комбинирование параметров через &amp;
          </p>
        </div>
      </div>
    </div>
  );
}
