# Dependencies
node_modules/
.pnp
.pnp.js

# Testing
coverage/
.nyc_output/

# Production
build/
dist/

# Environment variables
.env
.env.local
.env.*.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# IDE specific files
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Local development
.cache/
.temp/
.tmp/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.test

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Local Netlify folder
.netlify

# Build output for different environments
build/
dist/

# Local development files
*.local

# Misc
.DS_Store
*.pem

# Debug directory used by debugger
.vs/

# Dependency directories
jspm_packages/

# Optional stylehint configuration
.stylelintcache

# Optional prettier configuration
.prettiercache

# Optional lerna debug log
debug.log

# Optional pnpm debug log
pnpm-debug.log*

# Generated files
/src/generated/
