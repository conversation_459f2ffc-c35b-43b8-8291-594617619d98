# 🍪 Исправление проблемы с SuperTokens HttpOnly куки

## Проблема
При авторизации видно только один `sAccessToken`, отсутствуют другие токены (`sRefreshToken`, `sFrontToken`). Нужно исправить настройки для правильной работы HttpOnly куки.

## ✅ Исправления, которые были внесены

### 1. Обновлена конфигурация SuperTokens в BFF
**Файл:** `apps/bff/src/auth/supertokens.ts`

Добавлены недостающие настройки:
- Принудительное использование куки: `getTokenTransferMethod: () => "cookie"`
- Отладочная информация для мониторинга создания сессий
- Правильная настройка override функций

### 2. Обновлена конфигурация SuperTokens в Auth-API
**Файл:** `apps/auth-api/src/config/supertokens.ts`

Добавлены:
- Принудительное использование куки
- Отладочная информация для auth-api
- Синхронизация настроек с BFF

### 3. Улучшена отладка в Frontend Middleware
**Файл:** `apps/frontend/src/middleware.ts`

Добавлены:
- Детальная отладка куки в development режиме
- Анализ всех куки в запросах
- Логирование отсутствующих токенов

### 4. Созданы тестовые скрипты
**Файлы:** `test-cookies.sh`, `test-cookies.ps1`, `apps/bff/src/test-cookie-debug.ts`

## 🧪 Как протестировать исправления

### Вариант 1: Автоматический тест (рекомендуется)

**Linux/Mac:**
```bash
./test-cookies.sh
```

**Windows PowerShell:**
```powershell
.\test-cookies.ps1
```

### Вариант 2: Ручное тестирование

1. **Запустите все сервисы:**
```bash
# Terminal 1: SuperTokens Core
docker run -p 3567:3567 -d registry.supertokens.io/supertokens/supertokens-postgresql

# Terminal 2: Auth API
cd apps/auth-api && npm run dev

# Terminal 3: BFF
cd apps/bff && npm run dev

# Terminal 4: Frontend
cd apps/frontend && npm run dev
```

2. **Проверьте авторизацию:**
```bash
# Создайте пользователя
curl -X POST http://localhost:3001/auth/signup \
  -H "Content-Type: application/json" \
  -d '{
    "formFields": [
      {"id": "email", "value": "<EMAIL>"},
      {"id": "password", "value": "testpassword123"}
    ]
  }' \
  -c cookies.txt

# Войдите в систему
curl -X POST http://localhost:3001/auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "formFields": [
      {"id": "email", "value": "<EMAIL>"},
      {"id": "password", "value": "testpassword123"}
    ]
  }' \
  -c cookies.txt -b cookies.txt

# Проверьте куки
cat cookies.txt
```

3. **Проверьте GraphQL с куки:**
```bash
curl -X POST http://localhost:4000/graphql \
  -H "Content-Type: application/json" \
  -d '{"query": "query { checkAuthStatus }"}' \
  -b cookies.txt
```

## 🔍 Что искать в результатах

### ✅ Правильная работа (HttpOnly куки):
```
✅ sAccessToken cookie found
✅ sRefreshToken cookie found  
❌ sFrontToken cookie NOT found (это нормально для HttpOnly режима)
```

### ❌ Неправильная работа (Header режим):
```
❌ sAccessToken cookie NOT found
❌ sRefreshToken cookie NOT found
✅ sFrontToken cookie found (указывает на header режим)
```

## 🐛 Отладка

### 1. Проверьте логи BFF
В development режиме должны появиться логи:
```
🍪 BFF Session Created: { sessionHandle: "...", userId: "..." }
🍪 Cookie Check: { hasAccessToken: true, hasRefreshToken: true }
🍪 Cookie Analysis: { sessionCookiesFound: 2, hasAccessToken: true }
```

### 2. Проверьте переменные окружения
```bash
# BFF
echo $SUPERTOKENS_CORE_URL  # должно быть http://localhost:3567
echo $FRONTEND_URL          # должно быть http://localhost:3000

# Auth-API  
echo $SUPERTOKENS_CORE_URL  # должно быть http://localhost:3567
echo $WEBSITE_DOMAIN        # должно быть http://localhost:3000
```

### 3. Проверьте SuperTokens Core
```bash
curl http://localhost:3567/hello
# Должен вернуть: Hello
```

## 🔧 Дополнительные настройки

### Если проблема остается:

1. **Очистите кеш браузера** и куки для localhost
2. **Перезапустите все сервисы** в правильном порядке
3. **Проверьте CORS настройки** - должен быть `credentials: true`
4. **Убедитесь в версии SuperTokens** - должна быть совместимая

### Production настройки:
```env
NODE_ENV=production
COOKIE_DOMAIN=.yourdomain.com
COOKIE_SECURE=true
```

## 📋 Чеклист исправлений

- [x] Добавлен `getTokenTransferMethod: () => "cookie"` в BFF
- [x] Добавлен `getTokenTransferMethod: () => "cookie"` в Auth-API  
- [x] Добавлена отладочная информация в оба сервиса
- [x] Улучшена отладка куки в middleware
- [x] Созданы тестовые скрипты
- [x] CORS настроен с `credentials: true`
- [x] Документация обновлена

## 🎯 Ожидаемый результат

После применения исправлений вы должны видеть:
1. **sAccessToken** - основной токен доступа (HttpOnly)
2. **sRefreshToken** - токен обновления (HttpOnly) 
3. **Отсутствие sFrontToken** - это нормально для HttpOnly режима

Куки будут автоматически передаваться между frontend и BFF, обеспечивая безопасную аутентификацию.
