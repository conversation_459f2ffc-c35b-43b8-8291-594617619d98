import { configureStore, combineSlices } from "@reduxjs/toolkit";
import { IAuthService } from "@/auth";

// Import our slices
import { authSlice } from "@/features/auth/authSlice";
import { uiSlice } from "@/features/ui/uiSlice";

// Combine slices using RTK 2.0 combineSlices
const rootReducer = combineSlices(authSlice, uiSlice);

// Store-level authService holder
let storeAuthService: IAuthService | undefined;

// Function to inject authService into existing store
export function injectAuthService(authService: IAuthService) {
  storeAuthService = authService;
}

// Function to get current authService
export function getAuthService(): IAuthService | undefined {
  return storeAuthService;
}

// Store configuration function for SSR compatibility
export function makeStore() {
  const store = configureStore({
    reducer: rootReducer,
    middleware: (getDefaultMiddleware) =>
      getDefaultMiddleware({
        // Configure thunk middleware with dynamic authService getter
        thunk: {
          extraArgument: {
            get authService() {
              return storeAuthService;
            },
          },
        },
        // Enable state serialization checks in development
        serializableCheck: {
          ignoredActions: ["persist/PERSIST", "persist/REHYDRATE"],
        },
      }),

    // Redux DevTools configuration
    devTools: process.env.NODE_ENV !== "production" && {
      name: "PulsePanel Redux Store",
      trace: true,
      traceLimit: 25,
      actionsDenylist: ["auth/checkAuthStatus/pending"],
      stateSanitizer: (state: any) => ({
        ...state,
        // Hide sensitive auth data in DevTools
        auth: {
          ...state.auth,
          config: state.auth.config ? "[Config Hidden]" : null,
        },
      }),
    },
  });

  return store;
}

// Create store instance for client-side
let store: ReturnType<typeof makeStore> | undefined;

export function getStore() {
  // For SSR: always create a new store
  if (typeof window === "undefined") {
    return makeStore();
  }

  // For client: create store once and reuse
  if (!store) {
    store = makeStore();
  }

  return store;
}

// Export types
export type RootState = ReturnType<typeof rootReducer>;
export type AppStore = ReturnType<typeof makeStore>;
export type AppDispatch = AppStore["dispatch"];

// Export store for Next.js App Router
export default getStore;
