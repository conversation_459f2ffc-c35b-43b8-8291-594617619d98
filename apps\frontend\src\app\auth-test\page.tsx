"use client";

import dynamic from "next/dynamic";
import { AuthServiceProvider } from "@/auth";

const AuthTest = dynamic(
  () =>
    import("../../auth/components/AuthTest").then((mod) => ({
      default: mod.AuthTest,
    })),
  {
    ssr: false,
    loading: () => <p>Loading auth test component...</p>,
  },
);

const AuthDebug = dynamic(
  () =>
    import("../../auth/components/AuthDebug").then((mod) => ({
      default: mod.AuthDebug,
    })),
  {
    ssr: false,
    loading: () => <p>Loading auth debug component...</p>,
  },
);

export default function AuthTestPage() {
  return (
    <AuthServiceProvider>
      <div style={{ padding: "20px" }}>
        <h1>Auth Service Layer Test</h1>
        <p>Testing our transport-agnostic auth service implementation</p>

        <div
          style={{
            padding: "15px",
            border: "2px solid green",
            margin: "15px",
            backgroundColor: "#d4edda",
          }}
        >
          <h4>✅ Correct Auth Architecture</h4>
          <p>
            <strong>Auth Strategy:</strong> Backend-centric (Frontend asks
            backend, not cookies)
          </p>
          <p>
            <strong>Cookie Security:</strong> HttpOnly cookies are invisible to
            JavaScript (correct!)
          </p>
          <p>
            <strong>Auth Check:</strong> Frontend → BFF → SuperTokens →
            PostgreSQL → Response
          </p>
          <p>
            <strong>Cache Strategy:</strong> 30-second auth status cache for
            performance
          </p>
        </div>

        <AuthDebug />
        <AuthTest />
      </div>
    </AuthServiceProvider>
  );
}
