version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: pulsepanel-postgres
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_USER: pulsepanel_user
      POSTGRES_PASSWORD: pulsepanel_password
      POSTGRES_DB: pulsepanel_dev_db
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U pulsepanel_user -d pulsepanel_dev_db"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis:
    image: redis:7-alpine
    container_name: pulsepanel-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 5s

  rabbitmq:
    image: rabbitmq:3.13-management-alpine
    container_name: pulsepanel-rabbitmq
    restart: unless-stopped
    ports:
      - "5672:5672"   # AMQP port
      - "15672:15672" # Management UI
    volumes:
      - rabbitmq_data:/var/lib/rabbitmq
    healthcheck:
      test: ["CMD", "rabbitmq-diagnostics", "check_port_connectivity"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 15s
    environment:
      RABBITMQ_DEFAULT_USER: guest
      RABBITMQ_DEFAULT_PASS: guest

  # SuperTokens Core service
  supertokens-core:
    build: ./apps/supertokens-core
    container_name: pulsepanel-supertokens-core
    restart: unless-stopped
    ports:
      - "3567:3567"
    environment:
      POSTGRESQL_CONNECTION_URI: **************************************************************/pulsepanel_dev_db?options=-c%20search_path=auth
      API_KEYS: dev-api-key-secure-2024
    depends_on:
      postgres:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3567/hello"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 30s

  # PulsePanel Auth API (Express + SuperTokens integration)
  auth-api:
    build: ./apps/auth-api
    container_name: pulsepanel-auth-api
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - SUPERTOKENS_CORE_URL=http://supertokens-core:3567
      - SUPERTOKENS_API_KEY=dev-api-key-secure-2024
      - API_DOMAIN=http://localhost:3001
      - WEBSITE_DOMAIN=http://localhost:3000
      - FRONTEND_URL=http://localhost:3000
      - BFF_URL=http://localhost:4000
      - API_BASE_PATH=/auth
      - WEBSITE_BASE_PATH=/auth
      # PostgreSQL connection parameters
      - POSTGRES_HOST=postgres
      - POSTGRES_PORT=5432
      - POSTGRES_USER=pulsepanel_user
      - POSTGRES_PASSWORD=pulsepanel_password
      - POSTGRES_DB=pulsepanel_dev_db
    depends_on:
      supertokens-core:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

volumes:
  postgres_data:
  redis_data:
  rabbitmq_data:
