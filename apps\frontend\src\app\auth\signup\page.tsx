"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth, useAuthActions } from "@/hooks/features";

export default function SignUpPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const auth = useAuth();
  const authActions = useAuthActions();

  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [name, setName] = useState("");
  const [tenantId, setTenantId] = useState("default");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const urlEmail = searchParams.get("email");
    const urlPassword = searchParams.get("password");
    const urlName = searchParams.get("name");
    const urlTenantId = searchParams.get("tenantId");
    const autoExecute = searchParams.get("auto");

    if (urlEmail) setEmail(urlEmail);
    if (urlPassword) setPassword(urlPassword);
    if (urlName) setName(urlName);
    if (urlTenantId) setTenantId(urlTenantId);

    if (autoExecute === "true" && urlEmail && urlPassword && urlTenantId) {
      const executeSignUp = async () => {
        try {
          setLoading(true);
          setError(null);

          await authActions.signUp({
            email: urlEmail,
            password: urlPassword,
            name: urlName || undefined,
            tenantId: urlTenantId,
          });
        } catch (err) {
          setError(err instanceof Error ? err.message : "Sign up failed");
        } finally {
          setLoading(false);
        }
      };
      executeSignUp();
    }
  }, [searchParams, authActions]);

  useEffect(() => {
    if (auth.isAuthenticated) {
      const redirectUrl = searchParams.get("redirect") || "/dashboard";
      router.push(redirectUrl);
    }
  }, [auth.isAuthenticated, searchParams, router]);

  const handleSignUp = async () => {
    if (!email || !password || !tenantId) {
      setError("Email, password, and tenant ID are required");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      await authActions.signUp({
        email,
        password,
        name: name || undefined,
        tenantId,
      });
    } catch (err) {
      setError(err instanceof Error ? err.message : "Sign up failed");
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
    authActions.clearAuthError();
  };

  const currentError = error || auth.authError;

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Sign Up (Test Page)
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Test sign up with URL parameters support
          </p>
        </div>

        {auth.isCheckingAuth ? (
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-green-600 mx-auto"></div>
            <p className="mt-2 text-sm text-gray-600">
              Checking authentication...
            </p>
          </div>
        ) : (
          <div className="mt-8 space-y-6">
            {currentError && (
              <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded relative">
                <span className="block sm:inline">{currentError}</span>
                <button
                  onClick={clearError}
                  className="absolute top-0 bottom-0 right-0 px-4 py-3"
                >
                  <span className="sr-only">Dismiss</span>×
                </button>
              </div>
            )}

            <div className="space-y-4">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700"
                >
                  Email *
                </label>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter your email"
                  disabled={loading}
                />
              </div>

              <div>
                <label
                  htmlFor="password"
                  className="block text-sm font-medium text-gray-700"
                >
                  Password *
                </label>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter your password"
                  disabled={loading}
                />
              </div>

              <div>
                <label
                  htmlFor="name"
                  className="block text-sm font-medium text-gray-700"
                >
                  Name (optional)
                </label>
                <input
                  id="name"
                  type="text"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter your name"
                  disabled={loading}
                />
              </div>

              <div>
                <label
                  htmlFor="tenantId"
                  className="block text-sm font-medium text-gray-700"
                >
                  Tenant ID *
                </label>
                <input
                  id="tenantId"
                  type="text"
                  value={tenantId}
                  onChange={(e) => setTenantId(e.target.value)}
                  className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-green-500 focus:border-green-500"
                  placeholder="Enter tenant ID"
                  disabled={loading}
                />
              </div>

              <button
                onClick={handleSignUp}
                disabled={loading || !email || !password || !tenantId}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Signing up...
                  </>
                ) : (
                  "Sign Up"
                )}
              </button>
            </div>

            <div className="mt-6 p-4 bg-green-50 rounded-lg">
              <h3 className="text-sm font-medium text-green-900 mb-2">
                URL Parameters:
              </h3>
              <ul className="text-xs text-green-700 space-y-1">
                <li>
                  <code>?email=<EMAIL></code> - Pre-fill email
                </li>
                <li>
                  <code>?password=password123</code> - Pre-fill password
                </li>
                <li>
                  <code>?name=John%20Doe</code> - Pre-fill name
                </li>
                <li>
                  <code>?tenantId=company1</code> - Pre-fill tenant ID
                </li>
                <li>
                  <code>?auto=true</code> - Auto-execute sign up
                </li>
                <li>
                  <code>?redirect=/dashboard</code> - Redirect after success
                </li>
              </ul>
              <p className="text-xs text-green-600 mt-2">
                Example:{" "}
                <code>
                  /auth/signup?email=<EMAIL>&password=test123&tenantId=default&auto=true
                </code>
              </p>
            </div>

            <div className="text-center space-y-2">
              <p className="text-sm text-gray-600">
                Already have an account?{" "}
                <a
                  href="/auth/signin"
                  className="text-green-600 hover:text-green-500"
                >
                  Sign in
                </a>
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
