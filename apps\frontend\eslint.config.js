// apps/frontend/eslint.config.js
import eslintConfig from "@pulsepanel/eslint-config-custom";
import nextEslint from "@next/eslint-plugin-next";

/** @type {import('eslint').Flat.ConfigArray} */
export default [
  ...eslintConfig,
  {
    files: ["**/*.js", "**/*.jsx", "**/*.ts", "**/*.tsx"],
    plugins: {
      "@next/next": nextEslint,
    },
    rules: {
      ...nextEslint.configs.recommended.rules,
    },
  },
  {
    ignores: [
      "node_modules/**",
      "dist/**",
      ".next/**",
      ".vercel/**",
      "generated/**",
    ],
  },
];
