/**
 * Auth Configuration
 *
 * Central configuration for auth service layer.
 * Handles environment-specific settings and provides
 * defaults for different deployment environments.
 */

import type { AuthConfig } from "@/auth";

/**
 * Environment type
 */
export type Environment = "development" | "staging" | "production" | "test";

/**
 * Auth Provider type for future extensibility
 */
export type AuthProvider = "supertokens" | "keycloak" | "auth0" | "custom";

/**
 * Extended Auth Configuration with environment support
 */
export interface ExtendedAuthConfig extends AuthConfig {
  environment: Environment;
  authProvider: AuthProvider;
  endpoints: {
    bff: string;
    graphql: string;
    rest?: string;
    websocket?: string;
  };
  security: {
    csrfProtection: boolean;
    sessionTimeout: number;
    refreshThreshold: number; // Refresh token when this much time is left
  };
  cache: {
    enabled: boolean;
    ttl: number;
    maxSize?: number;
  };
  monitoring: {
    enabled: boolean;
    endpoint?: string;
    sampleRate: number;
  };
}

/**
 * Get current environment
 */
function getCurrentEnvironment(): Environment {
  if (typeof window === "undefined") {
    // Server-side
    return (process.env.NODE_ENV as Environment) || "development";
  }

  // Client-side
  return (
    (process.env.NEXT_PUBLIC_ENVIRONMENT as Environment) ||
    (process.env.NODE_ENV as Environment) ||
    "development"
  );
}

/**
 * Get current auth provider
 */
function getCurrentAuthProvider(): AuthProvider {
  return (
    (process.env.NEXT_PUBLIC_AUTH_PROVIDER as AuthProvider) || "supertokens"
  );
}

/**
 * Environment-specific configurations
 */
const environmentConfigs: Record<Environment, Partial<ExtendedAuthConfig>> = {
  development: {
    endpoints: {
      bff: process.env.NEXT_PUBLIC_BFF_URL || "http://localhost:4000",
      graphql:
        process.env.NEXT_PUBLIC_GRAPHQL_URL || "http://localhost:4000/graphql",
    },
    enableCaching: true,
    debugMode: true,
    security: {
      csrfProtection: false, // Disabled for dev convenience
      sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
      refreshThreshold: 5 * 60 * 1000, // 5 minutes
    },
    cache: {
      enabled: true,
      ttl: 5 * 60 * 1000, // 5 minutes
      maxSize: 100,
    },
    monitoring: {
      enabled: true,
      sampleRate: 1.0, // 100% sampling in dev
    },
  },

  staging: {
    endpoints: {
      bff:
        process.env.NEXT_PUBLIC_BFF_URL || "https://staging-api.pulsepanel.com",
      graphql:
        process.env.NEXT_PUBLIC_GRAPHQL_URL ||
        "https://staging-api.pulsepanel.com/graphql",
    },
    enableCaching: true,
    debugMode: false,
    security: {
      csrfProtection: true,
      sessionTimeout: 8 * 60 * 60 * 1000, // 8 hours
      refreshThreshold: 10 * 60 * 1000, // 10 minutes
    },
    cache: {
      enabled: true,
      ttl: 3 * 60 * 1000, // 3 minutes
      maxSize: 50,
    },
    monitoring: {
      enabled: true,
      sampleRate: 0.1, // 10% sampling
    },
  },

  production: {
    endpoints: {
      bff: process.env.NEXT_PUBLIC_BFF_URL || "https://api.pulsepanel.com",
      graphql:
        process.env.NEXT_PUBLIC_GRAPHQL_URL ||
        "https://api.pulsepanel.com/graphql",
    },
    enableCaching: false, // Disabled for consistency
    debugMode: false,
    security: {
      csrfProtection: true,
      sessionTimeout: 4 * 60 * 60 * 1000, // 4 hours
      refreshThreshold: 15 * 60 * 1000, // 15 minutes
    },
    cache: {
      enabled: false, // Rely on server-side caching
      ttl: 1 * 60 * 1000, // 1 minute if enabled
      maxSize: 20,
    },
    monitoring: {
      enabled: true,
      endpoint: process.env.NEXT_PUBLIC_MONITORING_URL,
      sampleRate: 0.05, // 5% sampling
    },
  },

  test: {
    endpoints: {
      bff: "http://localhost:4000",
      graphql: "http://localhost:4000/graphql",
    },
    enableCaching: false, // Predictable testing
    debugMode: false,
    security: {
      csrfProtection: false,
      sessionTimeout: 1 * 60 * 60 * 1000, // 1 hour
      refreshThreshold: 5 * 60 * 1000, // 5 minutes
    },
    cache: {
      enabled: false,
      ttl: 30 * 1000, // 30 seconds
      maxSize: 10,
    },
    monitoring: {
      enabled: false,
      sampleRate: 0,
    },
  },
};

/**
 * Auth provider specific configurations
 */
const authProviderConfigs: Record<AuthProvider, Partial<ExtendedAuthConfig>> = {
  supertokens: {
    // Current SuperTokens configuration
    sessionRefreshInterval: 5 * 60 * 1000, // 5 minutes
    rememberMeDefault: false,
  },

  keycloak: {
    // Future KeyCloak configuration
    sessionRefreshInterval: 10 * 60 * 1000, // 10 minutes
    rememberMeDefault: true,
    endpoints: {
      // KeyCloak would use different endpoints
      bff: process.env.NEXT_PUBLIC_KEYCLOAK_URL || "",
      graphql: "", // KeyCloak might not use GraphQL
      rest: process.env.NEXT_PUBLIC_KEYCLOAK_REST_URL || "",
    },
  },

  auth0: {
    // Future Auth0 configuration
    sessionRefreshInterval: 15 * 60 * 1000, // 15 minutes
    rememberMeDefault: true,
  },

  custom: {
    // Custom auth provider
    sessionRefreshInterval: 5 * 60 * 1000,
    rememberMeDefault: false,
  },
};

/**
 * Create complete auth configuration
 */
export function createAuthConfig(
  overrides?: Partial<ExtendedAuthConfig>,
): ExtendedAuthConfig {
  const environment = getCurrentEnvironment();
  const authProvider = getCurrentAuthProvider();

  // Base configuration
  const baseConfig: ExtendedAuthConfig = {
    // Basic config
    apiUrl: "",
    enableCaching: true,
    debugMode: false,
    rememberMeDefault: false,
    sessionRefreshInterval: 5 * 60 * 1000,

    // Extended config
    environment,
    authProvider,
    endpoints: {
      bff: "",
      graphql: "",
    },
    security: {
      csrfProtection: true,
      sessionTimeout: 4 * 60 * 60 * 1000,
      refreshThreshold: 10 * 60 * 1000,
    },
    cache: {
      enabled: true,
      ttl: 5 * 60 * 1000,
    },
    monitoring: {
      enabled: false,
      sampleRate: 0,
    },
  };

  // Merge configurations in order of precedence:
  // 1. Base config
  // 2. Environment-specific config
  // 3. Auth provider-specific config
  // 4. User overrides
  const config: ExtendedAuthConfig = {
    ...baseConfig,
    ...environmentConfigs[environment],
    ...authProviderConfigs[authProvider],
    ...overrides,
  };

  // Set derived values
  config.apiUrl = config.endpoints.bff;
  config.graphqlEndpoint = config.endpoints.graphql;

  return config;
}

/**
 * Get configuration for specific environment
 */
export function getConfigForEnvironment(env: Environment): ExtendedAuthConfig {
  return createAuthConfig({ environment: env });
}

/**
 * Validate configuration
 */
export function validateAuthConfig(config: ExtendedAuthConfig): string[] {
  const errors: string[] = [];

  if (!config.endpoints.bff) {
    errors.push("BFF endpoint is required");
  }

  if (!config.endpoints.graphql && config.authProvider === "supertokens") {
    errors.push("GraphQL endpoint is required for SuperTokens");
  }

  if (config.cache.ttl <= 0) {
    errors.push("Cache TTL must be positive");
  }

  if (config.security.sessionTimeout <= config.security.refreshThreshold) {
    errors.push("Session timeout must be greater than refresh threshold");
  }

  if (config.monitoring.sampleRate < 0 || config.monitoring.sampleRate > 1) {
    errors.push("Monitoring sample rate must be between 0 and 1");
  }

  return errors;
}

/**
 * Default auth configuration for the current environment
 */
export const defaultAuthConfig = createAuthConfig();

/**
 * Configuration helpers
 */
export const authConfigHelpers = {
  isDevelopment: () => getCurrentEnvironment() === "development",
  isProduction: () => getCurrentEnvironment() === "production",
  isSupertokens: () => getCurrentAuthProvider() === "supertokens",
  isKeycloak: () => getCurrentAuthProvider() === "keycloak",

  shouldEnableDebug: (config: ExtendedAuthConfig) =>
    config.debugMode || config.environment === "development",

  shouldEnableCache: (config: ExtendedAuthConfig) =>
    config.cache.enabled && config.environment !== "test",

  getRefreshInterval: (config: ExtendedAuthConfig) =>
    config.sessionRefreshInterval,

  getApiTimeout: (config: ExtendedAuthConfig) =>
    config.environment === "production" ? 30000 : 10000, // 30s prod, 10s dev
};
