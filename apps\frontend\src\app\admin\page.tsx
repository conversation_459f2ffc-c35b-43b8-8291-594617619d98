"use client";

export default function AdminDashboard() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center">
            <div className="flex-shrink-0">
              <div className="h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-red-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
                  />
                </svg>
              </div>
            </div>
            <div className="ml-4">
              <h1 className="text-3xl font-bold text-gray-900">
                Administrator Dashboard
              </h1>
              <p className="text-gray-600">
                System administration and management center
              </p>
            </div>
          </div>
        </div>

        {/* RBAC Success Alert */}
        <div className="mb-8 bg-green-50 border border-green-200 rounded-lg p-6">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg
                className="h-6 w-6 text-green-400"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-lg font-semibold text-green-800">
                🎉 RBAC Protection Working!
              </h3>
              <p className="text-green-700 mt-1">
                You have successfully accessed the admin area. Enterprise
                Role-Based Access Control (RBAC) with hierarchical permissions
                is functioning correctly.
              </p>
            </div>
          </div>
        </div>

        {/* Admin Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* System Management */}
          <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-blue-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <h3 className="ml-3 text-lg font-semibold text-gray-900">
                System Settings
              </h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Configure global system settings and parameters
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Required Permission:</span>
                <span className="font-mono text-blue-600">system:admin</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Access Level:</span>
                <span className="text-red-600 font-medium">Admin Only</span>
              </div>
            </div>
          </div>

          {/* User Management */}
          <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 rounded-lg bg-purple-100 flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-purple-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                  />
                </svg>
              </div>
              <h3 className="ml-3 text-lg font-semibold text-gray-900">
                User Administration
              </h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Manage user accounts, roles, and permissions
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Required Permission:</span>
                <span className="font-mono text-blue-600">manage:roles</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Access Level:</span>
                <span className="text-red-600 font-medium">Admin Only</span>
              </div>
            </div>
          </div>

          {/* Tenant Management */}
          <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
            <div className="flex items-center mb-4">
              <div className="h-10 w-10 rounded-lg bg-green-100 flex items-center justify-center">
                <svg
                  className="h-6 w-6 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                  />
                </svg>
              </div>
              <h3 className="ml-3 text-lg font-semibold text-gray-900">
                Tenant Management
              </h3>
            </div>
            <p className="text-gray-600 text-sm mb-4">
              Create and manage organizational tenants
            </p>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-500">Required Permission:</span>
                <span className="font-mono text-blue-600">manage:tenants</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-500">Access Level:</span>
                <span className="text-red-600 font-medium">Admin Only</span>
              </div>
            </div>
          </div>
        </div>

        {/* RBAC Test Panel */}
        <div className="bg-white rounded-lg shadow border border-gray-200 p-6">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">
            🧪 RBAC Testing Panel
          </h2>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Current Access */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Your Current Access
              </h3>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                  <span className="font-medium">Admin Dashboard</span>
                  <span className="text-green-600 font-semibold">
                    ✓ Granted
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                  <span className="font-medium">System Administration</span>
                  <span className="text-green-600 font-semibold">
                    ✓ Granted
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                  <span className="font-medium">User Management</span>
                  <span className="text-green-600 font-semibold">
                    ✓ Granted
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-green-50 border border-green-200 rounded">
                  <span className="font-medium">Tenant Management</span>
                  <span className="text-green-600 font-semibold">
                    ✓ Granted
                  </span>
                </div>
              </div>
            </div>

            {/* Test Other Routes */}
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Test Other Protected Routes
              </h3>
              <div className="space-y-3">
                <button
                  onClick={() => window.open("/manage", "_blank")}
                  className="w-full text-left p-3 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Manager Dashboard</span>
                    <span className="text-blue-600">→ Test Access</span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">
                    Should work (admin inherits manager)
                  </p>
                </button>

                <button
                  onClick={() =>
                    window.open("/tenant/example-tenant", "_blank")
                  }
                  className="w-full text-left p-3 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Tenant Dashboard</span>
                    <span className="text-blue-600">→ Test Access</span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">
                    Should work (admin has tenant access)
                  </p>
                </button>

                <button
                  onClick={() =>
                    window.open("/project/example-project", "_blank")
                  }
                  className="w-full text-left p-3 bg-blue-50 border border-blue-200 rounded hover:bg-blue-100 transition-colors"
                >
                  <div className="flex items-center justify-between">
                    <span className="font-medium">Project Page</span>
                    <span className="text-blue-600">→ Test Access</span>
                  </div>
                  <p className="text-sm text-blue-700 mt-1">
                    Should work (resource ownership)
                  </p>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Back to Dashboard */}
        <div className="mt-8 text-center">
          <button
            onClick={() => (window.location.href = "/dashboard")}
            className="inline-flex items-center px-6 py-3 border border-transparent text-base font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            ← Back to Main Dashboard
          </button>
        </div>
      </div>
    </div>
  );
}
