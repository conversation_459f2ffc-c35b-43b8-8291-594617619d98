# Frontend Environment Configuration
# Note: Variables prefixed with NEXT_PUBLIC_ are exposed to the browser

# Environment Configuration
NODE_ENV=development
NEXT_PUBLIC_ENVIRONMENT=development

# Authentication Configuration
NEXT_PUBLIC_AUTH_PROVIDER=supertokens

# API Configuration
NEXT_PUBLIC_API_URL=http://localhost:4000
NEXT_PUBLIC_BFF_URL=http://localhost:4000
NEXT_PUBLIC_GRAPHQL_URL=http://localhost:4000/graphql

# Monitoring Configuration (production only)
NEXT_PUBLIC_MONITORING_URL=