{"name": "@pulsepanel/events", "version": "0.1.0", "private": true, "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc -p tsconfig.json", "lint": "eslint src", "test": "vitest run", "test:watch": "vitest", "clean": "rm -rf dist", "prepublishOnly": "pnpm run build"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"amqplib": "^0.10.8"}, "devDependencies": {"@pulsepanel/eslint-config-custom": "workspace:*", "@pulsepanel/tsconfig-custom": "workspace:*", "@types/amqplib": "^0.10.5", "@types/node": "^20.11.20", "typescript": "^5.4.5", "vitest": "^2.1.8"}, "type": "module"}