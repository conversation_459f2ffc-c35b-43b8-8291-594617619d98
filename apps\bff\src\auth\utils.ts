import type { Request } from "express";

/**
 * Extracts session ID from cookies or Authorization header
 */
export function extractSessionId(req: Request): string | null {
  try {
    // 1. Try to get from cookie (Web clients)
    const cookieHeader = req.headers.cookie;
    if (cookieHeader) {
      const cookies = parseCookies(cookieHeader);
      // SuperTokens standard cookie names
      const accessToken = cookies["sAccessToken"] || cookies["st-access-token"];
      if (accessToken) {
        return `cookie:${accessToken}`;
      }
    }

    // 2. Try to get from Authorization header (Mobile clients)
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const token = authHeader.substring(7);
      return `bearer:${token}`;
    }

    return null;
  } catch (error) {
    console.error("Error extracting session ID:", error);
    return null;
  }
}

/**
 * Simple cookies parser
 */
function parseCookies(cookieHeader: string): Record<string, string> {
  const cookies: Record<string, string> = {};

  cookieHeader.split(";").forEach((cookie) => {
    const parts = cookie.trim().split("=");
    if (parts.length === 2) {
      const [name, value] = parts;
      cookies[name] = decodeURIComponent(value);
    }
  });

  return cookies;
}

/**
 * Creates a unique cache key for session
 */
export function createSessionCacheKey(sessionId: string): string {
  // Hash for security and key length limitation
  const hash = Buffer.from(sessionId).toString("base64").substring(0, 32);
  return `session:${hash}`;
}

/**
 * Checks if the request is public (does not require auth)
 */
export function isPublicEndpoint(req: Request): boolean {
  const path = req.path;
  const method = req.method;

  // List of public endpoints
  const publicPaths = [
    "/", // Health check
    "/health",
    "/metrics",
  ];

  // Allow GraphQL introspection for development
  if (path === "/graphql" && method === "POST") {
    const body = req.body;
    if (body && body.query && body.query.includes("__schema")) {
      return true;
    }
  }

  return publicPaths.includes(path);
}
