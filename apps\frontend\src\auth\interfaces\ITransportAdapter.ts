/**
 * Transport Adapter Interface
 *
 * This interface abstracts the communication layer from the auth service.
 * Different implementations can handle:
 * - GraphQL with Apollo Client (current)
 * - REST API with fetch
 * - gRPC
 * - Any other transport protocol
 *
 * The auth service doesn't need to know about transport details.
 */

import type {
  SignInInput,
  SignUpInput,
  AuthResult,
  User,
  SessionInfo,
} from "@/auth";

/**
 * HTTP Request Configuration
 */
export interface RequestConfig {
  url?: string;
  method?: "GET" | "POST" | "PUT" | "DELETE";
  headers?: Record<string, string>;
  body?: any;
  query?: Record<string, any>;
  timeout?: number;
  retries?: number;
}

/**
 * Transport Response
 */
export interface TransportResponse<T = any> {
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  status?: number;
  headers?: Record<string, string>;
}

/**
 * GraphQL Operation Types
 */
export interface GraphQLOperation {
  operationName?: string;
  query: string;
  variables?: Record<string, any>;
}

/**
 * Transport Health Information
 */
export interface TransportHealth {
  isHealthy: boolean;
  latency?: number;
  lastCheck: string;
  error?: string;
}

/**
 * Core Transport Adapter Interface
 *
 * Implementations handle the actual communication with the backend.
 * The auth service uses this interface without knowing about transport details.
 */
export interface ITransportAdapter {
  signIn(input: SignInInput): Promise<TransportResponse<AuthResult>>;

  signUp(input: SignUpInput): Promise<TransportResponse<AuthResult>>;

  signOut(): Promise<TransportResponse<boolean>>;

  refreshToken(): Promise<TransportResponse<AuthResult>>;

  getCurrentUser(): Promise<TransportResponse<User>>;

  getSessionInfo(): Promise<TransportResponse<SessionInfo>>;

  // getUserTenants method removed - tenants now handled by SuperTokens only

  checkAuthStatus(): Promise<TransportResponse<boolean>>;

  hasValidSessionCookies(): Promise<boolean>;

  healthCheck(): Promise<TransportHealth>;

  configure(config: RequestConfig): void;

  cleanup(): Promise<void>;
}

/**
 * GraphQL-specific Transport Adapter Interface
 * Extends base transport with GraphQL-specific methods
 */
export interface IGraphQLTransportAdapter extends ITransportAdapter {
  /**
   * Execute GraphQL query
   */
  query<T = any>(operation: GraphQLOperation): Promise<TransportResponse<T>>;

  /**
   * Execute GraphQL mutation
   */
  mutate<T = any>(operation: GraphQLOperation): Promise<TransportResponse<T>>;

  /**
   * Subscribe to GraphQL subscription
   */
  subscribe<T = any>(
    operation: GraphQLOperation,
  ): Promise<AsyncIterableIterator<TransportResponse<T>>>;

  /**
   * Clear Apollo cache (if using Apollo Client)
   */
  clearCache(): Promise<void>;
}

/**
 * REST-specific Transport Adapter Interface
 * Extends base transport with REST-specific methods
 */
export interface IRestTransportAdapter extends ITransportAdapter {
  get<T = any>(
    url: string,
    config?: RequestConfig,
  ): Promise<TransportResponse<T>>;

  post<T = any>(
    url: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<TransportResponse<T>>;

  put<T = any>(
    url: string,
    data?: any,
    config?: RequestConfig,
  ): Promise<TransportResponse<T>>;

  delete<T = any>(
    url: string,
    config?: RequestConfig,
  ): Promise<TransportResponse<T>>;
}

/**
 * Transport Adapter Factory
 * Creates appropriate transport adapter based on configuration
 */
export interface ITransportAdapterFactory {
  createGraphQLAdapter(
    endpoint: string,
    config?: any,
  ): IGraphQLTransportAdapter;
  createRestAdapter(baseUrl: string, config?: any): IRestTransportAdapter;
}

/**
 * Cookie Management Interface
 * For handling HttpOnly cookies in the transport layer
 */
export interface ICookieManager {
  /**
   * Set cookie (handled by browser for HttpOnly cookies)
   */
  setCookie(
    name: string,
    value: string,
    options?: {
      domain?: string;
      path?: string;
      secure?: boolean;
      sameSite?: "strict" | "lax" | "none";
      maxAge?: number;
    },
  ): void;

  getCookie(name: string): string | undefined;

  removeCookie(name: string): void;

  areCookiesEnabled(): boolean;
}
