"use client";

import React from "react";
import { useAuth, useAuthActions } from "@/hooks/features";
import { useAuthServiceState } from "@/auth/providers/AuthServiceProvider";

export default function AuthTestReduxPage() {
  // New Redux-based auth
  const authRedux = useAuth();
  const authActions = useAuthActions();

  // Context service state (service layer only)
  const authService = useAuthServiceState();

  return (
    <div className="min-h-screen bg-gray-100 py-12 px-4">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Auth State Migration Test
        </h1>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Redux Auth State */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 text-green-600">
              Redux Auth State (New)
            </h2>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">Initialized:</span>
                <span
                  className={
                    authRedux.isInitialized ? "text-green-600" : "text-red-600"
                  }
                >
                  {authRedux.isInitialized ? "✅" : "❌"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Authenticated:</span>
                <span
                  className={
                    authRedux.isAuthenticated
                      ? "text-green-600"
                      : "text-red-600"
                  }
                >
                  {authRedux.isAuthenticated ? "✅" : "❌"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Checking Auth:</span>
                <span
                  className={
                    authRedux.isCheckingAuth
                      ? "text-yellow-600"
                      : "text-gray-600"
                  }
                >
                  {authRedux.isCheckingAuth ? "🔄" : "✅"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">User:</span>
                <span className="text-gray-600">
                  {authRedux.user
                    ? authRedux.user.name || authRedux.user.id
                    : "null"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Auth Error:</span>
                <span className="text-red-600 text-sm">
                  {authRedux.authError || "none"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Init Error:</span>
                <span className="text-red-600 text-sm">
                  {authRedux.initError || "none"}
                </span>
              </div>
            </div>
          </div>

          {/* Service Layer State */}
          <div className="bg-white rounded-lg shadow p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 text-blue-600">
              Auth Service Layer (Context)
            </h2>

            <div className="space-y-3">
              <div className="flex justify-between">
                <span className="font-medium">Service Initialized:</span>
                <span
                  className={
                    authService.isInitialized
                      ? "text-green-600"
                      : "text-red-600"
                  }
                >
                  {authService.isInitialized ? "✅" : "❌"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Service Type:</span>
                <span className="text-gray-600">
                  {authService.authService?.constructor.name || "null"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Service Loading:</span>
                <span
                  className={
                    authService.isLoading ? "text-yellow-600" : "text-gray-600"
                  }
                >
                  {authService.isLoading ? "🔄" : "✅"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Service Error:</span>
                <span className="text-red-600 text-sm">
                  {authService.error || "none"}
                </span>
              </div>

              <div className="flex justify-between">
                <span className="font-medium">Config API URL:</span>
                <span className="text-gray-600 text-sm">
                  {authService.config?.apiUrl || "none"}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Test Actions */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Test Actions (Redux)
          </h2>

          <div className="space-x-4">
            <button
              onClick={() => authActions.checkAuthStatus()}
              disabled={authRedux.isCheckingAuth}
              className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:opacity-50"
            >
              {authRedux.isCheckingAuth ? "Checking..." : "Check Auth Status"}
            </button>

            <button
              onClick={() => authActions.signOut()}
              disabled={!authRedux.isAuthenticated}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 disabled:opacity-50"
            >
              Sign Out
            </button>
          </div>
        </div>

        {/* State Sync Verification */}
        <div className="mt-8 bg-white rounded-lg shadow p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            Layer Separation Check
          </h2>

          <div className="space-y-2">
            <div className="flex justify-between">
              <span>Redux has auth state:</span>
              <span
                className={
                  authRedux.isAuthenticated !== undefined
                    ? "text-green-600"
                    : "text-red-600"
                }
              >
                {authRedux.isAuthenticated !== undefined ? "✅ Yes" : "❌ No"}
              </span>
            </div>

            <div className="flex justify-between">
              <span>Service layer initialized:</span>
              <span
                className={
                  authService.isInitialized ? "text-green-600" : "text-red-600"
                }
              >
                {authService.isInitialized ? "✅ Yes" : "❌ No"}
              </span>
            </div>

            <div className="flex justify-between">
              <span>Clean separation:</span>
              <span className="text-green-600">
                ✅ Service (Context) + State (Redux)
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
