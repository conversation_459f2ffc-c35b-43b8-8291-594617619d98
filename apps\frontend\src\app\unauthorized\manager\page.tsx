"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState, Suspense } from "react";

function ManagerUnauthorizedContent() {
  const searchParams = useSearchParams();
  const [errorDetails, setErrorDetails] = useState<{
    required: string;
    userRoles: string;
    reason: string;
  }>({
    required: "",
    userRoles: "",
    reason: "",
  });

  useEffect(() => {
    setErrorDetails({
      required: searchParams.get("required") || "manager",
      userRoles: searchParams.get("userRoles") || "none",
      reason: searchParams.get("reason") || "insufficient_role",
    });
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-orange-50 to-orange-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-lg">
        {/* Manager Badge Icon */}
        <div className="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-orange-100 border-4 border-orange-200">
          <svg
            className="h-16 w-16 text-orange-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>

        <h1 className="mt-6 text-center text-4xl font-bold text-gray-900">
          Manager Access Required
        </h1>

        <p className="mt-4 text-center text-lg text-gray-700 max-w-md mx-auto">
          This management area requires manager-level permissions or higher.
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
        <div className="bg-white py-8 px-6 shadow-lg sm:rounded-xl sm:px-10 border border-orange-200">
          <div className="space-y-6">
            {/* Manager Access Alert */}
            <div className="bg-orange-50 border-l-4 border-orange-400 p-6 rounded-r-lg">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-6 w-6 text-orange-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-semibold text-orange-800">
                    Management Privileges Required
                  </h3>
                  <div className="mt-3 text-orange-700">
                    <p className="text-base">
                      You need <strong>manager</strong> or{" "}
                      <strong>administrator</strong> privileges to access this
                      section.
                    </p>
                    <div className="mt-4 bg-white p-4 rounded border border-orange-200">
                      <dl className="space-y-2">
                        <div>
                          <dt className="text-sm font-medium text-orange-800">
                            Required Role:
                          </dt>
                          <dd className="text-sm text-orange-600 font-mono bg-orange-50 px-2 py-1 rounded">
                            {errorDetails.required} (or higher)
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-orange-800">
                            Your Current Roles:
                          </dt>
                          <dd className="text-sm text-orange-600 font-mono bg-orange-50 px-2 py-1 rounded">
                            {errorDetails.userRoles || "none"}
                          </dd>
                        </div>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Role Hierarchy Explanation */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-blue-900 mb-4">
                Role Hierarchy & Access Levels
              </h4>
              <div className="space-y-3">
                <div className="flex items-center justify-between p-3 bg-white rounded border border-blue-200">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                    <span className="font-medium text-gray-900">
                      Administrator
                    </span>
                  </div>
                  <span className="text-sm text-gray-600">
                    Full system access
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-white rounded border border-blue-200">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-orange-500 rounded-full mr-3"></div>
                    <span className="font-medium text-gray-900">Manager</span>
                  </div>
                  <span className="text-sm text-gray-600">
                    Team & tenant management
                  </span>
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-100 rounded border border-gray-200">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-gray-400 rounded-full mr-3"></div>
                    <span className="font-medium text-gray-500">User</span>
                  </div>
                  <span className="text-sm text-gray-500">
                    Basic access only
                  </span>
                </div>
              </div>
            </div>

            {/* What Managers Can Do */}
            <div className="bg-green-50 border border-green-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-green-900 mb-4">
                Manager Capabilities
              </h4>
              <ul className="space-y-2 text-green-800">
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-green-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Manage users within their tenant
                </li>
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-green-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Access team analytics and reports
                </li>
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-green-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Configure tenant-specific settings
                </li>
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-green-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Moderate content and user activity
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                onClick={() => window.history.back()}
                className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg text-base font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
              >
                ← Go Back
              </button>
              <button
                onClick={() => (window.location.href = "/dashboard")}
                className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg text-base font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                📊 Dashboard
              </button>
            </div>

            {/* Request Access Section */}
            <div className="border-t border-gray-200 pt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                Request Manager Access
              </h4>
              <div className="text-gray-600 space-y-3">
                <p>To get manager permissions:</p>
                <ol className="list-decimal list-inside ml-4 space-y-2 text-sm">
                  <li>Speak with your current manager or administrator</li>
                  <li>Demonstrate team leadership skills</li>
                  <li>Complete management training (if required)</li>
                  <li>Get approval for manager role assignment</li>
                </ol>

                <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex">
                    <svg
                      className="h-5 w-5 text-blue-400 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <p className="text-sm text-blue-800">
                      <strong>Good to know:</strong> Manager access provides
                      significant control over team resources and user data. It
                      requires trust and responsibility.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Debug Info (Development Mode) */}
            {process.env.NODE_ENV === "development" && (
              <div className="border-t border-gray-200 pt-4">
                <details className="group">
                  <summary className="text-sm font-medium text-gray-500 cursor-pointer hover:text-gray-700">
                    🔧 Debug Information
                  </summary>
                  <div className="mt-2 text-xs font-mono bg-gray-100 p-3 rounded border">
                    <pre className="whitespace-pre-wrap">
                      {JSON.stringify(errorDetails, null, 2)}
                    </pre>
                  </div>
                </details>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function ManagerUnauthorizedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-red-50">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <div className="text-6xl mb-4">📊</div>
          <h2 className="text-3xl font-extrabold text-red-900">
            Manager Access Required
          </h2>
          <p className="mt-2 text-red-700">
            You need manager privileges to access this page
          </p>
        </div>
        <div className="mt-8 space-y-6">
          <p className="text-red-600">
            📈 Manager unauthorized page placeholder
          </p>
        </div>
      </div>
    </div>
  );
}
