import { GraphQLError } from "graphql";
import { PrismaClient } from "@pulsepanel/db/generated/client";
import type { Context, UserContext } from "../types/context.js";
import type { SessionData } from "./types.js";
import Redis from "ioredis";
import UserRoles from "supertokens-node/recipe/userroles/index.js";
import { getPermissionsForRoles } from "./initializeRoles.js";

// Security error types
export class AuthenticationError extends GraphQLError {
  constructor(message = "Authentication required") {
    super(message, {
      extensions: {
        code: "UNAUTHENTICATED",
        http: { status: 401 },
      },
    });
  }
}

export class AuthorizationError extends GraphQLError {
  constructor(message = "Insufficient permissions") {
    super(message, {
      extensions: {
        code: "FORBIDDEN",
        http: { status: 403 },
      },
    });
  }
}

export class RateLimitError extends GraphQLError {
  constructor(message = "Rate limit exceeded") {
    super(message, {
      extensions: {
        code: "RATE_LIMITED",
        http: { status: 429 },
      },
    });
  }
}

export class TenantAccessError extends GraphQLError {
  constructor(message = "Access denied to tenant resource") {
    super(message, {
      extensions: {
        code: "TENANT_ACCESS_DENIED",
        http: { status: 403 },
      },
    });
  }
}

// =============================================================================
// AUTHENTICATION HELPERS
// =============================================================================

/**
 * Strict authentication requirement with comprehensive audit
 */
export function requireAuth(context: Context): asserts context is Context & {
  user: UserContext;
  session: SessionData;
} {
  const startTime = Date.now();

  try {
    if (!context.isAuthenticated || !context.user || !context.session) {
      // Audit failed authentication attempt
      context.logSecurityEvent("authentication_required_failed", {
        ip: context.security.ipAddress,
        userAgent: context.security.userAgent,
        operation: context.audit.operationName,
        timestamp: new Date().toISOString(),
      });

      throw new AuthenticationError(
        "Authentication required to access this resource",
      );
    }

    // Validate session is still active
    validateSession(context);

    // Audit successful authentication
    context.logSecurityEvent("authentication_success", {
      userId: context.user.id,
      tenantId: context.user.tenantId,
      operation: context.audit.operationName,
      latency: Date.now() - startTime,
    });
  } catch (error) {
    // Ensure we always audit authentication attempts
    if (!(error instanceof AuthenticationError)) {
      context.logSecurityEvent("authentication_error", {
        error: error instanceof Error ? error.message : String(error),
        operation: context.audit.operationName,
      });
    }
    throw error;
  }
}

/**
 * Optional authentication for public endpoints with partial data
 */
export function optionalAuth(context: Context): UserContext | null {
  if (!context.isAuthenticated || !context.user) {
    return null;
  }

  try {
    validateSession(context);
    return context.user;
  } catch {
    // Silent fail for optional auth
    return null;
  }
}

/**
 * Deep session validation with security checks
 */
export function validateSession(context: Context): void {
  if (!context.session) {
    throw new AuthenticationError("Invalid session");
  }

  // Additional session security validations could be added here
  // e.g., session expiry, device fingerprinting, etc.
}

// =============================================================================
// AUTHORIZATION HELPERS
// =============================================================================

/**
 * Role hierarchy mapping for permission inheritance
 */
const ROLE_HIERARCHY: Record<string, string[]> = {
  admin: ["admin", "manager", "user"],
  manager: ["manager", "user"],
  user: ["user"],
};

/**
 * Check if user has hierarchical role access
 */
function hasHierarchicalRole(
  userRoles: string[],
  requiredRole: string,
): boolean {
  // Check if any user role includes the required role in its hierarchy
  return userRoles.some((userRole) => {
    const hierarchy = ROLE_HIERARCHY[userRole] || [userRole];
    return hierarchy.includes(requiredRole);
  });
}

/**
 * Role validation with inheritance support
 */
export function requireRole(context: Context, requiredRole: string): void {
  requireAuth(context);

  const userRoles = context.user.roles || [];

  // Use hierarchical role checking instead of exact match
  if (!hasHierarchicalRole(userRoles, requiredRole)) {
    context.logSecurityEvent("authorization_role_denied", {
      userId: context.user.id,
      requiredRole,
      userRoles,
      operation: context.audit.operationName,
      hierarchyUsed: true,
    });

    throw new AuthorizationError(`Role '${requiredRole}' is required`);
  }

  context.logSecurityEvent("authorization_role_success", {
    userId: context.user.id,
    role: requiredRole,
    userRoles,
    operation: context.audit.operationName,
    hierarchyUsed: true,
  });
}

/**
 * Granular permission checking
 */
export function requirePermission(context: Context, permission: string): void {
  requireAuth(context);

  const userPermissions = context.user.permissions || [];

  if (!userPermissions.includes(permission)) {
    context.logSecurityEvent("authorization_permission_denied", {
      userId: context.user.id,
      requiredPermission: permission,
      userPermissions,
      operation: context.audit.operationName,
    });

    throw new AuthorizationError(`Permission '${permission}' is required`);
  }
}

/**
 * Tenant boundary enforcement
 */
export function requireTenantAccess(context: Context, tenantId: string): void {
  requireAuth(context);

  if (context.user.tenantId !== tenantId) {
    context.logSecurityEvent("tenant_access_violation", {
      userId: context.user.id,
      userTenantId: context.user.tenantId,
      requestedTenantId: tenantId,
      operation: context.audit.operationName,
      severity: "HIGH",
    });

    throw new TenantAccessError(`Access denied to tenant ${tenantId}`);
  }
}

// =============================================================================
// SECURITY HELPERS
// =============================================================================

/**
 * Input validation and sanitization
 */
export function validateInput(input: any, schema: any): any {
  // Implementation would depend on chosen validation library (e.g., Joi, Zod)
  // For now, basic validation
  if (input == null) {
    throw new GraphQLError("Input validation failed: null input");
  }

  return input;
}

/**
 * Comprehensive operation audit
 */
export function auditOperation(
  context: Context,
  operation: string,
  result?: any,
  metadata?: Record<string, any>,
): void {
  const auditData = {
    operation,
    userId: context.user?.id,
    tenantId: context.user?.tenantId,
    ip: context.security.ipAddress,
    userAgent: context.security.userAgent,
    timestamp: new Date().toISOString(),
    requestId: context.security.requestId,
    latency: Date.now() - context.audit.requestStartTime,
    success: !result || !result.errors,
    metadata,
  };

  context.logSecurityEvent("operation_audit", auditData);
}

/**
 * Operation-level rate limiting
 */
export async function checkRateLimit(
  context: Context,
  operation: string,
  maxRequests = 100,
  windowMs = 60000,
): Promise<void> {
  if (!context.user) return; // Skip rate limiting for unauthenticated users

  const key = `rate_limit:${context.user.id}:${operation}`;
  const current = await context.redis.incr(key);

  if (current === 1) {
    await context.redis.expire(key, Math.ceil(windowMs / 1000));
  }

  if (current > maxRequests) {
    context.logSecurityEvent("rate_limit_violation", {
      userId: context.user.id,
      operation,
      current,
      limit: maxRequests,
      window: windowMs,
    });

    throw new RateLimitError(`Rate limit exceeded for operation: ${operation}`);
  }

  // Update rate limit info in context
  context.security.rateLimit = {
    remaining: Math.max(0, maxRequests - current),
    resetTime: new Date(Date.now() + windowMs),
  };
}

// =============================================================================
// USER CONTEXT HELPERS
// =============================================================================

/**
 * Safe user data retrieval
 */
export function getCurrentUser(context: Context): UserContext {
  requireAuth(context);
  return context.user;
}

/**
 * Tenant context with validation
 */
export function getCurrentTenant(context: Context) {
  requireAuth(context);

  if (!context.currentTenant) {
    throw new TenantAccessError("Tenant context not available");
  }

  return context.currentTenant;
}

/**
 * Computed permissions cache
 */
export async function getUserPermissions(context: Context): Promise<string[]> {
  requireAuth(context);

  // Check cache first
  const cacheKey = `permissions:${context.user.id}:${context.user.tenantId}`;
  const cached = await context.redis.get(cacheKey);

  if (cached) {
    return JSON.parse(cached);
  }

  // Compute permissions based on roles
  const permissions = await computeUserPermissions(
    context.user.roles,
    context.user.tenantId,
    context.prisma,
    context.redis,
  );

  // Cache for 5 minutes
  await context.redis.setex(cacheKey, 300, JSON.stringify(permissions));

  return permissions;
}

/**
 * Compute user permissions based on roles using SuperTokens API + Smart Caching
 */
async function computeUserPermissions(
  roles: string[],
  tenantId: string,
  prisma: PrismaClient,
  redis?: Redis,
): Promise<string[]> {
  try {
    // 1. Try to get permissions from role-based cache first
    if (redis) {
      const cachedPermissions = await getRolePermissionsFromCache(redis, roles);
      if (cachedPermissions.length > 0) {
        return cachedPermissions;
      }
    }

    // 2. Get permissions from SuperTokens for each role
    const allPermissions = new Set<string>();

    for (const role of roles) {
      try {
        // Get permissions for this role from SuperTokens
        const response = await UserRoles.getPermissionsForRole(role);

        if (response.status === "OK") {
          response.permissions.forEach((permission) =>
            allPermissions.add(permission),
          );
        } else {
          console.warn(
            `⚠️ Role '${role}' not found in SuperTokens, falling back to local mapping`,
          );
          // Fallback to local mapping if role not found in SuperTokens
          const localPermissions = getPermissionsForRoles([role]);
          localPermissions.forEach((permission) =>
            allPermissions.add(permission),
          );
        }
      } catch (error) {
        console.error(
          `❌ Error fetching permissions for role '${role}':`,
          error,
        );
        // Fallback to local mapping on error
        const localPermissions = getPermissionsForRoles([role]);
        localPermissions.forEach((permission) =>
          allPermissions.add(permission),
        );
      }
    }

    const permissions = Array.from(allPermissions);

    // 3. Cache the results for future requests
    if (redis && permissions.length > 0) {
      await cacheRolePermissions(redis, roles, permissions);
    }

    return permissions;
  } catch (error) {
    console.error("❌ Error in computeUserPermissions:", error);

    // Ultimate fallback to local role-permission mapping
    console.log("🔄 Falling back to local role-permission mapping");
    return getPermissionsForRoles(roles);
  }
}

// =============================================================================
// ROLE PERMISSIONS CACHE MANAGEMENT
// =============================================================================

/**
 * Get role permissions from Redis cache
 * @param redis - Redis instance
 * @param roles - User roles
 * @returns Cached permissions or empty array if cache miss
 */
async function getRolePermissionsFromCache(
  redis: Redis,
  roles: string[],
): Promise<string[]> {
  try {
    const allPermissions = new Set<string>();
    let cacheHits = 0;

    // Check cache for each role
    for (const role of roles) {
      const cacheKey = `permissions:role:${role}`;
      const cached = await redis.get(cacheKey);

      if (cached) {
        const rolePermissions = JSON.parse(cached);
        rolePermissions.forEach((permission: string) =>
          allPermissions.add(permission),
        );
        cacheHits++;
      }
    }

    // Only return cached result if we have ALL roles cached
    if (cacheHits === roles.length && roles.length > 0) {
      return Array.from(allPermissions);
    }

    return []; // Cache miss - need to fetch from SuperTokens
  } catch (error) {
    console.error("❌ Error getting role permissions from cache:", error);
    return []; // Cache error - fetch from SuperTokens
  }
}

/**
 * Cache role permissions in Redis
 * @param redis - Redis instance
 * @param roles - User roles
 * @param permissions - Computed permissions
 */
async function cacheRolePermissions(
  redis: Redis,
  roles: string[],
  permissions: string[],
): Promise<void> {
  try {
    // Cache individual role permissions for future lookups
    for (const role of roles) {
      // Get permissions for this specific role
      const roleSpecificPermissions = getPermissionsForRoles([role]);

      if (roleSpecificPermissions.length > 0) {
        const cacheKey = `permissions:role:${role}`;
        // Cache without TTL for role permissions (they rarely change)
        await redis.set(cacheKey, JSON.stringify(roleSpecificPermissions));
      }
    }
  } catch (error) {
    console.error("❌ Error caching role permissions:", error);
    // Don't throw - caching is optional optimization
  }
}
