{"name": "@pulsepanel/frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "eslint .", "codegen": "graphql-codegen", "codegen:watch": "graphql-codegen --watch"}, "dependencies": {"@apollo/client": "^3.8.10", "@reduxjs/toolkit": "^2.8.2", "graphql": "^16.8.1", "next": "15.3.1", "react": "^19.0.0", "react-dom": "^19.0.0", "react-redux": "^9.2.0"}, "devDependencies": {"@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-operations": "^4.6.1", "@graphql-codegen/typescript-react-apollo": "^4.3.3", "@next/eslint-plugin-next": "^15.0.0", "@pulsepanel/eslint-config-custom": "workspace:*", "@types/node": "^20.11.20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-redux": "^7.1.34", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "eslint-config-next": "15.3.1", "eslint-plugin-react-hooks": "^5.2.0", "typescript": "^5.4.5"}}