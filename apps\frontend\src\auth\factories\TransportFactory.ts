/**
 * Transport Factory
 *
 * Abstracts transport adapter creation from auth service layer.
 * AuthServiceProvider doesn't know about implementation details (Apollo, REST, etc.)
 */

import type { ApolloClient } from "@apollo/client";
import type { IGraphQLTransportAdapter, AuthConfig } from "@/auth";
import { GraphQLTransportAdapter } from "@/auth";

export interface ITransportFactory {
  createGraphQLTransport(config: AuthConfig): IGraphQLTransportAdapter;
}

/**
 * Apollo-based Transport Factory
 * Uses Apollo Client to create GraphQL transport
 */
export class ApolloTransportFactory implements ITransportFactory {
  constructor(private apolloClient: ApolloClient<any>) {}

  createGraphQLTransport(config: AuthConfig): IGraphQLTransportAdapter {
    return new GraphQLTransportAdapter(this.apolloClient, {
      debugMode: config.debugMode,
    });
  }
}

/**
 * Mock Transport Factory for testing
 */
export class MockTransportFactory implements ITransportFactory {
  createGraphQLTransport(config: AuthConfig): IGraphQLTransportAdapter {
    // Return mock implementation for testing
    throw new Error("Mock transport not implemented");
  }
}

/**
 * REST Transport Factory (future)
 */
export class RestTransportFactory implements ITransportFactory {
  createGraphQLTransport(config: AuthConfig): IGraphQLTransportAdapter {
    // Future: REST-based implementation
    throw new Error("REST transport not implemented yet");
  }
}
