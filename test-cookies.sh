#!/bin/bash

# Cookie Debug Test Script
# Тестирует установку и чтение HttpOnly куки SuperTokens

echo "🧪 Testing SuperTokens HttpOnly Cookies"
echo "======================================="

# Проверяем, что сервер запущен
echo "📡 Checking if BFF server is running..."
if ! curl -s http://localhost:4000/health > /dev/null 2>&1; then
    echo "❌ BFF server is not running on port 4000"
    echo "   Please start the BFF server first: npm run dev:bff"
    exit 1
fi

echo "✅ BFF server is running"
echo ""

# Тест 1: Создание сессии
echo "🔐 Test 1: Creating session with SuperTokens..."
echo "POST http://localhost:4000/bff-auth/signin"

SIGNIN_RESPONSE=$(curl -s -c cookies.txt -b cookies.txt \
  -X POST http://localhost:4000/bff-auth/signin \
  -H "Content-Type: application/json" \
  -d '{
    "formFields": [
      {"id": "email", "value": "<EMAIL>"},
      {"id": "password", "value": "testpassword123"}
    ]
  }')

echo "Response: $SIGNIN_RESPONSE"
echo ""

# Проверяем установленные куки
echo "🍪 Checking cookies set by SuperTokens..."
if [ -f cookies.txt ]; then
    echo "Cookies file content:"
    cat cookies.txt
    echo ""
    
    # Анализируем куки
    if grep -q "sAccessToken" cookies.txt; then
        echo "✅ sAccessToken cookie found"
    else
        echo "❌ sAccessToken cookie NOT found"
    fi
    
    if grep -q "sRefreshToken" cookies.txt; then
        echo "✅ sRefreshToken cookie found"
    else
        echo "❌ sRefreshToken cookie NOT found"
    fi
    
    if grep -q "sFrontToken" cookies.txt; then
        echo "✅ sFrontToken cookie found"
    else
        echo "❌ sFrontToken cookie NOT found"
    fi
else
    echo "❌ No cookies file created"
fi

echo ""

# Тест 2: Проверка сессии
echo "🔍 Test 2: Verifying session..."
echo "GET http://localhost:4000/graphql (with cookies)"

GRAPHQL_RESPONSE=$(curl -s -b cookies.txt \
  -X POST http://localhost:4000/graphql \
  -H "Content-Type: application/json" \
  -d '{
    "query": "query { checkAuthStatus }"
  }')

echo "GraphQL Response: $GRAPHQL_RESPONSE"
echo ""

# Тест 3: Проверка middleware
echo "🛡️ Test 3: Testing frontend middleware..."
echo "GET http://localhost:3000/dashboard (with cookies)"

MIDDLEWARE_RESPONSE=$(curl -s -b cookies.txt \
  -w "HTTP_CODE:%{http_code}" \
  http://localhost:3000/dashboard)

echo "Middleware Response: $MIDDLEWARE_RESPONSE"
echo ""

# Очистка
echo "🧹 Cleaning up..."
rm -f cookies.txt

echo "✅ Cookie test completed!"
echo ""
echo "📋 Summary:"
echo "   - If you see sAccessToken cookie: ✅ HttpOnly cookies are working"
echo "   - If you see only sFrontToken: ❌ Using header mode instead of cookies"
echo "   - If no cookies: ❌ Authentication failed or cookies not set"
echo ""
echo "🔧 Troubleshooting:"
echo "   1. Make sure SuperTokens core is running (port 3567)"
echo "   2. Check BFF logs for cookie debug information"
echo "   3. Verify environment variables are set correctly"
