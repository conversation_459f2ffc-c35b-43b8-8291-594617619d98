schema: '../bff/src/schema/schema.graphql'
documents: './src/**/*.graphql'
generates:
  ./src/generated/graphql-types.ts:
    plugins:
      - typescript
      - typescript-operations
      - typescript-react-apollo
    config:
      withHooks: true
      withHOC: false
      withComponent: false
      apolloReactHooksImportFrom: '@apollo/client'
      scalars:
        ID: string
        String: string
        Boolean: boolean
        Int: number
        Float: number 