"use client";

import { useSearchParams } from "next/navigation";
import { useEffect, useState, Suspense } from "react";

function AdminUnauthorizedContent() {
  const searchParams = useSearchParams();
  const [errorDetails, setErrorDetails] = useState<{
    required: string;
    userRoles: string;
    reason: string;
  }>({
    required: "",
    userRoles: "",
    reason: "",
  });

  useEffect(() => {
    setErrorDetails({
      required: searchParams.get("required") || "admin",
      userRoles: searchParams.get("userRoles") || "none",
      reason: searchParams.get("reason") || "insufficient_role",
    });
  }, [searchParams]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-red-50 to-red-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-lg">
        {/* Admin Shield Icon */}
        <div className="mx-auto flex items-center justify-center h-32 w-32 rounded-full bg-red-100 border-4 border-red-200">
          <svg
            className="h-16 w-16 text-red-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"
            />
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M6 18L18 6M6 6l12 12"
            />
          </svg>
        </div>

        <h1 className="mt-6 text-center text-4xl font-bold text-gray-900">
          Administrator Access Required
        </h1>

        <p className="mt-4 text-center text-lg text-gray-700 max-w-md mx-auto">
          This area is restricted to system administrators only.
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-lg">
        <div className="bg-white py-8 px-6 shadow-lg sm:rounded-xl sm:px-10 border border-red-200">
          <div className="space-y-6">
            {/* Admin Access Alert */}
            <div className="bg-red-50 border-l-4 border-red-400 p-6 rounded-r-lg">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg
                    className="h-6 w-6 text-red-400"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      fillRule="evenodd"
                      d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                      clipRule="evenodd"
                    />
                  </svg>
                </div>
                <div className="ml-3">
                  <h3 className="text-lg font-semibold text-red-800">
                    Administrative Privileges Required
                  </h3>
                  <div className="mt-3 text-red-700">
                    <p className="text-base">
                      You need <strong>administrator</strong> privileges to
                      access this section.
                    </p>
                    <div className="mt-4 bg-white p-4 rounded border border-red-200">
                      <dl className="space-y-2">
                        <div>
                          <dt className="text-sm font-medium text-red-800">
                            Required Role:
                          </dt>
                          <dd className="text-sm text-red-600 font-mono bg-red-50 px-2 py-1 rounded">
                            {errorDetails.required}
                          </dd>
                        </div>
                        <div>
                          <dt className="text-sm font-medium text-red-800">
                            Your Current Roles:
                          </dt>
                          <dd className="text-sm text-red-600 font-mono bg-red-50 px-2 py-1 rounded">
                            {errorDetails.userRoles || "none"}
                          </dd>
                        </div>
                      </dl>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* What Admins Can Do */}
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
              <h4 className="text-lg font-semibold text-blue-900 mb-4">
                Administrator Capabilities
              </h4>
              <ul className="space-y-2 text-blue-800">
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-blue-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  System configuration and settings management
                </li>
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-blue-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  User role and permission management
                </li>
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-blue-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  Tenant and organization administration
                </li>
                <li className="flex items-start">
                  <svg
                    className="h-5 w-5 text-blue-600 mt-0.5 mr-2"
                    fill="currentColor"
                    viewBox="0 0 20 20"
                  >
                    <path
                      fillRule="evenodd"
                      d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                      clipRule="evenodd"
                    />
                  </svg>
                  System monitoring and audit logs
                </li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-4">
              <button
                onClick={() => window.history.back()}
                className="flex-1 bg-gray-600 text-white py-3 px-4 rounded-lg text-base font-medium hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors"
              >
                ← Go Back
              </button>
              <button
                onClick={() => (window.location.href = "/dashboard")}
                className="flex-1 bg-blue-600 text-white py-3 px-4 rounded-lg text-base font-medium hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors"
              >
                📊 Dashboard
              </button>
            </div>

            {/* Request Access Section */}
            <div className="border-t border-gray-200 pt-6">
              <h4 className="text-lg font-semibold text-gray-900 mb-4">
                Request Administrator Access
              </h4>
              <div className="text-gray-600 space-y-3">
                <p>If you need administrator access for your role:</p>
                <ol className="list-decimal list-inside ml-4 space-y-2 text-sm">
                  <li>Contact your system administrator or IT department</li>
                  <li>Provide business justification for admin access</li>
                  <li>Complete any required security training</li>
                  <li>Wait for approval and role assignment</li>
                </ol>

                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex">
                    <svg
                      className="h-5 w-5 text-yellow-400 mr-2 mt-0.5"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                        clipRule="evenodd"
                      />
                    </svg>
                    <p className="text-sm text-yellow-800">
                      <strong>Security Note:</strong> Administrator access
                      provides extensive system control. It should only be
                      granted to trusted users with legitimate business needs.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            {/* Debug Info (Development Mode) */}
            {process.env.NODE_ENV === "development" && (
              <div className="border-t border-gray-200 pt-4">
                <details className="group">
                  <summary className="text-sm font-medium text-gray-500 cursor-pointer hover:text-gray-700">
                    🔧 Debug Information
                  </summary>
                  <div className="mt-2 text-xs font-mono bg-gray-100 p-3 rounded border">
                    <pre className="whitespace-pre-wrap">
                      {JSON.stringify(errorDetails, null, 2)}
                    </pre>
                  </div>
                </details>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default function AdminUnauthorizedPage() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-red-50">
      <div className="max-w-md w-full space-y-8 text-center">
        <div>
          <div className="text-6xl mb-4">👑</div>
          <h2 className="text-3xl font-extrabold text-red-900">
            Admin Access Required
          </h2>
          <p className="mt-2 text-red-700">
            You need admin privileges to access this page
          </p>
        </div>
        <div className="mt-8 space-y-6">
          <p className="text-red-600">🛡️ Admin unauthorized page placeholder</p>
        </div>
      </div>
    </div>
  );
}
