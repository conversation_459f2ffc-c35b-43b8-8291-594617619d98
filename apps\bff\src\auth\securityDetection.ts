import type { Redis } from "ioredis";
import type {
  SessionMetadata,
  SecurityFlag,
  SecurityDetectionResult,
} from "./types.js";

export class SecurityDetectionService {
  constructor(private redis: Redis) {}

  async analyzeSession(
    sessionId: string,
    userId: string,
    currentIP: string,
    currentUserAgent: string,
  ): Promise<SecurityDetectionResult> {
    const flags: SecurityFlag[] = [];
    let highestRisk: "low" | "medium" | "high" | "critical" = "low";

    try {
      const metadataKey = `user:${userId}:meta`;
      const metadata = await this.getSessionMetadata(metadataKey);

      if (!metadata) {
        return {
          isViolation: false,
          riskLevel: "low",
          flags: [],
          recommendedAction: "none",
        };
      }

      // Check IP address changes
      if (metadata.ipAddress && metadata.ipAddress !== currentIP) {
        const ipFlag: SecurityFlag = {
          type: "ip_change",
          timestamp: Date.now(),
          severity: "medium",
          details: {
            previousIP: metadata.ipAddress,
            currentIP: currentIP,
            timeSinceLastActivity: Date.now() - metadata.lastActivity,
          },
        };
        flags.push(ipFlag);
        highestRisk = "medium";
      }

      // Check device/user-agent changes
      if (metadata.userAgent && metadata.userAgent !== currentUserAgent) {
        const deviceFlag: SecurityFlag = {
          type: "device_change",
          timestamp: Date.now(),
          severity: "high",
          details: {
            previousUserAgent: metadata.userAgent,
            currentUserAgent: currentUserAgent,
          },
        };
        flags.push(deviceFlag);
        highestRisk = "high";
      }

      // Check for concurrent sessions
      const activeSessions = await this.getActiveSessionsCount(userId);
      if (activeSessions > 5) {
        const concurrentFlag: SecurityFlag = {
          type: "concurrent_login",
          timestamp: Date.now(),
          severity: "medium",
          details: {
            activeSessionsCount: activeSessions,
            limit: 5,
          },
        };
        flags.push(concurrentFlag);
        if (highestRisk === "low") highestRisk = "medium";
      }

      // Check for suspicious activity patterns
      const suspiciousPattern = await this.detectSuspiciousPatterns(
        userId,
        currentIP,
      );
      if (suspiciousPattern) {
        flags.push(suspiciousPattern);
        highestRisk = this.getHigherRisk(
          highestRisk,
          suspiciousPattern.severity,
        );
      }

      // Update metadata for future checks
      await this.updateSessionMetadata(metadataKey, {
        ...metadata,
        lastActivity: Date.now(),
        ipAddress: currentIP,
        userAgent: currentUserAgent,
        securityFlags: [...metadata.securityFlags.slice(-10), ...flags], // Keep last 10 flags
      });

      return {
        isViolation: flags.length > 0,
        riskLevel: highestRisk,
        flags,
        recommendedAction: this.getRecommendedAction(highestRisk, flags.length),
      };
    } catch (error) {
      console.error("Security detection error:", error);
      return {
        isViolation: false,
        riskLevel: "low",
        flags: [],
        recommendedAction: "none",
      };
    }
  }

  async initializeSessionMetadata(
    userId: string,
    sessionId: string,
    ipAddress: string,
    userAgent: string,
  ): Promise<void> {
    const metadata: SessionMetadata = {
      loginTime: Date.now(),
      lastActivity: Date.now(),
      ipAddress,
      userAgent,
      deviceType: this.detectDeviceType(userAgent),
      loginAttempts: 1,
      securityFlags: [],
    };

    const metadataKey = `user:${userId}:meta`;
    await this.redis.setex(metadataKey, 7200, JSON.stringify(metadata)); // 2 hours TTL
  }

  async recordFailedAttempt(
    userId: string,
    ipAddress: string,
  ): Promise<boolean> {
    const attemptKey = `failed_attempts:${userId}:${ipAddress}`;
    const attempts = await this.redis.incr(attemptKey);
    await this.redis.expire(attemptKey, 3600); // 1 hour window

    if (attempts >= 5) {
      // Block for 1 hour after 5 failed attempts
      const blockKey = `blocked:${userId}:${ipAddress}`;
      await this.redis.setex(blockKey, 3600, "blocked");
      return true; // User is blocked
    }

    return false; // User is not blocked
  }

  async isBlocked(userId: string, ipAddress: string): Promise<boolean> {
    const blockKey = `blocked:${userId}:${ipAddress}`;
    const blocked = await this.redis.get(blockKey);
    return blocked === "blocked";
  }

  private async getSessionMetadata(
    metadataKey: string,
  ): Promise<SessionMetadata | null> {
    try {
      const data = await this.redis.get(metadataKey);
      return data ? JSON.parse(data) : null;
    } catch {
      return null;
    }
  }

  private async updateSessionMetadata(
    metadataKey: string,
    metadata: SessionMetadata,
  ): Promise<void> {
    try {
      await this.redis.setex(metadataKey, 7200, JSON.stringify(metadata));
    } catch (error) {
      console.error("Failed to update session metadata:", error);
    }
  }

  private async getActiveSessionsCount(userId: string): Promise<number> {
    try {
      const sessions = await this.redis.smembers(`user:${userId}:sessions`);
      return sessions.length;
    } catch {
      return 0;
    }
  }

  private async detectSuspiciousPatterns(
    userId: string,
    ipAddress: string,
  ): Promise<SecurityFlag | null> {
    // Check for rapid consecutive logins
    const recentLoginsKey = `recent_logins:${userId}`;
    const now = Date.now();

    await this.redis.zadd(recentLoginsKey, now, `${ipAddress}:${now}`);
    await this.redis.expire(recentLoginsKey, 300); // 5 minutes window

    const recentLogins = await this.redis.zrangebyscore(
      recentLoginsKey,
      now - 60000,
      now,
    ); // Last minute

    if (recentLogins.length >= 3) {
      return {
        type: "suspicious_activity",
        timestamp: now,
        severity: "high",
        details: {
          pattern: "rapid_consecutive_logins",
          count: recentLogins.length,
          timeWindow: "1 minute",
        },
      };
    }

    return null;
  }

  private detectDeviceType(userAgent: string): "web" | "mobile" | "unknown" {
    if (!userAgent) return "unknown";

    const mobileKeywords = ["mobile", "android", "iphone", "ipad", "tablet"];
    const lowerUA = userAgent.toLowerCase();

    if (mobileKeywords.some((keyword) => lowerUA.includes(keyword))) {
      return "mobile";
    }

    return "web";
  }

  private getRecommendedAction(
    riskLevel: "low" | "medium" | "high" | "critical",
    flagCount: number,
  ): "none" | "warn" | "logout" | "block" {
    if (riskLevel === "critical") return "block";
    if (riskLevel === "high") return "logout";
    if (riskLevel === "medium" && flagCount >= 2) return "logout";
    if (riskLevel === "medium") return "warn";
    return "none";
  }

  private getHigherRisk(
    currentRisk: "low" | "medium" | "high" | "critical",
    newRisk: "low" | "medium" | "high" | "critical",
  ): "low" | "medium" | "high" | "critical" {
    const riskLevels = ["low", "medium", "high", "critical"] as const;
    const currentIndex = riskLevels.indexOf(currentRisk);
    const newIndex = riskLevels.indexOf(newRisk);
    return riskLevels[Math.max(currentIndex, newIndex)];
  }
}
