import supertokens from "supertokens-node";
import Session from "supertokens-node/recipe/session/index.js";
import EmailPassword from "supertokens-node/recipe/emailpassword/index.js";
import UserMetadata from "supertokens-node/recipe/usermetadata/index.js";
import UserRoles from "supertokens-node/recipe/userroles/index.js";

// BFF uses Session + EmailPassword + UserRoles recipes for auth operations
const SUPERTOKENS_CORE_URL =
  process.env.SUPERTOKENS_CORE_URL || "http://localhost:3567";
const API_DOMAIN = process.env.BFF_URL || "http://localhost:4000";
const WEBSITE_DOMAIN = process.env.FRONTEND_URL || "http://localhost:3000";

export const initSuperTokens = () => {
  supertokens.init({
    framework: "express",
    supertokens: {
      connectionURI: SUPERTOKENS_CORE_URL,
      apiKey: process.env.SUPERTOKENS_API_KEY || "dev-api-key-secure-2024",
    },
    appInfo: {
      appName: "PulsePanel BFF",
      apiDomain: API_DOMAIN,
      websiteDomain: WEBSITE_DOMAIN,
      apiBasePath: "/bff-auth", // Avoid conflict with auth-api on /auth
      websiteBasePath: "/auth",
    },
    recipeList: [
      EmailPassword.init({
        signUpFeature: {
          formFields: [
            {
              id: "email",
              validate: async (value) => {
                if (typeof value !== "string") {
                  return "Please provide a valid email";
                }
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                  return "Please provide a valid email address";
                }
                return undefined;
              },
            },
            {
              id: "password",
              validate: async (value) => {
                if (typeof value !== "string") {
                  return "Please provide a valid password";
                }
                if (value.length < 8) {
                  return "Password must be at least 8 characters long";
                }
                return undefined;
              },
            },
          ],
        },
      }),
      UserRoles.init({}),
      UserMetadata.init(),
      Session.init({
        getTokenTransferMethod: () => "cookie", // Принудительно используем куки вместо заголовков
        cookieDomain:
          process.env.NODE_ENV === "production"
            ? process.env.COOKIE_DOMAIN
            : undefined,
        cookieSecure: process.env.NODE_ENV === "production",
        sessionExpiredStatusCode: 401,
        antiCsrf: "VIA_TOKEN",
        cookieSameSite: process.env.NODE_ENV === "production" ? "none" : "lax",
        // ✅ ИСПРАВЛЕНИЕ: Добавляем недостающие настройки для HttpOnly куки
        override: {
          functions: (originalImplementation) => {
            return {
              ...originalImplementation,
              createNewSession: async function (input) {
                const session = await originalImplementation.createNewSession(input);

                // Логируем создание сессии для отладки
                if (process.env.NODE_ENV === "development") {
                  console.log("🍪 BFF Session Created:", {
                    sessionHandle: session.getHandle(),
                    userId: session.getUserId(),
                    accessTokenPayload: session.getAccessTokenPayload(),
                  });
                }

                return session;
              },
            };
          },
        },
      }),
    ],
    telemetry: false, // Disabled for BFF, only enabled in auth-api
  });

  console.log(
    "✅ SuperTokens initialized for BFF with EmailPassword + UserRoles + Session recipes",
  );
};
