import { EventBus, createEventBus, EventBusConfig } from "./index";

/**
 * Singleton instance of EventBus for Next.js applications
 */
let eventBus: EventBus | null = null;

/**
 * Get or create a singleton instance of EventBus for Next.js applications
 *
 * Usage:
 * ```typescript
 * // In a Next.js API route or server component
 * import { getEventBusInstance } from '@pulsepanel/events/nextjs-singleton';
 *
 * export default async function handler(req, res) {
 *   const eventBus = getEventBusInstance();
 *   await eventBus.publish('topic', { ... });
 * }
 * ```
 */
export function getEventBusInstance(): EventBus {
  if (!eventBus) {
    const rabbitUrl = process.env.RABBITMQ_URL;
    const nodeEnv = process.env.NODE_ENV;

    const busConfig: EventBusConfig =
      nodeEnv === "test" || !rabbitUrl
        ? { type: "noop" }
        : {
            type: "rabbitmq",
            rabbitUrl,
            defaultExchange: process.env.RABBITMQ_EXCHANGE || "pulse.topic",
          };

    eventBus = createEventBus(busConfig);

    // Handle graceful shutdown
    if (typeof process !== "undefined") {
      process.on("SIGTERM", async () => {
        await disconnectEventBus();
      });

      process.on("SIGINT", async () => {
        await disconnectEventBus();
      });
    }
  }

  return eventBus;
}

/**
 * Disconnect the EventBus singleton instance
 * This should be called during application shutdown
 */
export async function disconnectEventBus(): Promise<void> {
  if (eventBus) {
    try {
      await eventBus.disconnect();
      console.log("[EventBus] Disconnected singleton instance");
    } catch (err) {
      console.error("[EventBus] Error disconnecting:", err);
    } finally {
      eventBus = null;
    }
  }
}
