---
description: 
globs: 
alwaysApply: false
---
This file provides guidance to CURSOR when working with code in this repository.

## Essential Development Commands

### Project Setup
```bash
pnpm install                    # Install all dependencies
pnpm services:up                # Start PostgreSQL, Redis, and RabbitMQ via Docker
pnpm codegen                    # Generate GraphQL and Prisma types
```

### Development
```bash
pnpm dev                        # Start all apps in development mode
pnpm -F @pulsepanel/<app> <command> <options> # Start <app> in development mode
pnpm test                       # Run all tests across packages
pnpm test:watch                 # Run tests in watch mode
pnpm lint                       # Lint entire codebase
pnpm format                     # Format code with Prettier
```

### Database & Services
```bash
pnpm services:up                # Start all Docker services (PostgreSQL, Redis, RabbitMQ)
pnpm services:down              # Stop all Docker services
pnpm services:logs              # View Docker service logs
pnpm events:up                  # Start only RabbitMQ service
```

### Building & Deployment
```bash
pnpm build                      # Build all packages
pnpm clean                      # Clean build artifacts
```

### Running strategy
``` bash
pnpm -F <app> <command> <options>
```

## High-Level Architecture

PulsePanel is a **monorepo** using **Backend for Frontend (BFF)** architecture with event-driven capabilities and multi-tenant support.

### Core Structure
- **`apps/frontend/`** - Next.js 15 client with React 19, Apollo Client for GraphQL
- **`apps/bff/`** - Express + Apollo Server GraphQL API using TypeGraphQL
- **`packages/db/`** - Prisma ORM with PostgreSQL, multi-tenant schema (User, Tenant, UserTenant)
- **`packages/events/`** - RabbitMQ integration for event-driven architecture
- **`packages/core-sdk/`** - Shared types and utilities

### Technology Stack
- **Language**: TypeScript 5.4.5 (ES modules across all packages)
- **Frontend**: Next.js 15.3.1 + React 19 + Apollo Client
- **Backend**: Express + Apollo Server 4.10.4 + TypeGraphQL 2.0.0-rc.2
- **Database**: PostgreSQL + Prisma 5.22.0 + typegraphql-prisma
- **Events**: RabbitMQ via amqplib
- **Cache**: Redis via ioredis
- **Monorepo**: Turbo 2.5.3 + pnpm 10.10.0

### Data Flow
1. **Frontend → BFF**: GraphQL queries via Apollo Client
2. **BFF → Database**: Prisma ORM with auto-generated TypeGraphQL resolvers
3. **BFF → Events**: RabbitMQ for async processing
4. **Type Safety**: GraphQL Code Generator creates TypeScript types for frontend/BFF

## Development Patterns

### Code Generation Workflow
1. Change GraphQL schema in `apps/bff/src/schema/schema.graphql`
2. Run `pnpm codegen` to regenerate types in both frontend and BFF
3. Prisma schema changes require `prisma generate` (included in codegen)

### Multi-Tenant Architecture
The database uses a three-entity model:
- **User**: Individual user accounts
- **Tenant**: Organizations/workspaces  
- **UserTenant**: Junction table with role-based access

### Event-Driven Patterns
Use the `@pulsepanel/events` package for async operations:
- RabbitMQ integration for reliable message processing
- Event publishing/consuming patterns already established

### Package Dependencies
- Use `workspace:*` for internal package references
- All packages use ES modules (`"type": "module"`)
- Shared configs in `configs/` directory (ESLint, TypeScript)

## Important Configuration

### Pre-commit Hooks
Husky + lint-staged automatically runs:
- ESLint with --fix
- Prettier formatting  
- Related tests for changed files

### Environment Setup
- Docker Compose provides PostgreSQL, Redis, and RabbitMQ
- Environment variables configured via .env files
- Health checks included for all services

### TypeScript Configuration
- Unified TypeScript 5.4.5 across all packages
- Shared configs via `@pulsepanel/tsconfig-custom`
- Strict mode enabled with modern ES module support

## Testing Strategy

Currently implemented in `packages/events/` with Jest 29.7.0. When adding tests to other packages:
- Use Jest with ts-jest for TypeScript support
- Follow existing patterns in events package
- Run tests via `pnpm test` (uses Turbo for caching)