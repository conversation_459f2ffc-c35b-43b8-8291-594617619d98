# Security Directives
directive @authenticated on FIELD_DEFINITION
directive @hasRole(role: String!) on FIELD_DEFINITION  
directive @belongsToTenant on FIELD_DEFINITION
directive @rateLimit(max: Int!, window: Int!) on FIELD_DEFINITION

# Auth Types
type AuthError {
  code: String!
  message: String!
  field: String
}

type AuthResponse {
  success: Boolean!
  user: User
  sessionInfo: SessionInfo
  error: AuthError
  sessionDuration: Int # TTL in seconds for frontend reference
}

# RBAC Types
type RoleResponse {
  success: Boolean!
  didUserAlreadyHaveRole: Boolean
  didUserHaveRole: Boolean
  error: AuthError
}

type UserRolesResponse {
  success: Boolean!
  roles: [String!]
  error: AuthError
}

type UsersWithRoleResponse {
  success: Boolean!
  users: [String!]
  error: AuthError
}

type CreateRoleResponse {
  success: Boolean!
  createdNewRole: Boolean
  error: AuthError
}

type DeviceInfo {
  deviceType: String
  userAgent: String
  location: String
  ipAddress: String
}

type SecurityMetadata {
  lastLoginAt: String
  loginAttempts: Int!
  riskScore: Float!
  suspiciousActivity: Boolean!
}

type SessionInfo {
  userId: String!
  sessionHandle: String!
  tenantId: String
  roles: [String!]!
  issuedAt: String!
  expiresAt: String!
  deviceInfo: DeviceInfo
  securityMetadata: SecurityMetadata
}

input UserRegistrationInput {
  email: String!
  password: String!
  name: String
  tenantId: String!
}

input UserMetadataInput {
  first_name: String
  last_name: String
  avatar_url: String
  phone: String
}

# RBAC Inputs
input AssignRoleInput {
  userId: String!
  role: String!
  tenantId: String = "public"
}

input RemoveRoleInput {
  userId: String!
  role: String!
  tenantId: String = "public"
}

input CreateRoleInput {
  role: String!
  permissions: [String!] = []
}

type User {
  id: ID!
  name: String
  tenantId: String!
  roles: [String!]
  createdAt: String!
  updatedAt: String!
}

# Resource Types
type Project {
  id: ID!
  name: String!
  description: String
  ownerId: String!
  tenantId: String!
  isActive: Boolean!
  owner: User!
  createdAt: String!
  updatedAt: String!
}

type ResourceOwnershipResponse {
  success: Boolean!
  hasAccess: Boolean!
  reason: String
  error: AuthError
}

type Query {
  hello: String
  
  # User queries - role-based access  
  users: [User!]! @authenticated @hasRole(role: "manager") @rateLimit(max: 50, window: 60)
  user(id: ID!): User @authenticated @belongsToTenant
  
  # Current user info - authenticated only
  me: User @authenticated
  
  # Session info - authenticated only
  sessionInfo: SessionInfo @authenticated
  
  # Auth status check - public endpoint for initial auth state
  checkAuthStatus: Boolean!
  
  # RBAC Queries - admin/manager only
  getUserRoles(userId: String!, tenantId: String = "public"): UserRolesResponse! @authenticated @hasRole(role: "manager") @rateLimit(max: 100, window: 60)
  getUsersWithRole(role: String!, tenantId: String = "public"): UsersWithRoleResponse! @authenticated @hasRole(role: "admin") @rateLimit(max: 50, window: 60)
  
  # Resource Ownership Queries - for middleware validation
  checkResourceOwnership(resourceType: String!, resourceId: String!): ResourceOwnershipResponse! @authenticated @rateLimit(max: 200, window: 60)
  
  # Project Queries
  projects: [Project!]! @authenticated @belongsToTenant @rateLimit(max: 100, window: 60)
  project(id: ID!): Project @authenticated @belongsToTenant @rateLimit(max: 100, window: 60)
}

type Mutation {
  # Auth mutations with security directives
  signUp(input: UserRegistrationInput!): AuthResponse! @rateLimit(max: 5, window: 300)
  signIn(email: String!, password: String!, rememberMe: Boolean = false): AuthResponse! @rateLimit(max: 10, window: 300)
  signOut: Boolean! @authenticated
  refreshToken: AuthResponse! @authenticated @rateLimit(max: 20, window: 300)
  updateUserMetadata(metadata: UserMetadataInput!): Boolean! @authenticated
  
  # RBAC Mutations - admin only
  assignUserRole(input: AssignRoleInput!): RoleResponse! @authenticated @hasRole(role: "admin") @rateLimit(max: 20, window: 60)
  removeUserRole(input: RemoveRoleInput!): RoleResponse! @authenticated @hasRole(role: "admin") @rateLimit(max: 20, window: 60)
  createRole(input: CreateRoleInput!): CreateRoleResponse! @authenticated @hasRole(role: "admin") @rateLimit(max: 10, window: 300)
  
  # Password Management Mutations (для completeness)
  forgotPassword(email: String!): Boolean! @rateLimit(max: 5, window: 300)
  resetPassword(token: String!, newPassword: String!): AuthResponse! @rateLimit(max: 5, window: 300)
  changePassword(oldPassword: String!, newPassword: String!): Boolean! @authenticated @rateLimit(max: 3, window: 300)
} 