/**
 * GraphQL Transport Adapter
 *
 * Handles all GraphQL communication with the BFF API using Apollo Client.
 * This adapter encapsulates Apollo Client and provides a clean interface
 * for the auth service layer.
 *
 * Features:
 * - HttpOnly cookie handling with credentials: 'include'
 * - Error mapping from GraphQL to transport format
 * - Cache management
 * - Health checks
 * - Automatic retry logic
 */

import { ApolloClient, gql, ApolloError } from "@apollo/client";

import type {
  IGraphQLTransportAdapter,
  GraphQLOperation,
  TransportResponse,
  TransportHealth,
  RequestConfig,
} from "../interfaces/ITransportAdapter";

import type {
  SignInInput,
  SignUpInput,
  AuthResult,
  User,
  SessionInfo,
} from "../interfaces/IAuthService";

/**
 * GraphQL Mutations and Queries
 */
const AUTH_MUTATIONS = {
  SIGN_IN: gql`
    mutation SignIn($email: String!, $password: String!, $rememberMe: Boolean) {
      signIn(email: $email, password: $password, rememberMe: $rememberMe) {
        success
        user {
          id
          name
          createdAt
          updatedAt
        }
        sessionInfo {
          userId
          sessionHandle
          tenantId
          roles
          issuedAt
          expiresAt
          deviceInfo {
            deviceType
            userAgent
            location
            ipAddress
          }
          securityMetadata {
            lastLoginAt
            loginAttempts
            riskScore
            suspiciousActivity
          }
        }
        error {
          code
          message
          field
        }
        sessionDuration
      }
    }
  `,

  SIGN_UP: gql`
    mutation SignUp($input: UserRegistrationInput!) {
      signUp(input: $input) {
        success
        user {
          id
          name
          createdAt
          updatedAt
        }
        sessionInfo {
          userId
          sessionHandle
          tenantId
          roles
          issuedAt
          expiresAt
        }
        error {
          code
          message
          field
        }
        sessionDuration
      }
    }
  `,

  SIGN_OUT: gql`
    mutation SignOut {
      signOut
    }
  `,

  REFRESH_TOKEN: gql`
    mutation RefreshToken {
      refreshToken {
        success
        user {
          id
          name
          createdAt
          updatedAt
        }
        sessionInfo {
          userId
          sessionHandle
          tenantId
          roles
          issuedAt
          expiresAt
        }
        error {
          code
          message
          field
        }
        sessionDuration
      }
    }
  `,
};

const AUTH_QUERIES = {
  ME: gql`
    query Me {
      me {
        id
        name
        createdAt
        updatedAt
      }
    }
  `,

  CHECK_AUTH_STATUS: gql`
    query CheckAuthStatus {
      checkAuthStatus
    }
  `,

  SESSION_INFO: gql`
    query SessionInfo {
      sessionInfo {
        userId
        sessionHandle
        tenantId
        roles
        issuedAt
        expiresAt
        deviceInfo {
          deviceType
          userAgent
          location
          ipAddress
        }
        securityMetadata {
          lastLoginAt
          loginAttempts
          riskScore
          suspiciousActivity
        }
      }
    }
  `,

  USER_TENANTS: gql`
    query UserTenants {
      me {
        id
        userTenants {
          id
          userId
          tenantId
          role
          createdAt
          updatedAt
          user {
            id
            name
            createdAt
            updatedAt
          }
          tenant {
            id
            name
            createdAt
            updatedAt
          }
        }
      }
    }
  `,
};

export class GraphQLTransportAdapter implements IGraphQLTransportAdapter {
  private healthStats = {
    lastCheck: new Date().toISOString(),
    latency: 0,
    errorCount: 0,
    successCount: 0,
  };

  constructor(
    private client: ApolloClient<any>,
    private config: {
      debugMode?: boolean;
    } = {},
  ) {
    // Apollo Client now injected as dependency - no more separate client creation!
  }

  /**
   * Transform Apollo/GraphQL errors to transport format
   */
  private transformError(
    error: ApolloError | Error,
  ): TransportResponse["error"] {
    if ("graphQLErrors" in error && error.graphQLErrors?.length > 0) {
      const gqlError = error.graphQLErrors[0];
      return {
        code: (gqlError.extensions?.code as string) || "GRAPHQL_ERROR",
        message: gqlError.message,
        details: gqlError.extensions,
      };
    }

    if ("networkError" in error && error.networkError) {
      return {
        code: "NETWORK_ERROR",
        message: error.networkError.message || "Network connection failed",
        details: error.networkError,
      };
    }

    return {
      code: "UNKNOWN_ERROR",
      message: error.message || "An unexpected error occurred",
      details: error,
    };
  }

  /**
   * Execute GraphQL query with error handling and performance tracking
   */
  async query<T = any>(
    operation: GraphQLOperation,
  ): Promise<TransportResponse<T>> {
    const startTime = Date.now();

    try {
      const result = await this.client.query({
        query: gql`
          ${operation.query}
        `,
        variables: operation.variables,
        fetchPolicy: "network-only", // Always get fresh data for auth operations
      });

      const latency = Date.now() - startTime;
      this.healthStats.latency = latency;
      this.healthStats.successCount++;
      this.healthStats.lastCheck = new Date().toISOString();

      if (this.config.debugMode) {
        console.log(`GraphQL Query [${operation.operationName}]:`, {
          latency: this.healthStats.latency,
          variables: operation.variables,
          result: result.data,
        });
      }

      return {
        data: result.data,
        status: 200,
      };
    } catch (error) {
      this.healthStats.errorCount++;
      this.healthStats.lastCheck = new Date().toISOString();

      if (this.config.debugMode) {
        console.error(
          `GraphQL Query Error [${operation.operationName}]:`,
          error,
        );
      }

      return {
        error: this.transformError(error as ApolloError),
      };
    }
  }

  /**
   * Execute GraphQL mutation with error handling and performance tracking
   */
  async mutate<T = any>(
    operation: GraphQLOperation,
  ): Promise<TransportResponse<T>> {
    const startTime = Date.now();

    try {
      const result = await this.client.mutate({
        mutation: gql`
          ${operation.query}
        `,
        variables: operation.variables,
      });

      this.healthStats.latency = Date.now() - startTime;
      this.healthStats.successCount++;
      this.healthStats.lastCheck = new Date().toISOString();

      if (this.config.debugMode) {
        console.log(`GraphQL Mutation [${operation.operationName}]:`, {
          latency: this.healthStats.latency,
          variables: operation.variables,
          result: result.data,
        });
      }

      return {
        data: result.data,
        status: 200,
      };
    } catch (error) {
      this.healthStats.errorCount++;
      this.healthStats.lastCheck = new Date().toISOString();

      if (this.config.debugMode) {
        console.error(
          `GraphQL Mutation Error [${operation.operationName}]:`,
          error,
        );
      }

      return {
        error: this.transformError(error as ApolloError),
      };
    }
  }

  // Auth-specific methods implementing ITransportAdapter interface

  async signIn(input: SignInInput): Promise<TransportResponse<AuthResult>> {
    return this.mutate<{ signIn: AuthResult }>({
      operationName: "SignIn",
      query: AUTH_MUTATIONS.SIGN_IN.loc?.source.body || "",
      variables: input,
    }).then((response) => ({
      ...response,
      data: response.data?.signIn,
    }));
  }

  async signUp(input: SignUpInput): Promise<TransportResponse<AuthResult>> {
    return this.mutate<{ signUp: AuthResult }>({
      operationName: "SignUp",
      query: AUTH_MUTATIONS.SIGN_UP.loc?.source.body || "",
      variables: { input },
    }).then((response) => ({
      ...response,
      data: response.data?.signUp,
    }));
  }

  async signOut(): Promise<TransportResponse<boolean>> {
    return this.mutate<{ signOut: boolean }>({
      operationName: "SignOut",
      query: AUTH_MUTATIONS.SIGN_OUT.loc?.source.body || "",
      variables: {},
    }).then((response) => ({
      ...response,
      data: response.data?.signOut,
    }));
  }

  async refreshToken(): Promise<TransportResponse<AuthResult>> {
    return this.mutate<{ refreshToken: AuthResult }>({
      operationName: "RefreshToken",
      query: AUTH_MUTATIONS.REFRESH_TOKEN.loc?.source.body || "",
      variables: {},
    }).then((response) => ({
      ...response,
      data: response.data?.refreshToken,
    }));
  }

  async getCurrentUser(): Promise<TransportResponse<User>> {
    return this.query<{ me: User }>({
      operationName: "Me",
      query: AUTH_QUERIES.ME.loc?.source.body || "",
      variables: {},
    }).then((response) => ({
      ...response,
      data: response.data?.me,
    }));
  }

  async checkAuthStatus(): Promise<TransportResponse<boolean>> {
    return this.query<{ checkAuthStatus: boolean }>({
      operationName: "CheckAuthStatus",
      query: AUTH_QUERIES.CHECK_AUTH_STATUS.loc?.source.body || "",
      variables: {},
    }).then((response) => ({
      ...response,
      data: response.data?.checkAuthStatus || false,
    }));
  }

  /**
   * HttpOnly cookies are not accessible to client-side JavaScript (by design)
   * This is a security feature - we should accept that auth checks may return 401
   */
  async hasValidSessionCookies(): Promise<boolean> {
    // HttpOnly cookies are not readable by JavaScript
    // Always return true to proceed with server auth check
    return true;
  }

  async getSessionInfo(): Promise<TransportResponse<SessionInfo>> {
    return this.query<{ sessionInfo: SessionInfo }>({
      operationName: "SessionInfo",
      query: AUTH_QUERIES.SESSION_INFO.loc?.source.body || "",
      variables: {},
    }).then((response) => ({
      ...response,
      data: response.data?.sessionInfo,
    }));
  }

  // getUserTenants method removed - tenants now handled by SuperTokens only

  async healthCheck(): Promise<TransportHealth> {
    const startTime = Date.now();

    try {
      await this.query({
        operationName: "HealthCheck",
        query: `query HealthCheck { hello }`,
        variables: {},
      });

      const latency = Date.now() - startTime;

      return {
        isHealthy: true,
        latency,
        lastCheck: new Date().toISOString(),
      };
    } catch (error) {
      return {
        isHealthy: false,
        lastCheck: new Date().toISOString(),
        error:
          error instanceof Error ? error.message : "Unknown health check error",
      };
    }
  }

  configure(config: RequestConfig): void {
    // Update headers if needed
    if (config.headers) {
      // Note: Apollo Client headers are set during creation
      // For dynamic header updates, we'd need to recreate the client
      console.warn("Dynamic header updates require client recreation");
    }
  }

  async clearCache(): Promise<void> {
    await this.client.clearStore();
  }

  async subscribe<T = any>(
    operation: GraphQLOperation,
  ): Promise<AsyncIterableIterator<TransportResponse<T>>> {
    // Subscription implementation would go here
    // For now, throwing as subscriptions aren't needed for basic auth
    throw new Error("Subscriptions not implemented yet");
  }

  async cleanup(): Promise<void> {
    await this.client.clearStore();
    this.client.stop();
  }

  /**
   * Get transport performance statistics
   */
  getStats() {
    return {
      ...this.healthStats,
      successRate:
        this.healthStats.successCount /
          (this.healthStats.successCount + this.healthStats.errorCount) || 0,
    };
  }
}
