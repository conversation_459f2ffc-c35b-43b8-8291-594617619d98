/*
  Warnings:

  - You are about to drop the `Tenant` table. If the table is not empty, all the data it contains will be lost.
  - You are about to drop the `UserTenant` table. If the table is not empty, all the data it contains will be lost.

*/
-- DropForeignKey
ALTER TABLE "public"."UserTenant" DROP CONSTRAINT "UserTenant_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "public"."UserTenant" DROP CONSTRAINT "UserTenant_userId_fkey";

-- AlterTable
ALTER TABLE "public"."User" ADD COLUMN     "tenantId" TEXT NOT NULL DEFAULT 'public';

-- DropTable
DROP TABLE "public"."Tenant";

-- DropTable
DROP TABLE "public"."UserTenant";

-- CreateIndex
CREATE INDEX "User_tenantId_idx" ON "public"."User"("tenantId");
