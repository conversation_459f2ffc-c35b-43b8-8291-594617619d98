"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useAuth, useAuthActions } from "@/hooks/features";

export default function LogoutPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const auth = useAuth();
  const authActions = useAuthActions();

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [logoutCompleted, setLogoutCompleted] = useState(false);

  useEffect(() => {
    const autoExecute = searchParams.get("auto");

    if (autoExecute === "true" && auth.isAuthenticated) {
      const executeLogout = async () => {
        try {
          setLoading(true);
          setError(null);

          await authActions.signOut();
          setLogoutCompleted(true);
        } catch (err) {
          setError(err instanceof Error ? err.message : "Logout failed");
        } finally {
          setLoading(false);
        }
      };
      executeLogout();
    }
  }, [searchParams, auth.isAuthenticated, authActions]);

  useEffect(() => {
    if (logoutCompleted || !auth.isAuthenticated) {
      const redirectUrl = searchParams.get("redirect") || "/auth/signin";
      const delay = parseInt(searchParams.get("delay") || "2000", 10);

      const timer = setTimeout(() => {
        router.push(redirectUrl);
      }, delay);

      return () => clearTimeout(timer);
    }
  }, [logoutCompleted, auth.isAuthenticated, searchParams, router]);

  const handleLogout = async () => {
    try {
      setLoading(true);
      setError(null);

      await authActions.signOut();
      setLogoutCompleted(true);
    } catch (err) {
      setError(err instanceof Error ? err.message : "Logout failed");
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
    authActions.clearAuthError();
  };

  const currentError = error || auth.authError;

  if (!auth.isAuthenticated && !logoutCompleted) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <h2 className="mt-6 text-3xl font-extrabold text-gray-900">
              Already Logged Out
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              You are not currently signed in
            </p>
            <div className="mt-4">
              <a
                href="/auth/signin"
                className="text-red-600 hover:text-red-500 font-medium"
              >
                Go to Sign In
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Logout (Test Page)
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Test logout with URL parameters support
          </p>
        </div>

        <div className="mt-8 space-y-6">
          {currentError && (
            <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded relative">
              <span className="block sm:inline">{currentError}</span>
              <button
                onClick={clearError}
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
              >
                <span className="sr-only">Dismiss</span>×
              </button>
            </div>
          )}

          {logoutCompleted ? (
            <div className="text-center space-y-4">
              <div className="text-green-600">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M5 13l4 4L19 7"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900">
                Logout Successful
              </h3>
              <p className="text-sm text-gray-600">
                You have been successfully logged out. Redirecting...
              </p>
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-red-600 mx-auto"></div>
            </div>
          ) : loading ? (
            <div className="text-center space-y-4">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600 mx-auto"></div>
              <p className="text-sm text-gray-600">Logging out...</p>
            </div>
          ) : (
            <div className="space-y-6">
              {auth.user && (
                <div className="bg-blue-50 p-4 rounded-lg">
                  <h3 className="text-sm font-medium text-blue-900 mb-2">
                    Current User:
                  </h3>
                  <p className="text-sm text-blue-700">
                    <strong>Name:</strong> {auth.user.name || "No name"}
                  </p>
                  <p className="text-sm text-blue-700">
                    <strong>ID:</strong> {auth.user.id}
                  </p>
                  <p className="text-sm text-blue-700">
                    <strong>Tenant:</strong> {auth.user.tenantId}
                  </p>
                </div>
              )}

              <button
                onClick={handleLogout}
                disabled={loading}
                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Logging out...
                  </>
                ) : (
                  "Logout"
                )}
              </button>

              <div className="mt-6 p-4 bg-red-50 rounded-lg">
                <h3 className="text-sm font-medium text-red-900 mb-2">
                  URL Parameters:
                </h3>
                <ul className="text-xs text-red-700 space-y-1">
                  <li>
                    <code>?auto=true</code> - Auto-execute logout
                  </li>
                  <li>
                    <code>?redirect=/auth/signin</code> - Redirect after logout
                  </li>
                  <li>
                    <code>?delay=3000</code> - Delay before redirect (ms,
                    default: 2000)
                  </li>
                </ul>
                <p className="text-xs text-red-600 mt-2">
                  Example:{" "}
                  <code>/auth/logout?auto=true&redirect=/&delay=1000</code>
                </p>
              </div>

              <div className="text-center space-y-2">
                <p className="text-sm text-gray-600">
                  <a
                    href="/dashboard"
                    className="text-red-600 hover:text-red-500"
                  >
                    Go to Dashboard
                  </a>
                </p>
                <p className="text-sm text-gray-600">
                  <a
                    href="/auth/signin"
                    className="text-red-600 hover:text-red-500"
                  >
                    Go to Sign In
                  </a>
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
