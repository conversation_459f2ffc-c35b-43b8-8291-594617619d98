import type { Redis } from "ioredis";
import type {
  SessionData,
  CacheStats,
  SessionHealthStats,
  InvalidationReason,
} from "./types.js";
import { SecurityDetectionService } from "./securityDetection.js";
import { AuditService } from "./auditService.js";

export class SessionCacheService {
  private hitCount = 0;
  private missCount = 0;
  private latencySum = 0;
  private requestCount = 0;
  private securityService: SecurityDetectionService;
  private auditService: AuditService;

  constructor(private redis: Redis) {
    this.securityService = new SecurityDetectionService(redis);
    this.auditService = new AuditService(redis);
  }

  async getSession(sessionId: string): Promise<SessionData | null> {
    const startTime = Date.now();

    try {
      const cached = await this.redis.get(`session:${sessionId}`);

      if (cached) {
        // Sliding window: extend TTL on each use to keep active sessions alive
        await this.redis.expire(`session:${sessionId}`, 300);

        this.hitCount++;
        this.updateLatency(Date.now() - startTime);

        return JSON.parse(cached);
      }

      this.missCount++;
      this.updateLatency(Date.now() - startTime);

      return null;
    } catch (error) {
      console.error("Redis session cache error:", error);
      this.missCount++;
      return null;
    }
  }

  async setSession(
    sessionId: string,
    data: SessionData,
    ttl = 300,
  ): Promise<void> {
    try {
      const pipeline = this.redis.pipeline();

      pipeline.setex(`session:${sessionId}`, ttl, JSON.stringify(data));

      // Batch invalidation: group sessions by user/tenant for mass deletion
      pipeline.sadd(`user:${data.userId}:sessions`, sessionId);
      pipeline.expire(`user:${data.userId}:sessions`, ttl + 60); // Longer TTL to avoid race conditions

      if (data.tenantId) {
        pipeline.sadd(`tenant:${data.tenantId}:sessions`, sessionId);
        pipeline.expire(`tenant:${data.tenantId}:sessions`, ttl + 60);
      }

      await pipeline.exec();
    } catch (error) {
      console.error("Redis session set error:", error);
    }
  }

  async invalidateSession(sessionId: string): Promise<void> {
    try {
      const sessionData = await this.getSession(sessionId);

      const pipeline = this.redis.pipeline();
      pipeline.del(`session:${sessionId}`);

      if (sessionData) {
        pipeline.srem(`user:${sessionData.userId}:sessions`, sessionId);
        if (sessionData.tenantId) {
          pipeline.srem(`tenant:${sessionData.tenantId}:sessions`, sessionId);
        }

        // Log the invalidation event
        await this.auditService.logInvalidationEvent(
          sessionId,
          sessionData.userId,
          sessionData.tenantId,
          "logout",
          "user",
        );
      }

      await pipeline.exec();
    } catch (error) {
      console.error("Redis session invalidation error:", error);
    }
  }

  async invalidateUserSessions(
    userId: string,
    reason: InvalidationReason = "admin_action",
    triggeredBy: "system" | "user" | "admin" | "security" = "admin",
  ): Promise<void> {
    try {
      const sessions = await this.redis.smembers(`user:${userId}:sessions`);

      if (sessions.length > 0) {
        const pipeline = this.redis.pipeline();

        sessions.forEach((sessionId) => {
          pipeline.del(`session:${sessionId}`);
        });

        pipeline.del(`user:${userId}:sessions`);

        await pipeline.exec();

        // Log each session invalidation
        for (const sessionId of sessions) {
          await this.auditService.logInvalidationEvent(
            sessionId,
            userId,
            undefined,
            reason,
            triggeredBy,
          );
        }
      }
    } catch (error) {
      console.error("Redis user sessions invalidation error:", error);
    }
  }

  async invalidateTenantSessions(
    tenantId: string,
    reason: InvalidationReason = "admin_action",
    triggeredBy: "system" | "user" | "admin" | "security" = "admin",
  ): Promise<void> {
    try {
      const sessions = await this.redis.smembers(`tenant:${tenantId}:sessions`);

      if (sessions.length > 0) {
        const pipeline = this.redis.pipeline();

        sessions.forEach((sessionId) => {
          pipeline.del(`session:${sessionId}`);
        });

        pipeline.del(`tenant:${tenantId}:sessions`);

        await pipeline.exec();

        // Log tenant-wide invalidation
        for (const sessionId of sessions) {
          await this.auditService.logInvalidationEvent(
            sessionId,
            "unknown", // We don't have userId here
            tenantId,
            reason,
            triggeredBy,
          );
        }
      }
    } catch (error) {
      console.error("Redis tenant sessions invalidation error:", error);
    }
  }

  // Advanced invalidation methods
  async invalidateByRole(
    userId: string,
    oldRoles: string[],
    newRoles: string[],
  ): Promise<void> {
    const rolesChanged =
      JSON.stringify(oldRoles.sort()) !== JSON.stringify(newRoles.sort());

    if (rolesChanged) {
      await this.invalidateUserSessions(userId, "role_change", "system");
      console.log(
        `🔄 Role change: Invalidated all sessions for user ${userId}`,
      );
    }
  }

  async invalidateBySecurityViolation(
    userId: string,
    sessionId: string,
    violationType: string,
    details: Record<string, any>,
  ): Promise<void> {
    try {
      // Invalidate the specific session
      await this.invalidateSession(sessionId);

      // Log security violation
      await this.auditService.logInvalidationEvent(
        sessionId,
        userId,
        undefined,
        "security_violation",
        "security",
        { violationType, ...details },
      );

      console.warn(
        `🚨 Security violation: ${violationType} for user ${userId}`,
      );
    } catch (error) {
      console.error("Security invalidation error:", error);
    }
  }

  async enforceConcurrentSessionLimit(
    userId: string,
    maxSessions = 5,
  ): Promise<void> {
    try {
      const sessions = await this.redis.smembers(`user:${userId}:sessions`);

      if (sessions.length > maxSessions) {
        // Remove oldest sessions (keep most recent)
        const sessionDetails = [];

        for (const sessionId of sessions) {
          const sessionData = await this.getSession(sessionId);
          if (sessionData) {
            sessionDetails.push({ sessionId, data: sessionData });
          }
        }

        // Sort by login time (oldest first)
        sessionDetails.sort((a, b) => {
          // We don't have login time in SessionData, so we'll use a different approach
          return a.sessionId.localeCompare(b.sessionId);
        });

        const sessionsToRemove = sessionDetails.slice(
          0,
          sessions.length - maxSessions,
        );

        for (const { sessionId } of sessionsToRemove) {
          await this.invalidateSession(sessionId);
          await this.auditService.logInvalidationEvent(
            sessionId,
            userId,
            undefined,
            "concurrent_limit",
            "system",
            { maxSessions, totalSessions: sessions.length },
          );
        }

        console.log(
          `📊 Concurrent limit: Removed ${sessionsToRemove.length} old sessions for user ${userId}`,
        );
      }
    } catch (error) {
      console.error("Concurrent session limit enforcement error:", error);
    }
  }

  async invalidateByDeviceType(
    userId: string,
    deviceType: "web" | "mobile",
  ): Promise<void> {
    try {
      const sessions = await this.redis.smembers(`user:${userId}:sessions`);
      let invalidatedCount = 0;

      for (const sessionId of sessions) {
        const metadataKey = `user:${userId}:meta`;
        const metadataStr = await this.redis.get(metadataKey);

        if (metadataStr) {
          const metadata = JSON.parse(metadataStr);
          if (metadata.deviceType === deviceType) {
            await this.invalidateSession(sessionId);
            await this.auditService.logInvalidationEvent(
              sessionId,
              userId,
              undefined,
              "device_change",
              "admin",
              { deviceType },
            );
            invalidatedCount++;
          }
        }
      }

      if (invalidatedCount > 0) {
        console.log(
          `📱 Device invalidation: Removed ${invalidatedCount} ${deviceType} sessions for user ${userId}`,
        );
      }
    } catch (error) {
      console.error("Device-based invalidation error:", error);
    }
  }

  async performSecurityCheck(
    sessionId: string,
    userId: string,
    ipAddress: string,
    userAgent: string,
  ): Promise<boolean> {
    try {
      const result = await this.securityService.analyzeSession(
        sessionId,
        userId,
        ipAddress,
        userAgent,
      );

      if (
        result.recommendedAction === "block" ||
        result.recommendedAction === "logout"
      ) {
        await this.invalidateBySecurityViolation(
          userId,
          sessionId,
          "security_analysis",
          {
            riskLevel: result.riskLevel,
            flags: result.flags,
            recommendedAction: result.recommendedAction,
          },
        );
        return false; // Session invalidated
      }

      return true; // Session is valid
    } catch (error) {
      console.error("Security check error:", error);
      return true; // Default to allowing session on error
    }
  }

  async getSessionHealthStats(): Promise<SessionHealthStats> {
    try {
      const keys = await this.redis.keys("session:*");
      const userKeys = await this.redis.keys("user:*:sessions");
      const metaKeys = await this.redis.keys("user:*:meta");

      // Calculate memory usage (rough estimate)
      const memoryUsageMB = Math.round(
        (keys.length * 0.5 + userKeys.length * 0.1 + metaKeys.length * 0.3) /
          1024,
      );

      // Get cleanup stats from audit service
      const cleanupStats = await this.auditService.getInvalidationStats(1);

      return {
        totalActiveSessions: keys.length,
        expiredSessions: 0, // Redis handles TTL automatically
        orphanedSessions: Math.max(0, keys.length - userKeys.length * 3), // Rough estimate
        memoryUsageMB,
        cleanupEventsLast24h: cleanupStats.totalEvents,
        securityEventsLast24h: cleanupStats.securityEvents,
        averageSessionLifetime: 300, // 5 minutes TTL
      };
    } catch (error) {
      console.error("Session health stats error:", error);
      return {
        totalActiveSessions: 0,
        expiredSessions: 0,
        orphanedSessions: 0,
        memoryUsageMB: 0,
        cleanupEventsLast24h: 0,
        securityEventsLast24h: 0,
        averageSessionLifetime: 0,
      };
    }
  }

  async performAutomaticCleanup(): Promise<{
    cleaned: number;
    errors: number;
  }> {
    let cleaned = 0;
    let errors = 0;

    try {
      // Clean up expired sessions that Redis didn't remove
      const allKeys = await this.redis.keys("session:*");

      for (const key of allKeys) {
        try {
          const ttl = await this.redis.ttl(key);
          if (ttl === -1) {
            // Key exists but has no TTL
            await this.redis.del(key);
            cleaned++;
          }
        } catch {
          errors++;
        }
      }

      // Clean audit logs
      const auditCleanup = await this.auditService.cleanupOldEvents();
      cleaned += auditCleanup.deleted;
      errors += auditCleanup.errors;

      if (cleaned > 0) {
        console.log(
          `🧹 Automatic cleanup: ${cleaned} items cleaned, ${errors} errors`,
        );
      }
    } catch (error) {
      console.error("Automatic cleanup error:", error);
      errors++;
    }

    return { cleaned, errors };
  }

  private updateLatency(latency: number): void {
    this.latencySum += latency;
    this.requestCount++;
  }

  async getCacheStats(): Promise<CacheStats> {
    try {
      const keys = await this.redis.keys("session:*");
      const hitRate =
        this.requestCount > 0 ? this.hitCount / this.requestCount : 0;
      const avgLatency =
        this.requestCount > 0 ? this.latencySum / this.requestCount : 0;

      return {
        totalSessions: keys.length,
        hitRate: Number((hitRate * 100).toFixed(2)),
        avgLatency: Number(avgLatency.toFixed(2)),
      };
    } catch (error) {
      console.error("Redis cache stats error:", error);
      return {
        totalSessions: 0,
        hitRate: 0,
        avgLatency: 0,
      };
    }
  }

  async clearAllSessions(): Promise<void> {
    try {
      const keys = await this.redis.keys("session:*");
      const userKeys = await this.redis.keys("user:*:sessions");
      const tenantKeys = await this.redis.keys("tenant:*:sessions");

      const allKeys = [...keys, ...userKeys, ...tenantKeys];

      if (allKeys.length > 0) {
        await this.redis.del(...allKeys);

        // Log mass invalidation
        await this.auditService.logInvalidationEvent(
          "all_sessions",
          "system",
          undefined,
          "system_cleanup",
          "admin",
          { clearedSessions: keys.length },
        );
      }
    } catch (error) {
      console.error("Redis clear all sessions error:", error);
    }
  }

  // Public wrapper methods for external access
  async initializeSecurityMetadata(
    userId: string,
    sessionId: string,
    ipAddress: string,
    userAgent: string,
  ): Promise<void> {
    return this.securityService.initializeSessionMetadata(
      userId,
      sessionId,
      ipAddress,
      userAgent,
    );
  }

  async getAuditEvents(limit = 50): Promise<any[]> {
    return this.auditService.getRecentEvents(limit);
  }

  async getAuditStats(days = 7): Promise<Record<string, any>> {
    return this.auditService.getInvalidationStats(days);
  }
}
