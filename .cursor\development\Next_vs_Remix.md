### Next .js vs Remix — что надёжнее «в запас» на 6–18 месяцев, если Next уже стоит в проекте

| Фактор                               | **Next .js 14 (App Router)**                                                                                          | **Remix v2 (Vite‑core)**                                                                               | Что важнее для PulsePanel                                    |
| ------------------------------------ | --------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------ | ------------------------------------------------------------ |
| **Объём экосистемы**                 | Самый большой в React‑мире: MDX, auth‑провайдеры, UI‑киты, плагины APM.                                               | Экосистема растёт, но в разы меньше; иногда портируешь либы вручную.                                   | Чем меньше времени на обёртки — тем лучше.                   |
| **Stability roadmap**                | App Router & Server Actions объявлены «stable» (Oct‑2024); RSC в продакшн‑режиме у Vercel, Shopify Hydrogen, Patreon. | Remix API стабильный, но в Core‑Team ≈ 8 человек, релизы медленнее.                                    | Поддержка крупных вендоров = дольше живёт.                   |
| **SSR / Edge / ISR**                 | Встроенный ISR, Full & Partial Prerender; edge‑middleware out‑of‑box.                                                 | С v2 можно рендерить на edge, но ISR нужно писать руками (ETag + KV).                                  | Если позже захотите кэшировать «public KPI»–страницы.        |
| **Формы / Actions**                  | Server Actions (stable) + useForm()(Заглушек CSRF нет, но есть либы).                                                 | Loader/Action pattern с CSRF из коробки.                                                               | Для OAuth‑callback обоим нужно API‑route; разница небольшая. |
| **Хостинг‑варианты**                 | Любой Node, но идеальный fit — Vercel / AWS Lambda.                                                                   | Любой Node/edge; Fly.io лидер.                                                                         | Мы и так можем деплоить Node‑процесс на Fly или DO.          |
| **Refine / Mantine интеграция**      | Официальный пакет `@refinedev/nextjs‑router`.                                                                         | Официальный `@refinedev/remix‑router`.                                                                 | Паритет.                                                     |
| **Команда уже настроила**            | ✅ Есть репо, CI, вероятно набор страниц.                                                                             | ❌ Нужно мигрировать.                                                                                  | Миграция дороже, чем допилить формы в Next.                  |
| **Долгосрочный риск «переписывать»** | Невысокий: App Router назад‑несовместимых сдвигов нет; RSC допиливают, но API не ломаeтся.                            | Риск, что придётся вручную внедрять ISR/Streaming‑лонг‑кэш или переходить на Next ради RSC‑экосистемы. | Next стабильнее как «default React server framework».        |

### Что это значит практично

- **GraphQL‑BFF** легко подключается через `@apollo/client` + Next Server Actions или традиционные `getServerSideProps`.
- **Shopify/Instagram OAuth** — `app/api/auth/[provider]/route.ts` отлично ловит callback.
- **Product‑tagging формы** — можно:
  1. серверное действие `export async function action(prevState, formData)` (Next 14),
  2. или классический `/api/link-product` с CSRF‑токеном из `next‑csurf`.
- **Hot‑reload** и DX у Next 14 на `next dev` сравнимы с Remix‑Vite (ручные бенчмарки ≈ 400‑600 ms HMR).

### Как не попасть на переезд через 3–6 месяцев

1. **Изолировать серверные функции** в `/server` или `/utils/server‑only.ts` — если когда‑либо перейдёте на Remix или tRPC, придётся трогать только одну папку.
2. **GraphQL fetcher** — делайте универсальный `graphql‑request`‑hook, а не Next‑specific `fetch`, чтобы он жил и в Remix.
3. **Формы** — не завязывайтесь на `useFormStatus` глубоко; оставьте fallback `<form action="/api/…">` + progressive enhancement.
4. **Env‑границы** — храните `process.env.*` доступные клиенту через `NEXT_PUBLIC_` — в Remix это превратится в `import.meta.env.PUBLIC_*`.

### Итог

**Оставляем Next .js 14** как фронтенд‑движок:

- он уже настроен → не тратим 1‑2 недели на переезд;
- экосистема самая большая, App Router устаканился;
- все ключевые требования PulsePanel (SSR, формы, OAuth, HMR) закрываются без хака.

Выигрыш Remix (loader/action DX) **не перекрывает** затрат миграции и риска, что через полгода вернётесь к Next ради RSC/ISR‑фич или найма разработчиков.

###  Формы: Remix vs Next.js

_(почему о них часто спорят и что действительно добавляет Remix)_

| Возможность                            | **Remix**                                                                                                                                                                              | **Next .js 14 (App Router)**                                                                                                                            | Что это даёт разработчику                                                                                 |
| -------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------- | --------------------------------------------------------------------------------------------------------- |
| **Loader + Action паттерн**            | `export async function action()` внутри того же файла, где и компонент; возвращаем `json                                                                                               | redirect`.                                                                                                                                              | **Server Actions** делают то же, но пока только внутри `use formAction` и требуют `\"use server\"` блока. |
| **Прогрессивное улучшение по дефолту** | Простое `<Form method=\"post\">` — если JS отключён, браузер делает полноценный POST; с JS Remix перехватывает, шлёт _fetch_ + возвращает асинхронный _response_.                      | Тот же `<form>` работает, но без JS не будет встроенной маршрутизации (вернёмся на origin). Улучшать придётся самим через `fetch()` или хук библиотеки. | В Remix нет “провала” UX при отключённом JS; всё из коробки.                                              |
| **CSRF‑безопасность**                  | Action автоматически проверяет `Content‑Type: application/x-www-form-urlencoded` + двойной submit токен шаблоном `<input type=\"hidden\" name=\"_csrf\" value={csrf}>` (готовый util). | Нет встроенного CSRF; используют пакеты `next-csrf`, `next-auth` стэк, или приходится писать в middleware.                                              | Меньше boilerplate в Remix, особенно для OAuth‑callback.                                                  |
| **Валидация и возврат ошибок**         | Action может возвратить `{ errors, values }` → компонент `<Form>` автоматически получает `useActionData()`; серверный голос об ошибке фокусирует поле с `<input name>` без доп. кода.  | В Server Actions нужно вручную формировать response, передавать его в компонент и делать фокус.                                                         | В Remix «happy path» и «error path» одинаково короткие.                                                   |
| **Nested‑route формы**                 | Action/loader живут внутри конкретного маршрута сегмента (например `routes/products/$id.edit.tsx`) и видят родительский loader через `useLoaderData()`.                                | В Next Server Actions глобальны; для вложенных макетов надо вручную пробрасывать пропсы.                                                                | Легче строить “Wizard” или модалки с вложенными путями в Remix.                                           |
| **Параллельность действий**            | Хук `useFetcher()` позволяет отправлять несколько независимых форм (пример: «Like», «Save draft») без потерянного состояния главной формы.                                             | Server Actions + `useTransition` дают похожее, но API моложе, нет встроенного `abort()` и прогресса.                                                    | В Remix старая отлаженная модель fetcher.                                                                 |
| **File Upload**                        | `<Form encType=\"multipart/form-data\">` → Action получает `request.formData()` без стороннего API.                                                                                    | В Next до Server Actions нужно было ставить `uploadthing`/`next-connect`; сейчас Server Actions умеют Files, но без progress‑callback.                  | Плюс Remix: одинаковый DX для urlencoded и multipart.                                                     |
| **Scroll restoration / focus‑restore** | Встроено: после submit Remix скроллит к месту ошибки или на top.                                                                                                                       | Нужно вручную scrollTo; Server Actions пока не делают этого за вас.                                                                                     | Улучшает UX мобильных форм без кода.                                                                      |
| **Edge‑рендер + формы**                | `<Form>` работает и в edge runtime (Cloudflare, Fly) без stateful API.                                                                                                                 | Server Actions сейчас не поддерживаются на edge (можно только в Node runtime); придётся fallback на API‑route.                                          | Remix формы могут жить целиком на edge без доп. сервера.                                                  |

### Где Next всё-таки догнал

- **Server Actions** убрали большинство болей с ручными API‑routes; теперь можно писать

  ```tsx
  "use server";
  export async function save(formData) {
    /* … */
  }
  ```

  и вызывать их через `<form action={save}>`.

  Это почти такой же DX, как Remix action.

- Библиотеки вроде **react‑hook‑form, zod, uploadthing** уже дают лайф‑валидацию и удобное API поверх Next.

### Итог для PulsePanel

| Если **остаемся на Next**                                         | Что придётся покрыть                    |
| ----------------------------------------------------------------- | --------------------------------------- |
| Выбрать CSRF‑решение (`next-csrf` или proxy GraphQL POST)         | Чёткая middle‑layer.                    |
| Использовать Server Actions только там, где Node runtime          | Instagram WebHook/OAuth всё равно Node. |
| Самостоятельно обработать scroll/focus на ошибку (маленький util) | UX‑штрих.                               |

| Если **переходим на Remix**                       | Что выигрываем                 |
| ------------------------------------------------- | ------------------------------ |
| “из коробки” безопасные формы, CSRF, edge‑run     | Меньше кода и багов.           |
| Nested route loaders удобны для dashboard widgets | Быстрее городить новые экраны. |

Но поскольку Next уже внедрён и Server Actions зрелы, **разница сейчас не критична**. Базовые плюсы Remix можно догнать:

_один пакет CSRF + небольшой форм‑helper_ vs 1‑2 недели миграции репо.

**Вывод:** для долгосрока PulsePanel, оставшись на Next.js, мы не теряем критичных возможностей форм; просто закроем два инфраструктурных нюанса — CSRF‑middleware и scroll/focus‑helper.
