# Validation Over Modification

- Prioritize extensive validation and comprehensive understanding of existing code and its operational context before introducing alterations or creating new artifacts.

## Task Focus
- Only make changes that are directly related to the assigned task.
- DO NOT make changes to code that is not directly relevant to the current task.
- DO NOT fix pre-existing issues (including linter errors) unless they were introduced by your changes within the current task or directly impede its completion.

## Security
- During development, pay attention to potential vulnerabilities and write secure code.
- Consider security best practices (e.g., input validation, output escaping, avoiding SQL injection, checking permissions).

## Error Handling
- Implement robust error handling mechanisms (e.g., try-catch blocks, Result types, checking return codes) to improve code reliability.
- Pay special attention to error handling when dealing with file I/O, network requests, database interactions, and other external systems or resources.
- Ensure that exceptional and unexpected situations are handled appropriately.

## Comments
- Add comments only to explain **non-obvious** or **complex** logic, important **business decisions**, or the **reasoning** ('why') behind a specific, perhaps non-standard, approach.
- **DO NOT add** trivial comments that merely duplicate what is already **clear from the code** itself (e.g., `// Increment counter` for `counter++;`).
- Strive for self-documenting code. Comments should supplement the code, not repeat it.


# MCP services

- Use context7 MCP services for all external integrations
- Use context7 MCP services for all database interactions


# State Applied Rules

## Critical Rules

- The very first line of any response MUST state which rules are being applied
- Format the statement as: "⚠️Applied rules: [rule1.mdc, rule2.mdc, ...]"
- If no rules are being applied, state: "No rules applied."
- Use only the filename of the rule (without path) as the identifier
- Rules must be listed in alphabetical order
- Multiple rules should be separated by commas and a space
- This statement must appear before any other content, including greetings or explanations

## Examples

<example>
⚠️Applied rules: python-style-auto.mdc, typescript-standards-agent.mdc

Here is my response to your query about implementing the new feature...
</example>

<example>
No rules applied.

Let me help you with your question about file organization...
</example>

<example type="invalid">
Here is my implementation following our Python standards...

⚠️Applied rules: python-style-auto.mdc

Or:

I'm using the following rules: .cursor/rules/python/python-style-auto.mdc

Or:

[Without any statement about applied rules]
</example> 

