{"name": "@pulsepanel/core-sdk", "version": "0.1.0", "private": true, "type": "module", "main": "dist/index.js", "types": "dist/index.d.ts", "exports": {".": {"import": "./dist/index.js", "types": "./dist/index.d.ts"}}, "scripts": {"build": "tsc", "dev": "tsc --watch", "lint": "eslint src", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {}, "devDependencies": {"@pulsepanel/eslint-config-custom": "workspace:*", "@types/node": "^20.11.20", "typescript": "^5.4.5", "vitest": "^2.1.8", "@vitest/coverage-v8": "^2.1.8"}}