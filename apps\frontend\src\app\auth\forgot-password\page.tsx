"use client";

import React, { useState, useEffect } from "react";
import { useRouter, useSearchParams } from "next/navigation";

export default function ForgotPasswordPage() {
  const router = useRouter();
  const searchParams = useSearchParams();

  const [email, setEmail] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [emailSent, setEmailSent] = useState(false);

  useEffect(() => {
    const urlEmail = searchParams.get("email");
    const autoExecute = searchParams.get("auto");

    if (urlEmail) setEmail(urlEmail);

    if (autoExecute === "true" && urlEmail) {
      const executeForgotPassword = async () => {
        if (!urlEmail) {
          setError("Email is required");
          return;
        }

        try {
          setLoading(true);
          setError(null);

          console.log("🔑 Forgot password request for:", urlEmail);

          await new Promise((resolve) => setTimeout(resolve, 1000));

          setEmailSent(true);

          const redirectUrl = searchParams.get("redirect");
          if (redirectUrl) {
            setTimeout(() => {
              router.push(redirectUrl);
            }, 2000);
          }
        } catch (err) {
          setError(
            err instanceof Error ? err.message : "Failed to send reset email",
          );
        } finally {
          setLoading(false);
        }
      };
      executeForgotPassword();
    }
  }, [searchParams, router]);

  const handleForgotPassword = async () => {
    if (!email) {
      setError("Email is required");
      return;
    }

    try {
      setLoading(true);
      setError(null);

      console.log("🔑 Forgot password request for:", email);

      await new Promise((resolve) => setTimeout(resolve, 1000));

      setEmailSent(true);

      const redirectUrl = searchParams.get("redirect");
      if (redirectUrl) {
        setTimeout(() => {
          router.push(redirectUrl);
        }, 2000);
      }
    } catch (err) {
      setError(
        err instanceof Error ? err.message : "Failed to send reset email",
      );
    } finally {
      setLoading(false);
    }
  };

  const clearError = () => {
    setError(null);
  };

  if (emailSent) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="max-w-md w-full space-y-8">
          <div className="text-center">
            <div className="text-green-600 mb-4">
              <svg
                className="mx-auto h-12 w-12"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                />
              </svg>
            </div>
            <h2 className="text-3xl font-extrabold text-gray-900">
              Check Your Email
            </h2>
            <p className="mt-2 text-sm text-gray-600">
              We've sent a password reset link to <strong>{email}</strong>
            </p>
            <div className="mt-4 space-y-2">
              <p className="text-xs text-gray-500">
                Didn't receive the email? Check your spam folder or try again.
              </p>
              <a
                href="/auth/signin"
                className="text-purple-600 hover:text-purple-500 font-medium"
              >
                Back to Sign In
              </a>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <div className="max-w-md w-full space-y-8">
        <div>
          <h2 className="mt-6 text-center text-3xl font-extrabold text-gray-900">
            Forgot Password (Test Page)
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Test forgot password with URL parameters support
          </p>
        </div>

        <div className="mt-8 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-300 text-red-700 px-4 py-3 rounded relative">
              <span className="block sm:inline">{error}</span>
              <button
                onClick={clearError}
                className="absolute top-0 bottom-0 right-0 px-4 py-3"
              >
                <span className="sr-only">Dismiss</span>×
              </button>
            </div>
          )}

          <div className="space-y-4">
            <div>
              <label
                htmlFor="email"
                className="block text-sm font-medium text-gray-700"
              >
                Email Address
              </label>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-purple-500 focus:border-purple-500"
                placeholder="Enter your email address"
                disabled={loading}
              />
            </div>

            <button
              onClick={handleForgotPassword}
              disabled={loading || !email}
              className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-purple-600 hover:bg-purple-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-purple-500 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {loading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  Sending...
                </>
              ) : (
                "Send Reset Email"
              )}
            </button>
          </div>

          <div className="mt-6 p-4 bg-purple-50 rounded-lg">
            <h3 className="text-sm font-medium text-purple-900 mb-2">
              URL Parameters:
            </h3>
            <ul className="text-xs text-purple-700 space-y-1">
              <li>
                <code>?email=<EMAIL></code> - Pre-fill email
              </li>
              <li>
                <code>?auto=true</code> - Auto-execute password reset request
              </li>
              <li>
                <code>?redirect=/auth/signin</code> - Redirect after email sent
              </li>
            </ul>
            <p className="text-xs text-purple-600 mt-2">
              Example:{" "}
              <code>
                /auth/forgot-password?email=<EMAIL>&auto=true
              </code>
            </p>
          </div>

          <div className="text-center space-y-2">
            <p className="text-sm text-gray-600">
              Remember your password?{" "}
              <a
                href="/auth/signin"
                className="text-purple-600 hover:text-purple-500"
              >
                Sign in
              </a>
            </p>
            <p className="text-sm text-gray-600">
              Don't have an account?{" "}
              <a
                href="/auth/signup"
                className="text-purple-600 hover:text-purple-500"
              >
                Sign up
              </a>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
