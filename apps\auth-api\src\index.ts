import express, { Application } from "express";
import type { Request, Response, NextFunction } from "express";
import cors from "cors";
import helmet from "helmet";
import * as dotenv from "dotenv";
import supertokens from "supertokens-node";
import { middleware } from "supertokens-node/framework/express/index.js";
import { errorHandler } from "supertokens-node/framework/express/index.js";

// Load environment variables
dotenv.config();

// SuperTokens configuration
import "./config/supertokens.js";

const app: Application = express();
const PORT = process.env.PORT || 3001;

// Security middleware
app.use(
  helmet({
    crossOriginResourcePolicy: { policy: "cross-origin" },
    contentSecurityPolicy: false,
  }),
);

// CORS configuration
app.use(
  cors({
    origin: [
      process.env.FRONTEND_URL || "http://localhost:3000",
      process.env.BFF_URL || "http://localhost:4000",
      process.env.WEBSITE_DOMAIN || "http://localhost:3000",
    ],
    allowedHeaders: [
      "content-type",
      "authorization",
      "x-requested-with",
      ...supertokens.getAllCORSHeaders(),
    ],
    methods: ["GET", "PUT", "POST", "DELETE", "OPTIONS"],
    credentials: true,
  }),
);

// SuperTokens middleware
app.use(middleware());

// Health check endpoint
app.get("/health", (req: Request, res: Response) => {
  res.json({
    status: "ok",
    service: "auth-api",
    timestamp: new Date().toISOString(),
    version: "1.0.0",
  });
});

// Basic info endpoint
app.get("/", (req: Request, res: Response) => {
  res.json({
    service: "PulsePanel Auth API",
    version: "1.0.0",
    endpoints: {
      health: "/health",
      auth: "/auth/*",
      dashboard: "/auth/dashboard",
      status: "/status",
    },
  });
});

// SuperTokens status endpoint
app.get("/status", async (req: Request, res: Response) => {
  try {
    // Check if SuperTokens is properly initialized
    const coreUrl = process.env.SUPERTOKENS_CORE_URL || "http://localhost:3567";

    res.json({
      status: "ok",
      service: "auth-api",
      supertokens: {
        coreUrl: coreUrl,
        initialized: true,
        dashboard: `${process.env.API_DOMAIN || "http://localhost:3001"}/auth/dashboard`,
      },
      database: {
        host: process.env.POSTGRES_HOST || "postgres",
        database: process.env.POSTGRES_DB || "pulsepanel_dev_db",
        schema: "auth",
      },
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error("Status check error:", error);
    res.status(500).json({
      status: "error",
      service: "auth-api",
      error: error instanceof Error ? error.message : "Unknown error",
      timestamp: new Date().toISOString(),
    });
  }
});

// SuperTokens error handling
app.use(errorHandler());

// Global error handler
app.use((err: Error, req: Request, res: Response, next: NextFunction) => {
  console.error("Auth API error:", err);
  res.status(500).json({
    error: "Internal server error",
    message:
      process.env.NODE_ENV === "development"
        ? err.message
        : "Something went wrong",
  });
});

// Start server
app.listen(PORT, () => {
  console.log(`🚀 Auth API running on port ${PORT}`);
  console.log(
    `🔗 Health check: ${process.env.API_DOMAIN || `http://localhost:${PORT}`}/health`,
  );
});

export default app;
