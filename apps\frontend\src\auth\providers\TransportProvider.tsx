"use client";

import React, { createContext, useContext, ReactNode } from "react";
import { ApolloProvider as BaseApolloProvider } from "@apollo/client";
import { getApolloClient } from "@/lib/apollo/client";
import type { ITransportFactory } from "@/auth/factories/TransportFactory";
import { ApolloTransportFactory } from "@/auth/factories/TransportFactory";

interface TransportContextValue {
  transportFactory: ITransportFactory;
}

const TransportContext = createContext<TransportContextValue | null>(null);

interface TransportProviderProps {
  children: ReactNode;
  factory?: ITransportFactory;
}

/**
 * Transport Provider
 *
 * Provides transport factory for auth service.
 * Isolates Apollo Client from auth layer - AuthServiceProvider
 * doesn't know about transport implementation details.
 */
export function TransportProvider({
  children,
  factory,
}: TransportProviderProps) {
  const apolloClient = getApolloClient();

  // Use provided factory or create Apollo-based as default
  const transportFactory = factory || new ApolloTransportFactory(apolloClient);

  return (
    <BaseApolloProvider client={apolloClient}>
      <TransportContext.Provider value={{ transportFactory }}>
        {children}
      </TransportContext.Provider>
    </BaseApolloProvider>
  );
}

export function useTransportFactory(): ITransportFactory {
  const context = useContext(TransportContext);

  if (!context) {
    throw new Error(
      "useTransportFactory must be used within a TransportProvider",
    );
  }

  return context.transportFactory;
}
