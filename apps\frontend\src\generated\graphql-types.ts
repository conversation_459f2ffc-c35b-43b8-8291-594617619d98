import { gql } from '@apollo/client';
import * as <PERSON> from '@apollo/client';
import * as ApolloReactHooks from '@apollo/client';
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
const defaultOptions = {} as const;
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
};

export type AssignRoleInput = {
  role: Scalars['String']['input'];
  tenantId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type AuthError = {
  __typename?: 'AuthError';
  code: Scalars['String']['output'];
  field?: Maybe<Scalars['String']['output']>;
  message: Scalars['String']['output'];
};

export type AuthResponse = {
  __typename?: 'AuthResponse';
  error?: Maybe<AuthError>;
  sessionDuration?: Maybe<Scalars['Int']['output']>;
  sessionInfo?: Maybe<SessionInfo>;
  success: Scalars['Boolean']['output'];
  user?: Maybe<User>;
};

export type CreateRoleInput = {
  permissions?: InputMaybe<Array<Scalars['String']['input']>>;
  role: Scalars['String']['input'];
};

export type CreateRoleResponse = {
  __typename?: 'CreateRoleResponse';
  createdNewRole?: Maybe<Scalars['Boolean']['output']>;
  error?: Maybe<AuthError>;
  success: Scalars['Boolean']['output'];
};

export type DeviceInfo = {
  __typename?: 'DeviceInfo';
  deviceType?: Maybe<Scalars['String']['output']>;
  ipAddress?: Maybe<Scalars['String']['output']>;
  location?: Maybe<Scalars['String']['output']>;
  userAgent?: Maybe<Scalars['String']['output']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  assignUserRole: RoleResponse;
  changePassword: Scalars['Boolean']['output'];
  createRole: CreateRoleResponse;
  forgotPassword: Scalars['Boolean']['output'];
  refreshToken: AuthResponse;
  removeUserRole: RoleResponse;
  resetPassword: AuthResponse;
  signIn: AuthResponse;
  signOut: Scalars['Boolean']['output'];
  signUp: AuthResponse;
  updateUserMetadata: Scalars['Boolean']['output'];
};


export type MutationAssignUserRoleArgs = {
  input: AssignRoleInput;
};


export type MutationChangePasswordArgs = {
  newPassword: Scalars['String']['input'];
  oldPassword: Scalars['String']['input'];
};


export type MutationCreateRoleArgs = {
  input: CreateRoleInput;
};


export type MutationForgotPasswordArgs = {
  email: Scalars['String']['input'];
};


export type MutationRemoveUserRoleArgs = {
  input: RemoveRoleInput;
};


export type MutationResetPasswordArgs = {
  newPassword: Scalars['String']['input'];
  token: Scalars['String']['input'];
};


export type MutationSignInArgs = {
  email: Scalars['String']['input'];
  password: Scalars['String']['input'];
  rememberMe?: InputMaybe<Scalars['Boolean']['input']>;
};


export type MutationSignUpArgs = {
  input: UserRegistrationInput;
};


export type MutationUpdateUserMetadataArgs = {
  metadata: UserMetadataInput;
};

export type Project = {
  __typename?: 'Project';
  createdAt: Scalars['String']['output'];
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  isActive: Scalars['Boolean']['output'];
  name: Scalars['String']['output'];
  owner: User;
  ownerId: Scalars['String']['output'];
  tenantId: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type Query = {
  __typename?: 'Query';
  checkAuthStatus: Scalars['Boolean']['output'];
  checkResourceOwnership: ResourceOwnershipResponse;
  getUserRoles: UserRolesResponse;
  getUsersWithRole: UsersWithRoleResponse;
  hello?: Maybe<Scalars['String']['output']>;
  me?: Maybe<User>;
  project?: Maybe<Project>;
  projects: Array<Project>;
  sessionInfo?: Maybe<SessionInfo>;
  user?: Maybe<User>;
  users: Array<User>;
};


export type QueryCheckResourceOwnershipArgs = {
  resourceId: Scalars['String']['input'];
  resourceType: Scalars['String']['input'];
};


export type QueryGetUserRolesArgs = {
  tenantId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};


export type QueryGetUsersWithRoleArgs = {
  role: Scalars['String']['input'];
  tenantId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryProjectArgs = {
  id: Scalars['ID']['input'];
};


export type QueryUserArgs = {
  id: Scalars['ID']['input'];
};

export type RemoveRoleInput = {
  role: Scalars['String']['input'];
  tenantId?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['String']['input'];
};

export type ResourceOwnershipResponse = {
  __typename?: 'ResourceOwnershipResponse';
  error?: Maybe<AuthError>;
  hasAccess: Scalars['Boolean']['output'];
  reason?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type RoleResponse = {
  __typename?: 'RoleResponse';
  didUserAlreadyHaveRole?: Maybe<Scalars['Boolean']['output']>;
  didUserHaveRole?: Maybe<Scalars['Boolean']['output']>;
  error?: Maybe<AuthError>;
  success: Scalars['Boolean']['output'];
};

export type SecurityMetadata = {
  __typename?: 'SecurityMetadata';
  lastLoginAt?: Maybe<Scalars['String']['output']>;
  loginAttempts: Scalars['Int']['output'];
  riskScore: Scalars['Float']['output'];
  suspiciousActivity: Scalars['Boolean']['output'];
};

export type SessionInfo = {
  __typename?: 'SessionInfo';
  deviceInfo?: Maybe<DeviceInfo>;
  expiresAt: Scalars['String']['output'];
  issuedAt: Scalars['String']['output'];
  roles: Array<Scalars['String']['output']>;
  securityMetadata?: Maybe<SecurityMetadata>;
  sessionHandle: Scalars['String']['output'];
  tenantId?: Maybe<Scalars['String']['output']>;
  userId: Scalars['String']['output'];
};

export type User = {
  __typename?: 'User';
  createdAt: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  name?: Maybe<Scalars['String']['output']>;
  roles?: Maybe<Array<Scalars['String']['output']>>;
  tenantId: Scalars['String']['output'];
  updatedAt: Scalars['String']['output'];
};

export type UserMetadataInput = {
  avatar_url?: InputMaybe<Scalars['String']['input']>;
  first_name?: InputMaybe<Scalars['String']['input']>;
  last_name?: InputMaybe<Scalars['String']['input']>;
  phone?: InputMaybe<Scalars['String']['input']>;
};

export type UserRegistrationInput = {
  email: Scalars['String']['input'];
  name?: InputMaybe<Scalars['String']['input']>;
  password: Scalars['String']['input'];
  tenantId: Scalars['String']['input'];
};

export type UserRolesResponse = {
  __typename?: 'UserRolesResponse';
  error?: Maybe<AuthError>;
  roles?: Maybe<Array<Scalars['String']['output']>>;
  success: Scalars['Boolean']['output'];
};

export type UsersWithRoleResponse = {
  __typename?: 'UsersWithRoleResponse';
  error?: Maybe<AuthError>;
  success: Scalars['Boolean']['output'];
  users?: Maybe<Array<Scalars['String']['output']>>;
};

export type GetUsersQueryVariables = Exact<{ [key: string]: never; }>;


export type GetUsersQuery = { __typename?: 'Query', users: Array<{ __typename?: 'User', id: string, name?: string | null, tenantId: string, createdAt: string, updatedAt: string }> };

export type GetUserQueryVariables = Exact<{
  id: Scalars['ID']['input'];
}>;


export type GetUserQuery = { __typename?: 'Query', user?: { __typename?: 'User', id: string, name?: string | null, tenantId: string, createdAt: string, updatedAt: string } | null };


export const GetUsersDocument = gql`
    query GetUsers {
  users {
    id
    name
    tenantId
    createdAt
    updatedAt
  }
}
    `;

/**
 * __useGetUsersQuery__
 *
 * To run a query within a React component, call `useGetUsersQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUsersQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUsersQuery({
 *   variables: {
 *   },
 * });
 */
export function useGetUsersQuery(baseOptions?: ApolloReactHooks.QueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
      }
export function useGetUsersLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
        }
export function useGetUsersSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUsersQuery, GetUsersQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUsersQuery, GetUsersQueryVariables>(GetUsersDocument, options);
        }
export type GetUsersQueryHookResult = ReturnType<typeof useGetUsersQuery>;
export type GetUsersLazyQueryHookResult = ReturnType<typeof useGetUsersLazyQuery>;
export type GetUsersSuspenseQueryHookResult = ReturnType<typeof useGetUsersSuspenseQuery>;
export type GetUsersQueryResult = Apollo.QueryResult<GetUsersQuery, GetUsersQueryVariables>;
export const GetUserDocument = gql`
    query GetUser($id: ID!) {
  user(id: $id) {
    id
    name
    tenantId
    createdAt
    updatedAt
  }
}
    `;

/**
 * __useGetUserQuery__
 *
 * To run a query within a React component, call `useGetUserQuery` and pass it any options that fit your needs.
 * When your component renders, `useGetUserQuery` returns an object from Apollo Client that contains loading, error, and data properties
 * you can use to render your UI.
 *
 * @param baseOptions options that will be passed into the query, supported options are listed on: https://www.apollographql.com/docs/react/api/react-hooks/#options;
 *
 * @example
 * const { data, loading, error } = useGetUserQuery({
 *   variables: {
 *      id: // value for 'id'
 *   },
 * });
 */
export function useGetUserQuery(baseOptions: ApolloReactHooks.QueryHookOptions<GetUserQuery, GetUserQueryVariables> & ({ variables: GetUserQueryVariables; skip?: boolean; } | { skip: boolean; }) ) {
        const options = {...defaultOptions, ...baseOptions}
        return ApolloReactHooks.useQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
      }
export function useGetUserLazyQuery(baseOptions?: ApolloReactHooks.LazyQueryHookOptions<GetUserQuery, GetUserQueryVariables>) {
          const options = {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useLazyQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
        }
export function useGetUserSuspenseQuery(baseOptions?: ApolloReactHooks.SkipToken | ApolloReactHooks.SuspenseQueryHookOptions<GetUserQuery, GetUserQueryVariables>) {
          const options = baseOptions === ApolloReactHooks.skipToken ? baseOptions : {...defaultOptions, ...baseOptions}
          return ApolloReactHooks.useSuspenseQuery<GetUserQuery, GetUserQueryVariables>(GetUserDocument, options);
        }
export type GetUserQueryHookResult = ReturnType<typeof useGetUserQuery>;
export type GetUserLazyQueryHookResult = ReturnType<typeof useGetUserLazyQuery>;
export type GetUserSuspenseQueryHookResult = ReturnType<typeof useGetUserSuspenseQuery>;
export type GetUserQueryResult = Apollo.QueryResult<GetUserQuery, GetUserQueryVariables>;