# Sprint 1 — Shopify Integration & KPI Base

**Duration:** 3 weeks (15 working days)

---

## Sprint Goal

Deliver the first end-to-end slice: Shopify OAuth, scheduled data pull, event bus abstraction (RabbitMQ), metrics ETL, PostgreSQL schema, GraphQL `dashboardMetrics` query, and dashboard widgets that show **Revenue, Orders, AOV** for the last 7 days.

---

## 1 High-level Milestones

| Day range | Milestone                                                               |
| --------- | ----------------------------------------------------------------------- |
| 1 – 3     | EventBus abstraction (`@pulsepanel/events`) + local RabbitMQ stack      |
| 4 – 7     | core-sdk Shopify client + OAuth callback in Next API route              |
| 8 – 11    | shopify-collector (cron) + queue-publisher dual-write stub              |
| 12 – 13   | metrics-etl MVP (aggregate revenue/order_count/aov) + Prisma migrations |
| 14        | GraphQL `dashboardMetrics` resolver                                     |
| 15        | FE widgets (`<KpiCard revenue orders aov />`) + QA & demo               |

_Buffer 2 days inside milestones for spill-over._

---

## 2 Detailed Backlog

### 2.1 Event Bus Abstraction (BE1 + DevOps)

| Task                                                                               | Acceptance criteria                                      |
| ---------------------------------------------------------------------------------- | -------------------------------------------------------- |
| Design TypeScript interface `EventBus` & `EventEnvelope` (id ULID, type, payload). | `packages/events/src/index.ts` exported and 100 % typed. |
| Implement `rabbitBus` adapter using `amqplib`, exchange `pulse.topic`.             | Unit test publishes and consumes message locally.        |
| Implement stub `noopBus` for local unit tests.                                     | No runtime dependency on Rabbit.                         |
| DI: NestJS provider + Next singleton (`eventBus.use(rabbitBus)`).                  | All imports compile.                                     |
| Dev tooling: `pnpm events:up` spins rabbit via docker-compose.                     | Docs in README.                                          |

### 2.2 Shopify OAuth & core-sdk (BE2 + FE support)

| Task                                                                                                     | Acceptance                                                        |
| -------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------- |
| Next API route `/api/auth/shopify/redirect` (App Router action).                                         | Redirects to Shopify consent screen.                              |
| `/api/auth/shopify/callback` exchanges `code` for access token, inserts/updates `store` row in Postgres. | Manual test with dev store passes, token stored encrypted (Jose). |
| `core-sdk/shopify.ts`: thin wrapper around `graphql-request`, retry, rate-limit.                         | Query `shop { name plan }` returns data.                          |
| Store model migration: `shopifyAccessToken`, `shopId`.                                                   | `prisma migrate dev` runs.                                        |

### 2.3 shopify-collector & queue-publisher (BE1)

| Task                                                                                    | Acceptance                                                                         |
| --------------------------------------------------------------------------------------- | ---------------------------------------------------------------------------------- |
| Cron worker (BullMQ – repeatable) every 5 minutes; pulls `orders` updated since cursor. | Inserts at least 1 event per order to `EventBus.publish("shop.order.created.v1")`. |
| Cursor stored in Postgres table `collector_cursor`.                                     | Restart resumes without duplicates.                                                |
| queue-publisher publishes envelope to RabbitMQ topic exchange.                          | Rabbit management UI shows message rate.                                           |
| Dual-write flag (`PUBLISH_NOOP=true`) for offline dev.                                  | Flagging disables real Rabbit call.                                                |

### 2.4 metrics-etl MVP (BE2)

| Task                                                                                 | Acceptance                    |
| ------------------------------------------------------------------------------------ | ----------------------------- |
| NestJS microservice subscribes to `shop.order.created.v1`.                           | Logs “consumed”.              |
| Derive `total_price` + `line_items` count.                                           | Unit test fixture.            |
| Aggregate per store & per day -> table `daily_metrics` (date, revenue, orders, aov). | SQL migration + Prisma model. |
| Upsert logic idempotent by `order_id`.                                               | Double-publish test passes.   |
| Back-fill command `pnpm etl:replay --from 2025-01-01`.                               | Runs locally.                 |

### 2.5 GraphQL & BFF (BE1 + BE2)

| Task                                                                                  | Acceptance                              |
| ------------------------------------------------------------------------------------- | --------------------------------------- |
| Extend schema: `type Metric { date: Date! revenue: Float! orders: Int! aov: Float! }` | SDL merged.                             |
| Resolver `dashboardMetrics(storeId, range)` pulls from `daily_metrics`.               | Unit & Postman test.                    |
| Add dataloader cache per request.                                                     | Two queries for same store hit DB once. |

### 2.6 Frontend widgets (FE)

| Task                                                       | Acceptance                |
| ---------------------------------------------------------- | ------------------------- |
| Create `<KpiCard title value delta />` using ShadCN Card. | Storybook snapshot.       |
| Dashboard fetch `/graphql` query and renders 3 cards.      | Manual QA shows numbers.  |
| Loading skeleton + error state.                            | Network throttle test ok. |
| Cypress e2e: login -> dashboard shows cards (mocked data). | CI green.                 |

### 2.7 DevOps / CI

| Task                                                                                                      | Acceptance                |
| --------------------------------------------------------------------------------------------------------- | ------------------------- |
| RabbitMQ service added to `docker-compose.dev.yml`.                                                       | `docker compose up` runs. |
| Secrets storage: `SHOPIFY_API_KEY`, `SHOPIFY_API_SECRET` in `.dev.env`; GitHub Actions uses repo secrets. | CI passes.                |
| Add workflow `publish-docker` building collector & etl images (no deploy).                                | Image cached in GHCR.     |

---

## 3 Definition of Done (Sprint 1)

- Shopify OAuth end-to-end with real dev store.
- RabbitMQ running locally; EventBus abstraction used everywhere.
- shopify-collector writes events; metrics-etl persists daily revenue/orders/aov.
- GraphQL `dashboardMetrics` returns correct aggregates (unit snapshot).
- Dashboard page displays 3 KPI cards for sample store.
- CI: lint, unit tests, docker build succeed.
- Documentation: `docs/sprint-1.md` with setup & API examples.

---

## 4 Risks & Mitigations

| Risk                                    | Mitigation                                               |
| --------------------------------------- | -------------------------------------------------------- |
| Rate-limit 2/sec on Shopify dev stores. | Use cursor+sleep in collector; stub mode for load tests. |
| RabbitMQ container flaky on M1 laptops. | Provide LocalStack SQS fallback adapter for dev.         |
| OAuth redirect URL mismatch.            | Add HTTPS tunnel (ngrok) + docs.                         |
| ETL idempotency bug.                    | ULID event_id + Postgres unique index on `order_id`.     |

---

## 5 Outcome Metrics

- Time to first dashboard render ≤ 600 ms local.
- Collector lag < 2 min (monitor queue depth).
- Unit test coverage ≥ 60 % for collector & etl packages.
- Sprint burndown chart hits 0 by day 15.

| Вопрос                          | Выбранный подход                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                   | Причина / детали реализации                                                                                                                                                                                                                                                                                                                                    |
| ------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | -------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |
| **1. Session Storage**          | **Redis Cluster**                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  | _Почему:_• быстрый TTL-кэш для `state`, OAuth nonce, short-lived online-sessions;• одинаково работает локально (docker-redis) и в клауде;• вписывается в общий стек (Redis уже нужен для rate-limit)._Реализация:_• key `shp:session:<state>` → JSON {shop,nonce} TTL = 10 мин;• NestJS `RedisModule` + type-safe wrapper;• fallback для jest — in-memory map. |
| **2. Online vs Offline tokens** | **Сохраняем оба** • offline token — в таблице **Store** (`accessTokenOffline`); • online token — в Redis (`shp:online:<shop>:<userId>` TTL = 24 ч).                                                                                                                                                                                                                                                                                                                                                                                                                                                                                | _Зачем:_• offline нужен collectors/ETL, когда юзера нет;• online нужен при action-API от лица конкретного обладателя прав (например, «Создать скидку»)._Flow:_ запрос `grant_options[]=per-user` → callback вернёт оба.                                                                                                                                        |
| **3. Error handling**           | Единая util-ф-ция `handleShopifyError(e)`→ classifies: `OAuthError`, `RateLimitError`, `ShopUnavailable`, `ValidationError`, `Unknown`→ лог OTel + Sentry + GraphQL error map в UI.                                                                                                                                                                                                                                                                                                                                                                                                                                                | Код`if(e.response?.status === 429) throw new RateLimitError(retryAfter)`.Callback-route возвращает 400 с html-stripe если `hmac`/`state` invalid.Webhook 400 → Shopify переотправит; 410 → отписывает.                                                                                                                                                         |
| **4. Rate limiting**            | • Используем **Shopify “leaky-bucket”** — каждый ответ содержит `X-Shopify-Shop-Api-Call-Limit: 39/40`.• core-sdk оборачивает запросы в `p-queue`, считывая заголовок, если осталось ≤ 2 — ждём `500 ms`.• Collectors делят нагрузку по магазину (`partitionKey=storeId`) — параллелизма нет внутри одного магазина.                                                                                                                                                                                                                                                                                                               |                                                                                                                                                                                                                                                                                                                                                                |
| **5. Embedded vs Non-Embedded** | **Non-embedded (стэндэлон)** приложение на собственном домене.                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     | У PulsePanel уже Next front-end; не нужен App Bridge, меньше требований к CSP и Iframe-монтажу._Если позже понадобится embedded_ — роут `/embedded` с `@shopify/app-bridge-react`, но не ломает текущий UX.                                                                                                                                                    |
| **6. Multi-Shop support**       | структура базы данных (ConnectedAccounts таблица должна быть спроектирована так, чтобы позволять одному userId иметь несколько записей для разных интеграций одного типа (например, два магазина Shopify). Backend API должно быть способно обрабатывать запросы в контексте конкретного подключенного аккаунта (например, передавая connectedAccountId в запросах к бэкенду для получения данных дашборда по конкретному магазину). UI MVP может изначально ограничивать пользователя возможностью добавить только один аккаунт каждого типа, но архитектура бэкенда уже будет готова к поддержке нескольких магазинов в будущем. |                                                                                                                                                                                                                                                                                                                                                                |

## Sprint 1.1 – **Shopify OAuth & core-sdk** (5 working days)

**Goal:**

_Merchant installs PulsePanel app in a Shopify dev-store; offline + online tokens are stored (PostgreSQL + Redis); first GraphQL call `shop { name plan }` succeeds through the shared SDK; multi-shop enabled._

---

## Pre-sprint Checklist (Day 0)

| ENV template | Add keys to `.env.example`: |
| ------------ | --------------------------- |

```jsx
SHOPIFY_API_KEY=
SHOPIFY_API_SECRET=
SHOPIFY_SCOPES=read_products,read_orders
APP_URL=https://localhost:3000
REDIS_URL=redis://redis:6379
```

| Dev app | Partners console → update callback **${APP_URL}/api/auth/shopify/callback** |

| DB ext | Run `CREATE EXTENSION IF NOT EXISTS pgcrypto;` for field encryption |

| Dev app | Partners console → update callback **${APP_URL}/api/auth/shopify/callback** |

---

## Day 1 – API Routes & Session Storage

| #                                                                           | Task                                                                                                                                                                                                    | Acceptance                                  |
| --------------------------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------------------- |
| 1.1                                                                         | `/api/auth/shopify/redirect` (Next App Route). Generates `state=ULID`, stores `shp:session:<state>` in **Redis** JSON {shop,nonce} TTL 10 min, sets cookie `shp_state`. Redirects to Shopify OAuth URL. | Manual check: cookie set; Redis key exists. |
| 1.2                                                                         | `/api/auth/shopify/callback` route:                                                                                                                                                                     |                                             |
| • verifies `hmac`, `state` (Redis) → 400 on fail;                           |                                                                                                                                                                                                         |                                             |
| • exchanges `code` for `access_token` & `associated_user` (online+offline); | Invalid hmac returns 400.                                                                                                                                                                               |                                             |
| 1.3                                                                         | Store rows: if shop new, create **Store** record with encrypted `accessTokenOffline`; delete `shp:session`.                                                                                             | Prisma migration passes.                    |

---

## Day 2 – Online Token Cache & Multi-Shop

| #   | Task                                                                                                                               | Acceptance                      |
| --- | ---------------------------------------------------------------------------------------------------------------------------------- | ------------------------------- |
| 2.1 | Save online token to Redis key `shp:online:<shop>:<userId>` TTL 24 h.                                                              | Redis TTL verified.             |
| 2.2 | Create **Account** model; link Store→Account many-to-one.                                                                          | Sign-in flows keep `accountId`. |
| 2.3 | Switch-store dropdown in Settings page ( Next Route `/settings/stores`). JWT stores `currentStoreId`; FE hook `useCurrentStore()`. | Add second store, UI switches.  |
| 2.4 | Migration SQL unique index `(accountId, shopDomain)`.                                                                              |                                 |

---

## Day 3 – core-sdk & Rate-limit Guard

| #   | Task                                                                                                                  | Acceptance                     |
| --- | --------------------------------------------------------------------------------------------------------------------- | ------------------------------ |
| 3.1 | `packages/core-sdk/shopify.ts` — factory `createShopifyClient(domain, token)` using `graphql-request`.                | Unit test compiles.            |
| 3.2 | Util `shopifyFetch()` wraps request; inspects header `X-Shopify-Shop-Api-Call-Limit`; if used≥38→ `await delay(500)`. | Jest mock verifies throttling. |
| 3.3 | GraphQL codegen for file `queries/shop.graphql`; generate typed SDK.                                                  | Type mismatch fails CI.        |
| 3.4 | Error utility `handleShopifyError()` returns `OAuthError`, `RateLimitError`, etc.; logs via OTel.                     | 429 mocked → RateLimitError.   |

---

## Day 4 – BFF Resolver & FE Integration

| #   | Task                                                                                                                                  | Acceptance                     |
| --- | ------------------------------------------------------------------------------------------------------------------------------------- | ------------------------------ |
| 4.1 | BFF context loads offline token from Store; injects shopifyClient.                                                                    | Apollo playground query works. |
| 4.2 | Resolver `shopInfo(storeId)` → `{ name, plan }`.                                                                                      | Returns real store name.       |
| 4.3 | FE `/integrations` page:• If no current store → “Connect Shopify” button → redirect API route.• If connected → show shop name & plan. | Cypress flow passes.           |

---

## Day 5 – QA, Docs & Security

| #                                      | Task                                                                    | Acceptance      |
| -------------------------------------- | ----------------------------------------------------------------------- | --------------- |
| 5.1                                    | Cypress e2e: install app, token saved, dashboard loads KPIs (mock ETL). | CI green.       |
| 5.2                                    | Security checklist:                                                     |                 |
| • HTTP-only cookies;                   |                                                                         |                 |
| • Redis session TTL 10 min;            |                                                                         |                 |
| • Rotate `shp_state` cookie after use. | Checklist signed.                                                       |                 |
| 5.3                                    | Docs `docs/auth/shopify.md`: env setup, ngrok HTTPS, add-store flow.    | Reviewed by TL. |

---

## Deliverables

- Redis-backed session & online-token cache.
- PostgreSQL `Account` / `Store` with encrypted offline token.
- Core Shopify SDK with rate-limit guard & typed queries.
- Multi-shop capable UI (connect / switch).
- Error util + logging via OTel.
- Full QA scripts & documentation.

---

### Out-of-Scope for Sprint 1.1

- Webhook registration/processing
- Orders/products collectors (Sprint 1.2)
- ETL aggregation (Sprint 1.3)

Sprint 1.1 completes when a dev-store is connected and its name appears on the integrations page, with tokens stored and tests passing.
