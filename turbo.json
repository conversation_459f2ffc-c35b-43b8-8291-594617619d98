{"$schema": "https://raw.githubusercontent.com/vercel/turbo/main/packages/turbo-repository-config-schema/schema.json", "tasks": {"build": {"dependsOn": ["^build"], "outputs": ["dist/**", ".next/**", "!.next/cache/**"]}, "lint": {"outputs": []}, "test": {"dependsOn": ["^build"], "outputs": ["coverage/**"]}, "dev": {"cache": false, "persistent": true}, "clean": {"cache": false}, "codegen": {"cache": false, "persistent": true}}}