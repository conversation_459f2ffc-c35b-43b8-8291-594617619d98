#!/usr/bin/env node

/**
 * Enterprise RBAC Middleware Test Suite
 * Tests hierarchical permissions, tenant isolation, dynamic routes, and advanced security features
 */

const fs = require('fs');

// Test configuration
const config = {
  frontendUrl: process.env.FRONTEND_URL || 'http://localhost:3000',
  bffUrl: process.env.BFF_URL || 'http://localhost:4000',
  testUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!',
    roles: ['user'] // Will be promoted during tests
  },
  timeouts: {
    auth: 10000,
    middleware: 5000,
    navigation: 3000
  }
};

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  dim: '\x1b[2m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  white: '\x1b[37m'
};

class EnterpriseRBACTester {
  constructor() {
    this.testResults = [];
    this.authCookies = null;
    this.currentUserRoles = ['user'];
    this.startTime = Date.now();
  }

  log(message, color = colors.white) {
    const timestamp = new Date().toISOString().substr(11, 12);
    console.log(`${colors.dim}[${timestamp}]${colors.reset} ${color}${message}${colors.reset}`);
  }

  logSuccess(message) {
    this.log(`✅ ${message}`, colors.green);
  }

  logError(message) {
    this.log(`❌ ${message}`, colors.red);
  }

  logWarning(message) {
    this.log(`⚠️  ${message}`, colors.yellow);
  }

  logInfo(message) {
    this.log(`ℹ️  ${message}`, colors.blue);
  }

  async recordTest(testName, success, details = '') {
    this.testResults.push({
      test: testName,
      success,
      details,
      timestamp: new Date().toISOString()
    });

    if (success) {
      this.logSuccess(`${testName}: ${details}`);
    } else {
      this.logError(`${testName}: ${details}`);
    }
  }

  async makeRequest(url, options = {}) {
    const defaultHeaders = {
      'User-Agent': 'Enterprise-RBAC-Tester/1.0',
      'Accept': 'application/json, text/html',
      ...(this.authCookies && { 'Cookie': this.authCookies })
    };

    const requestOptions = {
      method: 'GET',
      headers: { ...defaultHeaders, ...options.headers },
      redirect: 'manual', // Don't follow redirects automatically
      ...options
    };

    try {
      const response = await fetch(url, requestOptions);
      
      // Capture cookies from response
      if (response.headers.get('set-cookie') && !this.authCookies) {
        this.authCookies = response.headers.get('set-cookie');
      }

      return {
        ok: response.ok,
        status: response.status,
        statusText: response.statusText,
        headers: Object.fromEntries(response.headers.entries()),
        url: response.url,
        redirected: response.redirected,
        type: response.type
      };
    } catch (error) {
      return {
        ok: false,
        status: 0,
        error: error.message
      };
    }
  }

  async signInUser() {
    this.logInfo('Step 1: Authenticating user for RBAC testing...');

    try {
      const response = await fetch(`${config.bffUrl}/graphql`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          query: `
            mutation SignInForRBACTest($email: String!, $password: String!) {
              signIn(email: $email, password: $password) {
                success
                message
                user {
                  id
                  email
                  roles
                  tenantId
                }
                sessionDuration
              }
            }
          `,
          variables: {
            email: config.testUser.email,
            password: config.testUser.password
          }
        })
      });

      if (!response.ok) {
        throw new Error(`GraphQL request failed: ${response.status}`);
      }

      // Capture auth cookies
      const setCookieHeader = response.headers.get('set-cookie');
      if (setCookieHeader) {
        this.authCookies = setCookieHeader;
      }

      const data = await response.json();

      if (data.errors) {
        throw new Error(`GraphQL errors: ${JSON.stringify(data.errors)}`);
      }

      const signInResult = data.data?.signIn;
      if (signInResult?.success) {
        this.currentUserRoles = signInResult.user?.roles || ['user'];
        await this.recordTest('User Authentication', true, 
          `User signed in with roles: ${this.currentUserRoles.join(', ')}`);
        return true;
      } else {
        throw new Error(signInResult?.message || 'Sign in failed');
      }
    } catch (error) {
      await this.recordTest('User Authentication', false, error.message);
      return false;
    }
  }

  async testPublicRoutes() {
    this.logInfo('Testing public routes...');
    
    const routes = [
      { path: '/', description: 'Landing page' },
      { path: '/auth/signin', description: 'Sign in page' },
      { path: '/auth/signup', description: 'Sign up page' },
      { path: '/auth/forgot-password', description: 'Forgot password page' },
      { path: '/auth/reset-password', description: 'Reset password page' },
      { path: '/unauthorized', description: 'Unauthorized page' },
      { path: '/unauthorized/admin', description: 'Admin unauthorized page' },
      { path: '/unauthorized/manager', description: 'Manager unauthorized page' }
    ];
    
    for (const route of routes) {
      try {
        const response = await fetch(`${config.frontendUrl}${route.path}`, {
          method: 'GET',
          redirect: 'manual'
        });
        
        // Public routes should return 200 (or 404 if page doesn't exist yet)
        const success = response.status === 200 || response.status === 404;
        await this.recordTest(`Public Route: ${route.path}`, success, 
          `${route.description} - Status: ${response.status}`);
      } catch (error) {
        await this.recordTest(`Public Route: ${route.path}`, false, error.message);
      }
    }
  }

  async testProtectedRoutes() {
    this.logInfo('Testing protected routes...');
    
    const routes = [
      { path: '/dashboard', description: 'Main dashboard', expectedRedirect: true },
      { path: '/dashboard/profile', description: 'User profile', expectedRedirect: true },
      { path: '/integrations', description: 'Integrations page', expectedRedirect: true },
      { path: '/admin', description: 'Admin dashboard', expectedRedirect: true },
      { path: '/admin/users', description: 'Admin user management', expectedRedirect: true },
      { path: '/manage', description: 'Manager dashboard', expectedRedirect: true },
      { path: '/tenant/test-tenant', description: 'Tenant-scoped page', expectedRedirect: true },
      { path: '/project/test-project', description: 'Project page', expectedRedirect: true },
      { path: '/users/test-user', description: 'User profile page', expectedRedirect: true }
    ];
    
    for (const route of routes) {
      try {
        const response = await fetch(`${config.frontendUrl}${route.path}`, {
          method: 'GET',
          redirect: 'manual'
        });
        
        const isRedirect = response.status >= 300 && response.status < 400;
        const success = route.expectedRedirect ? isRedirect : response.status === 200;
        
        await this.recordTest(`Protected Route: ${route.path}`, success, 
          `${route.description} - Status: ${response.status} (${isRedirect ? 'Redirected' : 'Direct'})`);
      } catch (error) {
        await this.recordTest(`Protected Route: ${route.path}`, false, error.message);
      }
    }
  }

  async testTenantIsolation() {
    this.logInfo('Step 5: Testing tenant isolation with dynamic routes...');

    const tenantTests = [
      {
        route: '/tenant/example-tenant',
        description: 'Basic tenant access',
        shouldPass: true // Authenticated users can access their tenant
      },
      {
        route: '/tenant/example-tenant/settings',
        description: 'Tenant settings (manager+)',
        shouldPass: this.currentUserRoles.includes('admin') || this.currentUserRoles.includes('manager')
      },
      {
        route: '/tenant/other-tenant',
        description: 'Cross-tenant access',
        shouldPass: this.currentUserRoles.includes('admin') // Only admins can cross tenants
      },
      {
        route: '/tenant/forbidden-tenant',
        description: 'Unauthorized tenant access',
        shouldPass: this.currentUserRoles.includes('admin') // Only admins should access
      }
    ];

    for (const test of tenantTests) {
      try {
        const response = await this.makeRequest(`${config.frontendUrl}${test.route}`);
        
        let success;
        let details;
        
        if (test.shouldPass) {
          success = response.status === 200;
          details = `Tenant access granted (Status: ${response.status})`;
        } else {
          success = (response.status >= 300 && response.status < 400) || response.status === 403;
          details = `Tenant access blocked (Status: ${response.status})`;
        }
        
        await this.recordTest(`Tenant Route: ${test.route}`, success, 
          `${test.description} - ${details}`);
          
      } catch (error) {
        await this.recordTest(`Tenant Route: ${test.route}`, false, error.message);
      }
    }
  }

  async testResourceOwnership() {
    this.logInfo('Step 6: Testing resource ownership validation...');

    const resourceTests = [
      {
        route: '/project/example-project',
        description: 'Project resource access',
        shouldPass: true // Basic authenticated access
      },
      {
        route: '/users/user123',
        description: 'User profile access',
        shouldPass: this.currentUserRoles.includes('admin') || this.currentUserRoles.includes('manager')
      }
    ];

    for (const test of resourceTests) {
      try {
        const response = await this.makeRequest(`${config.frontendUrl}${test.route}`);
        
        let success;
        let details;
        
        if (test.shouldPass) {
          success = response.status === 200;
          details = `Resource access granted (Status: ${response.status})`;
        } else {
          success = (response.status >= 300 && response.status < 400) || response.status === 403;
          details = `Resource access blocked (Status: ${response.status})`;
        }
        
        await this.recordTest(`Resource Route: ${test.route}`, success, 
          `${test.description} - ${details}`);
          
      } catch (error) {
        await this.recordTest(`Resource Route: ${test.route}`, false, error.message);
      }
    }
  }

  async testMiddlewarePerformance() {
    this.logInfo('Step 7: Testing middleware performance...');

    const performanceTests = [
      '/dashboard', // Cookie validation (fast)
      '/admin',     // GraphQL validation (slower)
      '/tenant/example-tenant' // Dynamic route processing
    ];

    for (const route of performanceTests) {
      try {
        const startTime = Date.now();
        const response = await this.makeRequest(`${config.frontendUrl}${route}`);
        const duration = Date.now() - startTime;
        
        // Performance thresholds
        const isGraphQLRoute = route.includes('admin') || route.includes('tenant');
        const threshold = isGraphQLRoute ? 100 : 50; // ms
        
        const success = duration < threshold;
        await this.recordTest(`Middleware Performance: ${route}`, success, 
          `${duration}ms (threshold: ${threshold}ms)`);
          
      } catch (error) {
        await this.recordTest(`Middleware Performance: ${route}`, false, error.message);
      }
    }
  }

  async testSecurityHeaders() {
    this.logInfo('Step 8: Testing security headers and debug information...');

    try {
      const response = await this.makeRequest(`${config.frontendUrl}/admin`);
      
      // Check for custom security headers added by middleware
      const expectedHeaders = [
        'x-auth-method',
        'x-route-description'
      ];
      
      let foundHeaders = 0;
      for (const header of expectedHeaders) {
        if (response.headers[header]) {
          foundHeaders++;
          this.logInfo(`Found security header: ${header} = ${response.headers[header]}`);
        }
      }
      
      const success = foundHeaders >= expectedHeaders.length / 2; // At least half
      await this.recordTest('Security Headers', success, 
        `Found ${foundHeaders}/${expectedHeaders.length} expected headers`);
        
      // Check for auth context headers (if authenticated)
      if (this.authCookies && response.headers['x-auth-user-roles']) {
        await this.recordTest('Auth Context Headers', true, 
          `User roles: ${response.headers['x-auth-user-roles']}`);
      }
        
    } catch (error) {
      await this.recordTest('Security Headers', false, error.message);
    }
  }

  async testUnauthorizedPages() {
    this.logInfo('Step 9: Testing custom unauthorized pages...');

    const unauthorizedTests = [
      {
        route: '/unauthorized',
        description: 'Generic unauthorized page'
      },
      {
        route: '/unauthorized/admin',
        description: 'Admin-specific unauthorized page'
      },
      {
        route: '/unauthorized/manager',
        description: 'Manager-specific unauthorized page'
      }
    ];

    for (const test of unauthorizedTests) {
      try {
        const response = await this.makeRequest(`${config.frontendUrl}${test.route}`);
        
        const success = response.status === 200;
        await this.recordTest(`Unauthorized Page: ${test.route}`, success, 
          `${test.description} (Status: ${response.status})`);
          
      } catch (error) {
        await this.recordTest(`Unauthorized Page: ${test.route}`, false, error.message);
      }
    }
  }

  async generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    this.log('\n' + '='.repeat(60), colors.bright);
    this.log('🏆 ENTERPRISE RBAC TEST RESULTS', colors.bright);
    this.log('='.repeat(60), colors.bright);
    
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(t => t.success).length;
    const failedTests = totalTests - passedTests;
    const successRate = totalTests > 0 ? (passedTests / totalTests * 100).toFixed(1) : 0;
    
    this.log(`\n📊 SUMMARY`, colors.cyan);
    this.log(`Total Tests: ${totalTests}`, colors.white);
    this.log(`Passed: ${passedTests}`, colors.green);
    this.log(`Failed: ${failedTests}`, failedTests > 0 ? colors.red : colors.green);
    this.log(`Success Rate: ${successRate}%`, successRate >= 90 ? colors.green : colors.yellow);
    this.log(`Duration: ${(duration / 1000).toFixed(2)} seconds`, colors.white);
    
    this.log(`\n📋 DETAILED RESULTS`, colors.cyan);
    for (const test of this.testResults) {
      const status = test.success ? '✅' : '❌';
      this.log(`  ${status} ${test.test}: ${test.details}`, colors.white);
    }
    
    this.log('\n' + '='.repeat(60), colors.bright);
    
    return successRate >= 90;
  }

  async run() {
    this.log('🚀 Starting Enterprise RBAC Middleware Test Suite', colors.bright);
    this.log(`Frontend URL: ${config.frontendUrl}`, colors.cyan);
    this.log(`BFF URL: ${config.bffUrl}`, colors.cyan);
    
    try {
      // Quick middleware test
      await this.testPublicRoutes();
      await this.testProtectedRoutes();
      
      // Generate report
      await this.generateReport();
      
    } catch (error) {
      this.logError(`Test suite failed: ${error.message}`);
      console.error(error);
      process.exit(1);
    }
  }
}

// Run the test suite
if (require.main === module) {
  const tester = new EnterpriseRBACTester();
  tester.run().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
} 