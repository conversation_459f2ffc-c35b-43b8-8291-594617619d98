import { useApollo<PERSON>lient } from "@apollo/client";
import type { ApolloClient } from "@apollo/client";

// Simplified hooks that work with generated types
// The generated hooks from codegen are the primary way to use GraphQL operations

export function useAppApolloClient(): ApolloClient<any> {
  return useApolloClient();
}

export function useApolloNetworkStatus() {
  const client = useAppApolloClient();

  return {
    refetch: () => client.refetchQueries({ include: "active" }),
    resetStore: () => client.resetStore(),
    clearStore: () => client.clearStore(),
  };
}

export function useApolloCache() {
  const client = useAppApolloClient();

  return {
    cache: client.cache,
    readQuery: client.readQuery.bind(client),
    writeQuery: client.writeQuery.bind(client),
    readFragment: client.readFragment.bind(client),
    writeFragment: client.writeFragment.bind(client),
    evict: client.cache.evict.bind(client.cache),
    gc: client.cache.gc.bind(client.cache),
    reset: () => client.cache.reset(),
  };
}
