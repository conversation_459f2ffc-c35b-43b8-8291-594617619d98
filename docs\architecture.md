# PulsePanel - Архитектура и технологический стек

## Обзор проекта

PulsePanel представляет собой современный монорепозиторий, построенный с использованием передовых технологий для создания масштабируемого веб-приложения. Проект следует архитектуре **Backend for Frontend (BFF)** с четким разделением ответственности между компонентами.

## Управление монорепозиторием

- **Менеджер пакетов:** pnpm `10.10.0`
- **Система сборки:** Turbo `^2.5.2` для эффективного управления задачами в монорепозитории
- **Рабочие области:** 7 пакетов с автоматическим управлением зависимостями

## Технологический стек

### Язык программирования

- **TypeScript:** `^5.4.5` (унифицировано во всех модулях)
  - Строгая типизация
  - Современные ES модули (`"type": "module"`)
  - Общие конфигурации через `@pulsepanel/tsconfig-custom`

### Инструменты качества кода

- **ESLint:** `^9.0.0` (унифицировано)
- **TypeScript ESLint:** `^8.0.0` (плагин и парсер)
- **Prettier:** `^3.3.0`
- **Husky:** `^9.0.11` для Git hooks
- **lint-staged:** `^16.0.0` для проверок перед коммитом

### Frontend (`@pulsepanel/frontend@0.1.0`)

- **Framework:** Next.js `15.3.1` с Turbopack для быстрой разработки
- **UI Library:** React `^19.0.0` + React DOM `^19.0.0`
- **GraphQL Client:** Apollo Client (через `@graphql-codegen/typescript-react-apollo@^4.3.3`)
- **Генерация кода:** GraphQL Code Generator `^5.0.6`
- **Линтинг:** ESLint с Next.js конфигурацией и React Hooks правилами

#### Управление состоянием

- **State Manager:** Redux Toolkit `^2.0` с современной архитектурой
- **React Bindings:** React-Redux `^9.0` с hooks API
- **Middleware:** RTK Thunk для асинхронных операций
- **DevTools:** Redux DevTools с time-travel debugging
- **Селекторы:** Reselect `^5.0` с улучшенной мемоизацией

**Архитектура состояния:**

- `configureStore` для настройки хранилища с автоматическими middleware
- `createSlice` для объединения reducer'ов и action creator'ов
- `createAsyncThunk` для работы с асинхронными операциями
- `createSelector` для эффективных селекторов с мемоизацией
- Поддержка lazy-loading слайсов с `combineSlices`
- SSR-совместимая гидратация состояния для Next.js

**Интеграция с GraphQL:**

- Apollo Client Cache для server state
- Redux Store для client state и UI состояния
- Автоматическая синхронизация между источниками данных

### Backend (`@pulsepanel/bff@0.1.0`)

- **Runtime:** Node.js с поддержкой современных ES модулей
- **Framework:** Express.js `^4.19.2`
- **GraphQL Server:** Apollo Server `^4.10.4`
- **Schema Builder:** TypeGraphQL `2.0.0-rc.2`
- **ORM Integration:** typegraphql-prisma `^0.28.0`
- **Cache/Sessions:** ioredis `^5.6.1` (с встроенными TypeScript типами)
- **Development:** tsx `^4.7.0` для hot reload с `--watch`
- **CORS:** cors `^2.8.5`
- **Environment:** dotenv `^16.4.5`

### База данных (`@pulsepanel/db@0.1.0`)

- **ORM:** Prisma `^5.22.0`
- **Client:** @prisma/client `^5.22.0`
- **Schema Management:** Миграции, генерация клиента, Prisma Studio
- **GraphQL Integration:** TypeGraphQL `2.0.0-rc.2` + typegraphql-prisma `^0.28.0`
- **GraphQL:** `^16.11.0` (унифицированная версия)

### Система событий (`@pulsepanel/events@0.1.0`)

- **Message Broker:** RabbitMQ через amqplib `^0.10.8`
- **Testing:** Jest `^29.7.0` с ts-jest `^29.1.4`
- **Паттерны:** Event-driven архитектура для асинхронной обработки

### Общий SDK (`@pulsepanel/core-sdk@0.1.0`)

- **Назначение:** Общие типы, интерфейсы, утилиты
- **Зависимости:** Минимальные - только TypeScript для типизации
- **Экспорт:** ES модули с поддержкой Tree Shaking

## Архитектура

```
┌─── apps/
│    ├─── frontend/          # Next.js клиентское приложение
│    └─── bff/              # GraphQL Backend for Frontend
│
├─── packages/
│    ├─── db/               # Prisma схема и клиент
│    ├─── events/           # RabbitMQ интеграция
│    └─── core-sdk/         # Общие утилиты и типы
│
└─── tools/
     ├─── eslint-config-custom/
     ├─── jest-config/
     └─── tsconfig-custom/
```

### Потоки данных

1. **Frontend → BFF:** GraphQL запросы через Apollo Client
2. **BFF → Database:** Prisma ORM для работы с данными
3. **BFF → Events:** RabbitMQ для асинхронных задач
4. **Cross-Package:** Shared types через core-sdk
5. **State Management:** Redux Toolkit 2 для client state + Apollo Cache для server state
6. **SSR Hydration:** Безопасная гидратация Redux store при серверном рендеринге

## Процессы разработки

### Генерация кода

- **GraphQL Types:** Автоматическая генерация TypeScript типов из GraphQL схем
- **Prisma Client:** Генерация типизированного клиента из схемы БД
- **Hot Reload:** Автоматическая регенерация при изменениях

### Скрипты разработки

```bash
pnpm dev              # Запуск всех приложений в режиме разработки
pnpm build            # Сборка всех пакетов
pnpm lint             # Линтинг всего проекта
pnpm test             # Запуск тестов
pnpm codegen          # Генерация GraphQL/Prisma кода
```

### Управление сервисами

```bash
pnpm services:up      # Запуск Docker Compose сервисов
pnpm services:down    # Остановка сервисов
pnpm events:up        # Запуск только RabbitMQ
```

## Унификация зависимостей

### ✅ Унифицированные версии

- **TypeScript:** `^5.4.5` во всех модулях
- **@types/node:** `^20.11.20` во всех модулях
- **ESLint:** `^9.0.0` во всех модулях
- **@typescript-eslint плагины:** `^8.0.0` во всех модулях
- **GraphQL:** `^16.11.0` в bff и db
- **Redux Toolkit:** `^2.0` с React-Redux `^9.0`

### Преимущества унификации

- 🔧 **Совместимость:** Устранены потенциальные конфликты версий
- 📦 **Дедупликация:** Эффективное использование pnpm workspace
- 🚀 **Производительность:** Согласованное поведение инструментов
- 🛠️ **Разработка:** Упрощенное обновление и отладка

## Качество и безопасность

### Статический анализ

- Строгие правила ESLint для TypeScript
- Автоматическое форматирование с Prettier
- Pre-commit hooks для проверки качества кода

### Тестирование

- Unit тесты с Jest
- Специальные тесты для RabbitMQ интеграции
- Автоматическая генерация типов для type safety

### Безопасность

- Отсутствие критических уязвимостей (проверено pnpm audit)
- Современные версии всех зависимостей
- Удаление deprecated пакетов (@types/ioredis)

## Инфраструктура разработки

### Docker Services

- **RabbitMQ:** Брокер сообщений для event-driven архитектуры
- **База данных:** (конфигурация в docker-compose.dev.yml)

### CI/CD готовность

- GitHub Actions workflows (`.github/workflows/`)
- Turbo для кэширования сборки
- Готовность к контейнеризации

---

**Статус проекта:** ✅ Активная разработка  
**Последнее обновление:** Январь 2025  
**Версии унифицированы:** ✅ Все зависимости приведены в соответствие
